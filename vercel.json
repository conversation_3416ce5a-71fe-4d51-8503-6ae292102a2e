{"version": 2, "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}, {"src": "backend/src/server.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/health", "dest": "/api/health"}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "dest": "/frontend/dist/$1"}, {"src": "/landing.html", "dest": "/frontend/dist/landing.html"}, {"src": "/index.html", "dest": "/frontend/dist/index.html"}, {"src": "/app", "dest": "/frontend/dist/index.html"}, {"src": "/n8n", "dest": "/frontend/dist/index.html"}, {"src": "/", "dest": "/frontend/dist/landing.html"}, {"src": "/(.*)", "dest": "/frontend/dist/index.html"}], "env": {"NODE_ENV": "production"}, "installCommand": "npm run install:all", "buildCommand": "npm run build", "devCommand": "npm run dev"}