function loco(){
  try {
    // Check if required libraries are loaded
    if (typeof gsap === 'undefined') {
      console.error('GSAP library not loaded');
      return;
    }
    if (typeof LocomotiveScroll === 'undefined') {
      console.error('Locomotive Scroll library not loaded');
      return;
    }
    
    gsap.registerPlugin(ScrollTrigger);

    // Using Locomotive Scroll from Locomotive https://github.com/locomotivemtl/locomotive-scroll
    const mainElement = document.querySelector("#main");
    if (!mainElement) {
      console.error('Main element not found');
      return;
    }

    const locoScroll = new LocomotiveScroll({
      el: mainElement,
      smooth: true
    });
    
    // each time Locomotive Scroll updates, tell ScrollTrigger to update too (sync positioning)
    locoScroll.on("scroll", ScrollTrigger.update);

    // tell ScrollTrigger to use these proxy methods for the "#main" element since Locomotive Scroll is hijacking things
    ScrollTrigger.scrollerProxy("#main", {
      scrollTop(value) {
        return arguments.length ? locoScroll.scrollTo(value, 0, 0) : locoScroll.scroll.instance.scroll.y;
      }, // we don't have to define a scrollLeft because we're only scrolling vertically.
      getBoundingClientRect() {
        return {top: 0, left: 0, width: window.innerWidth, height: window.innerHeight};
      },
      // LocomotiveScroll handles things completely differently on mobile devices - it doesn't even transform the container at all! So to get the correct behavior and avoid jitters, we should pin things with position: fixed on mobile. We sense it by checking to see if there's a transform applied to the container (the LocomotiveScroll-controlled element).
      pinType: document.querySelector("#main").style.transform ? "transform" : "fixed"
    });

    // each time the window updates, we should refresh ScrollTrigger and then update LocomotiveScroll. 
    ScrollTrigger.addEventListener("refresh", () => locoScroll.update());

    // after everything is set up, refresh() ScrollTrigger and update LocomotiveScroll because padding may have been added for pinning, etc.
    ScrollTrigger.refresh();
    
    console.log('Locomotive Scroll initialized successfully');
  } catch (error) {
    console.error('Error initializing Locomotive Scroll:', error);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', loco);
} else {
  loco();
}


// Text animation for page2
try {
  var clutter = "";
  const page2H1 = document.querySelector("#page2>h1");
  
  if (page2H1 && page2H1.textContent) {
    page2H1.textContent.split("").forEach(function(dets){
      clutter += `<span>${dets}</span>`
    });
    page2H1.innerHTML = clutter;

    if (typeof gsap !== 'undefined') {
      gsap.to("#page2>h1>span",{
        scrollTrigger:{
            trigger:`#page2>h1>span`,
            start:`top bottom`,
            end:`bottom top`,
            scroller:`#main`,
            scrub:.5,
        },
        stagger:.2,
        color:`#fff`
      });
    }
  }
} catch (error) {
  console.error('Error setting up page2 text animation:', error);
}



function canvas(){
  try {
    const canvas = document.querySelector("#page3>canvas");
    if (!canvas) {
      console.error('Canvas element not found');
      return;
    }
    
    const context = canvas.getContext("2d");
    if (!context) {
      console.error('Canvas context not available');
      return;
    }

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;


window.addEventListener("resize", function () {
canvas.width = window.innerWidth;
canvas.height = window.innerHeight;
render();
});

function files(index) {
var data = `
/frames00007.png
/frames00010.png
/frames00013.png
/frames00016.png
/frames00019.png
/frames00022.png
/frames00025.png
/frames00028.png
/frames00031.png
/frames00034.png
/frames00037.png
/frames00040.png
/frames00043.png
/frames00046.png
/frames00049.png
/frames00052.png
/frames00055.png
/frames00058.png
/frames00061.png
/frames00064.png
/frames00067.png
/frames00070.png
/frames00073.png
/frames00076.png
/frames00079.png
/frames00082.png
/frames00085.png
/frames00088.png
/frames00091.png
/frames00094.png
/frames00097.png
/frames00100.png
/frames00103.png
/frames00106.png
/frames00109.png
/frames00112.png
/frames00115.png
/frames00118.png
/frames00121.png
/frames00124.png
/frames00127.png
/frames00130.png
/frames00133.png
/frames00136.png
/frames00139.png
/frames00142.png
/frames00145.png
/frames00148.png
/frames00151.png
/frames00154.png
/frames00157.png
/frames00160.png
/frames00163.png
/frames00166.png
/frames00169.png
/frames00172.png
/frames00175.png
/frames00178.png
/frames00181.png
/frames00184.png
/frames00187.png
/frames00190.png
/frames00193.png
/frames00196.png
/frames00199.png
/frames00202.png
`;
return data.split("\n")[index];
}

const frameCount = 67;

const images = [];
const imageSeq = {
frame: 1,
};

for (let i = 0; i < frameCount; i++) {
const img = new Image();
img.src = files(i);
images.push(img);
}

gsap.to(imageSeq, {
frame: frameCount - 1,
snap: "frame",
ease: `none`,
scrollTrigger: {
  scrub: .5,
  trigger: `#page3`,
  start: `top top`,
  end: `250% top`,
  scroller: `#main`,
},
onUpdate: render,
});

images[1].onload = render;

function render() {
scaleImage(images[imageSeq.frame], context);
}

function scaleImage(img, ctx) {
var canvas = ctx.canvas;
var hRatio = canvas.width / img.width;
var vRatio = canvas.height / img.height;
var ratio = Math.max(hRatio, vRatio);
var centerShift_x = (canvas.width - img.width * ratio) / 2;
var centerShift_y = (canvas.height - img.height * ratio) / 2;
ctx.clearRect(0, 0, canvas.width, canvas.height);
ctx.drawImage(
  img,
  0,
  0,
  img.width,
  img.height,
  centerShift_x,
  centerShift_y,
  img.width * ratio,
  img.height * ratio
);
}
    if (typeof ScrollTrigger !== 'undefined') {
      ScrollTrigger.create({
        trigger: "#page3",
        pin: true,
        scroller: `#main`,
        start: `top top`,
        end: `250% top`,
      });
    }
    
    console.log('Canvas initialized successfully');
  } catch (error) {
    console.error('Error initializing canvas:', error);
  }
}

// Initialize canvas when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', canvas);
} else {
  canvas();
}






// Text animation for page4
try {
  var clutter = "";
  const page4H1 = document.querySelector("#page4>h1");
  
  if (page4H1 && page4H1.textContent) {
    page4H1.textContent.split("").forEach(function(dets){
      clutter += `<span>${dets}</span>`
    });
    page4H1.innerHTML = clutter;

    if (typeof gsap !== 'undefined') {
      gsap.to("#page4>h1>span",{
        scrollTrigger:{
            trigger:`#page4>h1>span`,
            start:`top bottom`,
            end:`bottom top`,
            scroller:`#main`,
            scrub:.5,
        },
        stagger:.2,
        color:`#fff`
      });
    }
  }
} catch (error) {
  console.error('Error setting up page4 text animation:', error);
}







function canvas1(){
  try {
    const canvas = document.querySelector("#page5>canvas");
    if (!canvas) {
      console.error('Canvas1 element not found');
      return;
    }
    
    const context = canvas.getContext("2d");
    if (!context) {
      console.error('Canvas1 context not available');
      return;
    }

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;


window.addEventListener("resize", function () {
canvas.width = window.innerWidth;
canvas.height = window.innerHeight;
render();
});

function files(index) {
var data = `
/bridges00004.png
/bridges00007.png
/bridges00010.png
/bridges00013.png
/bridges00016.png
/bridges00019.png
/bridges00022.png
/bridges00025.png
/bridges00028.png
/bridges00031.png
/bridges00034.png
/bridges00037.png
/bridges00040.png
/bridges00043.png
/bridges00046.png
/bridges00049.png
/bridges00052.png
/bridges00055.png
/bridges00058.png
/bridges00061.png
/bridges00064.png
/bridges00067.png
/bridges00070.png
/bridges00073.png
/bridges00076.png
/bridges00079.png
/bridges00082.png
/bridges00085.png
/bridges00088.png
/bridges00091.png
/bridges00094.png
/bridges00097.png
/bridges00100.png
/bridges00103.png
/bridges00106.png
/bridges00109.png
/bridges00112.png
/bridges00115.png
/bridges00118.png
/bridges00121.png
/bridges00124.png
/bridges00127.png
/bridges00130.png
/bridges00133.png
/bridges00136.png
/bridges00139.png
/bridges00142.png
/bridges00145.png
/bridges00148.png
/bridges00151.png
/bridges00154.png
/bridges00157.png
/bridges00160.png
/bridges00163.png
/bridges00166.png
/bridges00169.png
/bridges00172.png
/bridges00175.png
/bridges00178.png
/bridges00181.png
/bridges00184.png
/bridges00187.png
/bridges00190.png
/bridges00193.png
/bridges00196.png
/bridges00199.png
/bridges00202.png
`;
return data.split("\n")[index];
}

const frameCount = 67;

const images = [];
const imageSeq = {
frame: 1,
};

for (let i = 0; i < frameCount; i++) {
const img = new Image();
img.src = files(i);
images.push(img);
}

gsap.to(imageSeq, {
frame: frameCount - 1,
snap: "frame",
ease: `none`,
scrollTrigger: {
scrub: .5,
trigger: `#page5`,
start: `top top`,
end: `250% top`,
scroller: `#main`,
},
onUpdate: render,
});

images[1].onload = render;

function render() {
scaleImage(images[imageSeq.frame], context);
}

function scaleImage(img, ctx) {
var canvas = ctx.canvas;
var hRatio = canvas.width / img.width;
var vRatio = canvas.height / img.height;
var ratio = Math.max(hRatio, vRatio);
var centerShift_x = (canvas.width - img.width * ratio) / 2;
var centerShift_y = (canvas.height - img.height * ratio) / 2;
ctx.clearRect(0, 0, canvas.width, canvas.height);
ctx.drawImage(
img,
0,
0,
img.width,
img.height,
centerShift_x,
centerShift_y,
img.width * ratio,
img.height * ratio
);
}
    if (typeof ScrollTrigger !== 'undefined') {
      ScrollTrigger.create({
        trigger: "#page5",
        pin: true,
        scroller: `#main`,
        start: `top top`,
        end: `250% top`,
      });
    }
    
    console.log('Canvas1 initialized successfully');
  } catch (error) {
    console.error('Error initializing canvas1:', error);
  }
}

// Initialize canvas1 when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', canvas1);
} else {
  canvas1();
}




// Text animation for page6
try {
  var clutter = "";
  const page6H1 = document.querySelector("#page6>h1");
  
  if (page6H1 && page6H1.textContent) {
    page6H1.textContent.split("").forEach(function(dets){
      clutter += `<span>${dets}</span>`
    });
    page6H1.innerHTML = clutter;

    if (typeof gsap !== 'undefined') {
      gsap.to("#page6>h1>span",{
        scrollTrigger:{
            trigger:`#page6>h1>span`,
            start:`top bottom`,
            end:`bottom top`,
            scroller:`#main`,
            scrub:.5,
        },
        stagger:.2,
        color:`#fff`
      });
    }
  }
} catch (error) {
  console.error('Error setting up page6 text animation:', error);
}






function canvas2(){
  try {
    const canvas = document.querySelector("#page7>canvas");
    if (!canvas) {
      console.error('Canvas2 element not found');
      return;
    }
    
    const context = canvas.getContext("2d");
    if (!context) {
      console.error('Canvas2 context not available');
      return;
    }

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;


window.addEventListener("resize", function () {
canvas.width = window.innerWidth;
canvas.height = window.innerHeight;
render();
});

function files(index) {
var data = `

https://thisismagma.com/assets/home/<USER>/seq/1.webp?2
https://thisismagma.com/assets/home/<USER>/seq/2.webp?2
https://thisismagma.com/assets/home/<USER>/seq/3.webp?2
https://thisismagma.com/assets/home/<USER>/seq/4.webp?2
https://thisismagma.com/assets/home/<USER>/seq/5.webp?2
https://thisismagma.com/assets/home/<USER>/seq/6.webp?2
https://thisismagma.com/assets/home/<USER>/seq/7.webp?2
https://thisismagma.com/assets/home/<USER>/seq/8.webp?2
https://thisismagma.com/assets/home/<USER>/seq/9.webp?2
https://thisismagma.com/assets/home/<USER>/seq/10.webp?2
https://thisismagma.com/assets/home/<USER>/seq/11.webp?2
https://thisismagma.com/assets/home/<USER>/seq/12.webp?2
https://thisismagma.com/assets/home/<USER>/seq/13.webp?2
https://thisismagma.com/assets/home/<USER>/seq/14.webp?2
https://thisismagma.com/assets/home/<USER>/seq/15.webp?2
https://thisismagma.com/assets/home/<USER>/seq/16.webp?2
https://thisismagma.com/assets/home/<USER>/seq/17.webp?2
https://thisismagma.com/assets/home/<USER>/seq/18.webp?2
https://thisismagma.com/assets/home/<USER>/seq/19.webp?2
https://thisismagma.com/assets/home/<USER>/seq/20.webp?2
https://thisismagma.com/assets/home/<USER>/seq/21.webp?2
https://thisismagma.com/assets/home/<USER>/seq/22.webp?2
https://thisismagma.com/assets/home/<USER>/seq/23.webp?2
https://thisismagma.com/assets/home/<USER>/seq/24.webp?2
https://thisismagma.com/assets/home/<USER>/seq/25.webp?2
https://thisismagma.com/assets/home/<USER>/seq/26.webp?2
https://thisismagma.com/assets/home/<USER>/seq/27.webp?2
https://thisismagma.com/assets/home/<USER>/seq/28.webp?2
https://thisismagma.com/assets/home/<USER>/seq/29.webp?2
https://thisismagma.com/assets/home/<USER>/seq/30.webp?2
https://thisismagma.com/assets/home/<USER>/seq/31.webp?2
https://thisismagma.com/assets/home/<USER>/seq/32.webp?2
https://thisismagma.com/assets/home/<USER>/seq/33.webp?2
https://thisismagma.com/assets/home/<USER>/seq/34.webp?2
https://thisismagma.com/assets/home/<USER>/seq/35.webp?2
https://thisismagma.com/assets/home/<USER>/seq/36.webp?2
https://thisismagma.com/assets/home/<USER>/seq/37.webp?2
https://thisismagma.com/assets/home/<USER>/seq/38.webp?2
https://thisismagma.com/assets/home/<USER>/seq/39.webp?2
https://thisismagma.com/assets/home/<USER>/seq/40.webp?2
https://thisismagma.com/assets/home/<USER>/seq/41.webp?2
https://thisismagma.com/assets/home/<USER>/seq/42.webp?2
https://thisismagma.com/assets/home/<USER>/seq/43.webp?2
https://thisismagma.com/assets/home/<USER>/seq/44.webp?2
https://thisismagma.com/assets/home/<USER>/seq/45.webp?2
https://thisismagma.com/assets/home/<USER>/seq/46.webp?2
https://thisismagma.com/assets/home/<USER>/seq/47.webp?2
https://thisismagma.com/assets/home/<USER>/seq/48.webp?2
https://thisismagma.com/assets/home/<USER>/seq/49.webp?2
https://thisismagma.com/assets/home/<USER>/seq/50.webp?2
https://thisismagma.com/assets/home/<USER>/seq/51.webp?2
https://thisismagma.com/assets/home/<USER>/seq/52.webp?2
https://thisismagma.com/assets/home/<USER>/seq/53.webp?2
https://thisismagma.com/assets/home/<USER>/seq/54.webp?2
https://thisismagma.com/assets/home/<USER>/seq/55.webp?2
https://thisismagma.com/assets/home/<USER>/seq/56.webp?2
https://thisismagma.com/assets/home/<USER>/seq/57.webp?2
https://thisismagma.com/assets/home/<USER>/seq/58.webp?2
https://thisismagma.com/assets/home/<USER>/seq/59.webp?2
https://thisismagma.com/assets/home/<USER>/seq/60.webp?2
https://thisismagma.com/assets/home/<USER>/seq/61.webp?2
https://thisismagma.com/assets/home/<USER>/seq/62.webp?2
https://thisismagma.com/assets/home/<USER>/seq/63.webp?2
https://thisismagma.com/assets/home/<USER>/seq/64.webp?2
https://thisismagma.com/assets/home/<USER>/seq/65.webp?2
https://thisismagma.com/assets/home/<USER>/seq/66.webp?2
https://thisismagma.com/assets/home/<USER>/seq/67.webp?2
https://thisismagma.com/assets/home/<USER>/seq/68.webp?2
https://thisismagma.com/assets/home/<USER>/seq/69.webp?2
https://thisismagma.com/assets/home/<USER>/seq/70.webp?2
https://thisismagma.com/assets/home/<USER>/seq/71.webp?2
https://thisismagma.com/assets/home/<USER>/seq/72.webp?2
https://thisismagma.com/assets/home/<USER>/seq/73.webp?2
https://thisismagma.com/assets/home/<USER>/seq/74.webp?2
https://thisismagma.com/assets/home/<USER>/seq/75.webp?2
https://thisismagma.com/assets/home/<USER>/seq/76.webp?2
https://thisismagma.com/assets/home/<USER>/seq/77.webp?2
https://thisismagma.com/assets/home/<USER>/seq/78.webp?2
https://thisismagma.com/assets/home/<USER>/seq/79.webp?2
https://thisismagma.com/assets/home/<USER>/seq/80.webp?2
https://thisismagma.com/assets/home/<USER>/seq/81.webp?2
https://thisismagma.com/assets/home/<USER>/seq/82.webp?2
https://thisismagma.com/assets/home/<USER>/seq/83.webp?2
https://thisismagma.com/assets/home/<USER>/seq/84.webp?2
https://thisismagma.com/assets/home/<USER>/seq/85.webp?2
https://thisismagma.com/assets/home/<USER>/seq/86.webp?2
https://thisismagma.com/assets/home/<USER>/seq/87.webp?2
https://thisismagma.com/assets/home/<USER>/seq/88.webp?2
https://thisismagma.com/assets/home/<USER>/seq/89.webp?2
https://thisismagma.com/assets/home/<USER>/seq/90.webp?2
https://thisismagma.com/assets/home/<USER>/seq/91.webp?2
https://thisismagma.com/assets/home/<USER>/seq/92.webp?2
https://thisismagma.com/assets/home/<USER>/seq/93.webp?2
https://thisismagma.com/assets/home/<USER>/seq/94.webp?2
https://thisismagma.com/assets/home/<USER>/seq/95.webp?2
https://thisismagma.com/assets/home/<USER>/seq/96.webp?2
https://thisismagma.com/assets/home/<USER>/seq/97.webp?2
https://thisismagma.com/assets/home/<USER>/seq/98.webp?2
https://thisismagma.com/assets/home/<USER>/seq/99.webp?2
https://thisismagma.com/assets/home/<USER>/seq/100.webp?2
https://thisismagma.com/assets/home/<USER>/seq/101.webp?2
https://thisismagma.com/assets/home/<USER>/seq/102.webp?2
https://thisismagma.com/assets/home/<USER>/seq/103.webp?2
https://thisismagma.com/assets/home/<USER>/seq/104.webp?2
https://thisismagma.com/assets/home/<USER>/seq/105.webp?2
https://thisismagma.com/assets/home/<USER>/seq/106.webp?2
https://thisismagma.com/assets/home/<USER>/seq/107.webp?2
https://thisismagma.com/assets/home/<USER>/seq/108.webp?2
https://thisismagma.com/assets/home/<USER>/seq/109.webp?2
https://thisismagma.com/assets/home/<USER>/seq/110.webp?2
https://thisismagma.com/assets/home/<USER>/seq/111.webp?2
https://thisismagma.com/assets/home/<USER>/seq/112.webp?2
https://thisismagma.com/assets/home/<USER>/seq/113.webp?2
https://thisismagma.com/assets/home/<USER>/seq/114.webp?2
https://thisismagma.com/assets/home/<USER>/seq/115.webp?2
https://thisismagma.com/assets/home/<USER>/seq/116.webp?2
https://thisismagma.com/assets/home/<USER>/seq/117.webp?2
https://thisismagma.com/assets/home/<USER>/seq/118.webp?2
https://thisismagma.com/assets/home/<USER>/seq/119.webp?2
https://thisismagma.com/assets/home/<USER>/seq/120.webp?2
https://thisismagma.com/assets/home/<USER>/seq/121.webp?2
https://thisismagma.com/assets/home/<USER>/seq/122.webp?2
https://thisismagma.com/assets/home/<USER>/seq/123.webp?2
https://thisismagma.com/assets/home/<USER>/seq/124.webp?2
https://thisismagma.com/assets/home/<USER>/seq/125.webp?2
https://thisismagma.com/assets/home/<USER>/seq/126.webp?2
https://thisismagma.com/assets/home/<USER>/seq/127.webp?2
https://thisismagma.com/assets/home/<USER>/seq/128.webp?2
https://thisismagma.com/assets/home/<USER>/seq/129.webp?2
https://thisismagma.com/assets/home/<USER>/seq/130.webp?2
https://thisismagma.com/assets/home/<USER>/seq/131.webp?2
https://thisismagma.com/assets/home/<USER>/seq/132.webp?2
https://thisismagma.com/assets/home/<USER>/seq/133.webp?2
https://thisismagma.com/assets/home/<USER>/seq/134.webp?2
https://thisismagma.com/assets/home/<USER>/seq/135.webp?2
https://thisismagma.com/assets/home/<USER>/seq/136.webp?2

`;
return data.split("\n")[index];
}

const frameCount = 136;

const images = [];
const imageSeq = {
frame: 1,
};

for (let i = 0; i < frameCount; i++) {
const img = new Image();
img.src = files(i);
images.push(img);
}

gsap.to(imageSeq, {
frame: frameCount - 1,
snap: "frame",
ease: `none`,
scrollTrigger: {
scrub: .5,
trigger: `#page7`,
start: `top top`,
end: `250% top`,
scroller: `#main`,
},
onUpdate: render,
});

images[1].onload = render;

function render() {
scaleImage(images[imageSeq.frame], context);
}

function scaleImage(img, ctx) {
var canvas = ctx.canvas;
var hRatio = canvas.width / img.width;
var vRatio = canvas.height / img.height;
var ratio = Math.max(hRatio, vRatio);
var centerShift_x = (canvas.width - img.width * ratio) / 2;
var centerShift_y = (canvas.height - img.height * ratio) / 2;
ctx.clearRect(0, 0, canvas.width, canvas.height);
ctx.drawImage(
img,
0,
0,
img.width,
img.height,
centerShift_x,
centerShift_y,
img.width * ratio,
img.height * ratio
);
}
    if (typeof ScrollTrigger !== 'undefined') {
      ScrollTrigger.create({
        trigger: "#page7",
        pin: true,
        scroller: `#main`,
        start: `top top`,
        end: `250% top`,
      });
    }
    
    console.log('Canvas2 initialized successfully');
  } catch (error) {
    console.error('Error initializing canvas2:', error);
  }
}

// Initialize canvas2 when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', canvas2);
} else {
  canvas2();
}



// Page7 circle animations
try {
  if (typeof gsap !== 'undefined') {
    gsap.to(".page7-cir",{
      scrollTrigger:{
        trigger:`.page7-cir`,
        start:`top center`,
        end:`bottom top`,
        scroller:`#main`,
        scrub:.5
      },
      scale:1.5
    });

    gsap.to(".page7-cir-inner",{
      scrollTrigger:{
        trigger:`.page7-cir-inner`,
        start:`top center`,
        end:`bottom top`,
        scroller:`#main`,
        scrub:.5
      },
      backgroundColor : `#0a3bce91`,
    });
    
    console.log('Page7 animations initialized successfully');
  }
} catch (error) {
  console.error('Error setting up page7 animations:', error);
}

// Global initialization function
function initializeLandingPage() {
  console.log('Initializing landing page...');
  
  // Check if all required libraries are loaded
  const requiredLibraries = {
    'GSAP': typeof gsap !== 'undefined',
    'ScrollTrigger': typeof ScrollTrigger !== 'undefined',
    'LocomotiveScroll': typeof LocomotiveScroll !== 'undefined'
  };
  
  console.log('Library status:', requiredLibraries);
  
  // Initialize components
  const components = ['loco', 'canvas', 'canvas1', 'canvas2'];
  components.forEach(component => {
    try {
      if (typeof window[component] === 'function') {
        console.log(`Initializing ${component}...`);
      }
    } catch (error) {
      console.error(`Error with ${component}:`, error);
    }
  });
  
  console.log('Landing page initialization complete');
}

// Run initialization when page loads
window.addEventListener('load', function() {
  console.log('Page loaded, running initialization...');
  initializeLandingPage();
});