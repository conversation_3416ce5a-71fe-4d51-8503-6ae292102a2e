# Production Environment Configuration
# Copy this file to .env.production and update with your production values

# Server Configuration
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here-minimum-32-characters
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# API Keys
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/smart_contract_auditor
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=20
DATABASE_TIMEOUT=30000

# Redis Configuration (for rate limiting and caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# Rate Limiting Configuration
RATE_LIMIT_ANONYMOUS_POINTS=100
RATE_LIMIT_AUTH_POINTS=1000
RATE_LIMIT_PREMIUM_POINTS=5000
RATE_LIMIT_API_POINTS=10000
RATE_LIMIT_HEAVY_POINTS=50
RATE_LIMIT_BURST_POINTS=200

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_MAX_SIZE=10485760
LOG_FILE_MAX_FILES=5

# CORS Configuration
CORS_ORIGIN=https://your-frontend-domain.com,https://your-admin-domain.com
CORS_CREDENTIALS=true

# Security Headers
HSTS_MAX_AGE=31536000
CSP_REPORT_URI=https://your-csp-report-endpoint.com

# Feature Flags
ENABLE_REAL_TIME_MONITORING=true
ENABLE_CHAINIDE_INTEGRATION=true
ENABLE_ANALYTICS=true
ENABLE_TEAM_COLLABORATION=true
DISABLE_RATE_LIMITING=false

# ChainIDE Integration
CHAINIDE_WS_PORT=8080
CHAINIDE_API_URL=https://chainide.com/api
CHAINIDE_WEBHOOK_SECRET=your-chainide-webhook-secret

# Monitoring and Analytics
ANALYTICS_RETENTION_DAYS=90
METRICS_UPDATE_INTERVAL=30000
ENABLE_DETAILED_TRACKING=true
ENABLE_USER_PRIVACY=true

# External Services
SENTRY_DSN=https://<EMAIL>/project-id
DATADOG_API_KEY=your-datadog-api-key
NEW_RELIC_LICENSE_KEY=your-newrelic-license-key

# Blockchain RPC URLs
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-infura-key
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your-infura-key
BSC_RPC_URL=https://bsc-dataseed.binance.org/
ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc
OPTIMISM_RPC_URL=https://mainnet.optimism.io

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=.sol,.vy,.rs,.go,.js,.ts
UPLOAD_DIR=/app/uploads

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret-key
WEBHOOK_TIMEOUT=30000

# Performance Configuration
MAX_CONCURRENT_ANALYSES=10
ANALYSIS_TIMEOUT=300000
CACHE_TTL=3600
MEMORY_LIMIT=2048

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_RETRIES=3

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=********
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/your-cert.pem
SSL_KEY_PATH=/etc/ssl/private/your-key.pem
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.pem

# Container Configuration
CONTAINER_MEMORY_LIMIT=2048m
CONTAINER_CPU_LIMIT=1000m
CONTAINER_RESTART_POLICY=unless-stopped

# Kubernetes Configuration
K8S_NAMESPACE=smart-contract-auditor
K8S_SERVICE_ACCOUNT=auditor-service-account
K8S_CONFIG_MAP=auditor-config
K8S_SECRET=auditor-secrets
