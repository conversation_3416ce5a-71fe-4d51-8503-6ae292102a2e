# Firebase Deployment Guide for Flash-Audit

## 🎉 Deployment Status

✅ **Frontend Successfully Deployed!**
- **Live URL**: https://flash-audit-app.web.app
- **Firebase Project**: flash-audit-app
- **Status**: Active and accessible

⚠️ **Backend Functions**: Requires Blaze Plan
- Firebase Functions need the Blaze (pay-as-you-go) plan
- Currently running on Spark (free) plan

## 📁 Project Structure

```
Flash-Audit/
├── frontend/dist/          # Built frontend files (deployed)
├── functions/              # Firebase Functions (ready to deploy)
├── firebase.json           # Firebase configuration
├── .firebaserc            # Firebase project settings
└── package.json           # Updated with Firebase scripts
```

## 🚀 Deployment Commands

### Frontend Only (Free Tier)
```bash
npm run build:frontend
firebase deploy --only hosting
```

### Full Deployment (Requires Blaze Plan)
```bash
npm run firebase:build
firebase deploy
```

### Individual Deployments
```bash
# Deploy only hosting
firebase deploy --only hosting

# Deploy only functions (requires Blaze plan)
firebase deploy --only functions
```

## 💰 Upgrading to Blaze Plan

To deploy the backend functions, you need to upgrade to the Blaze plan:

1. Visit: https://console.firebase.google.com/project/flash-audit-app/usage/details
2. Click "Upgrade to Blaze"
3. Set up billing (pay-as-you-go)
4. Run: `firebase deploy`

## 🔧 Configuration Files

### firebase.json
- Configured for hosting from `frontend/dist`
- Functions source in `functions/` directory
- Rewrites for SPA routing

### .firebaserc
- Project ID: `flash-audit-app`
- Default project configuration

### functions/.env
- Environment variables for backend
- OpenRouter API keys configured
- Supabase configuration ready

## 🌐 Live Application

Your Flash-Audit application is now live at:
**https://flash-audit-app.web.app**

## 📝 Next Steps

1. **Test the frontend**: Visit the live URL and test the UI
2. **Upgrade to Blaze**: If you need backend functionality
3. **Configure Supabase**: Update environment variables with real Supabase credentials
4. **Custom Domain**: Optionally set up a custom domain in Firebase Console

## 🔄 Future Deployments

For future updates:
```bash
# Build and deploy frontend
npm run build:frontend
firebase deploy --only hosting

# Or use the convenience script
npm run firebase:deploy:hosting
```

## 🛠️ Troubleshooting

- **Build errors**: Check `npm run build:frontend` output
- **Function errors**: Ensure Blaze plan is active
- **Environment variables**: Update `functions/.env` with real values
- **CORS issues**: Update CORS_ORIGIN in environment variables

## 📞 Support

If you encounter issues:
1. Check Firebase Console logs
2. Verify environment variables
3. Ensure all dependencies are installed
4. Check Firebase CLI version: `firebase --version`
