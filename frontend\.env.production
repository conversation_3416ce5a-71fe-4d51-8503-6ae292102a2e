# Production Environment Variables for Frontend
NODE_ENV=production
VITE_NODE_ENV=production

# API Configuration
VITE_API_BASE_URL=https://flash-audit-app.web.app/api
VITE_API_URL=https://flash-audit-app.web.app/api
VITE_APP_NAME=FlashAudit
VITE_APP_VERSION=2.0.0

# Development Settings
VITE_DEV_MODE=false
VITE_DEBUG_MODE=false

# Authentication
VITE_ENABLE_AUTH=false
VITE_JWT_STORAGE_KEY=flashaudit_token

# Features
VITE_ENABLE_MULTI_CHAIN=true
VITE_ENABLE_AI_ANALYSIS=true
VITE_ENABLE_TEAM_COLLABORATION=true
VITE_ENABLE_REAL_TIME_MONITORING=true

# Supabase Configuration (placeholder - replace with real values)
VITE_SUPABASE_URL=your-supabase-url-here
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key-here

# Clerk Configuration (placeholder - replace with real values)
VITE_CLERK_PUBLISHABLE_KEY=your-clerk-publishable-key-here
