import React from 'react';
import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/clerk-react';

function SimpleApp() {
  console.log('SimpleApp rendering...');
  
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <header style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '40px',
        borderBottom: '1px solid #eee',
        paddingBottom: '20px'
      }}>
        <h1 style={{ color: '#007acc', margin: 0 }}>Flash-Audit</h1>
        <div>
          <SignedOut>
            <SignInButton mode="modal">
              <button style={{
                padding: '10px 20px',
                backgroundColor: '#007acc',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer'
              }}>
                Sign In
              </button>
            </SignInButton>
          </SignedOut>
          <SignedIn>
            <UserButton />
          </SignedIn>
        </div>
      </header>

      <main>
        <SignedOut>
          <div style={{ textAlign: 'center', marginTop: '100px' }}>
            <h2>Welcome to Flash-Audit</h2>
            <p>AI-Powered Smart Contract Security Auditing Platform</p>
            <p>Please sign in to continue.</p>
          </div>
        </SignedOut>

        <SignedIn>
          <div>
            <h2>Dashboard</h2>
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '20px',
              marginTop: '20px'
            }}>
              <div style={{
                padding: '20px',
                border: '1px solid #ddd',
                borderRadius: '8px',
                backgroundColor: '#f9f9f9'
              }}>
                <h3>Smart Contract Analysis</h3>
                <p>Upload and analyze your smart contracts for vulnerabilities.</p>
                <button style={{
                  padding: '8px 16px',
                  backgroundColor: '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}>
                  Start Analysis
                </button>
              </div>

              <div style={{
                padding: '20px',
                border: '1px solid #ddd',
                borderRadius: '8px',
                backgroundColor: '#f9f9f9'
              }}>
                <h3>Multi-Chain Support</h3>
                <p>Support for Ethereum, Polygon, Arbitrum, and more.</p>
                <button style={{
                  padding: '8px 16px',
                  backgroundColor: '#17a2b8',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}>
                  View Networks
                </button>
              </div>

              <div style={{
                padding: '20px',
                border: '1px solid #ddd',
                borderRadius: '8px',
                backgroundColor: '#f9f9f9'
              }}>
                <h3>AI Analysis</h3>
                <p>Powered by advanced AI models for comprehensive security analysis.</p>
                <button style={{
                  padding: '8px 16px',
                  backgroundColor: '#6f42c1',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}>
                  Learn More
                </button>
              </div>
            </div>
          </div>
        </SignedIn>
      </main>
    </div>
  );
}

export default SimpleApp;
