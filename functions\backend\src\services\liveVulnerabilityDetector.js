const EventEmitter = require('events');
const logger = require('../utils/logger');

/**
 * Live Vulnerability Detector Service
 * Provides real-time vulnerability detection for code changes
 */
class LiveVulnerabilityDetector extends EventEmitter {
  constructor() {
    super();
    this.activeSessions = new Map();
    this.detectionRules = this.initializeDetectionRules();
    this.isInitialized = false;
  }

  /**
   * Initialize detection rules
   * @returns {Object} Detection rules
   */
  initializeDetectionRules() {
    return {
      reentrancy: {
        patterns: [/\.call\s*\{/, /\.call\s*\(/, /\.send\s*\(/, /\.transfer\s*\(/],
        severity: 'high',
        message: 'Potential reentrancy vulnerability detected'
      },
      txOrigin: {
        patterns: [/tx\.origin/],
        severity: 'high',
        message: 'Use of tx.origin detected - use msg.sender instead'
      },
      uncheckedCall: {
        patterns: [/\.call\s*\((?![^)]*require)/, /\.delegatecall\s*\(/],
        severity: 'medium',
        message: 'Unchecked external call detected'
      },
      timestampDependence: {
        patterns: [/block\.timestamp/, /\bnow\b/],
        severity: 'low',
        message: 'Timestamp dependence detected'
      },
      integerOverflow: {
        patterns: [/\+\+/, /--/, /\+\s*=/, /-\s*=/, /\*\s*=/],
        severity: 'medium',
        message: 'Potential integer overflow/underflow'
      }
    };
  }

  /**
   * Initialize the live vulnerability detector
   * @param {Object} config - Service configuration
   */
  async initialize(config = {}) {
    try {
      this.config = {
        enablePatternDetection: config.enablePatternDetection !== false,
        enableRuleBasedDetection: config.enableRuleBasedDetection !== false,
        enableAIDetection: config.enableAIDetection !== false,
        alertLevel: config.alertLevel || 'medium',
        realTimeAlerts: config.realTimeAlerts !== false,
        ...config
      };

      this.isInitialized = true;
      logger.info('Live vulnerability detector initialized', this.config);
    } catch (error) {
      logger.error('Failed to initialize live vulnerability detector', { error: error.message });
      throw error;
    }
  }

  /**
   * Start detection session for a user
   * @param {string} userId - User identifier
   * @param {Object} sessionConfig - Session configuration
   * @returns {string} Session ID
   */
  startDetectionSession(userId, sessionConfig = {}) {
    const sessionId = `detection_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const session = {
      sessionId,
      userId,
      startedAt: new Date().toISOString(),
      config: sessionConfig,
      metrics: {
        detectionsCount: 0,
        alertsGenerated: 0,
        vulnerabilitiesFound: 0
      }
    };

    this.activeSessions.set(sessionId, session);
    
    logger.info('Detection session started', { userId, sessionId });
    return sessionId;
  }

  /**
   * End detection session
   * @param {string} sessionId - Session ID
   */
  endDetectionSession(sessionId) {
    if (this.activeSessions.has(sessionId)) {
      const session = this.activeSessions.get(sessionId);
      this.activeSessions.delete(sessionId);
      
      logger.info('Detection session ended', { 
        sessionId, 
        duration: Date.now() - new Date(session.startedAt).getTime() 
      });
    }
  }

  /**
   * Perform live vulnerability detection
   * @param {string|object} sessionId - Session ID or object containing sessionId
   * @param {Object} codeData - Code data to analyze
   * @returns {Object} Detection results
   */
  async performLiveDetection(sessionId, codeData) {
    try {
      // Accept sessionId as string or object
      let sid = sessionId;
      if (typeof sessionId === 'object' && sessionId !== null && sessionId.sessionId) {
        sid = sessionId.sessionId;
      }
      const session = this.activeSessions.get(sid);
      if (!session) {
        throw new Error('Invalid session ID');
      }

      // Ensure config is always defined
      if (!this.config) {
        this.config = {
          enablePatternDetection: true,
          enableRuleBasedDetection: true,
          enableAIDetection: false,
          alertLevel: 'medium',
          realTimeAlerts: true
        };
      }

      const { content, filePath, changeType } = codeData;

      const detection = {
        alerts: [],
        vulnerabilities: [],
        timestamp: new Date().toISOString()
      };

      // Pattern-based detection
      if (this.config.enablePatternDetection) {
        const patternResults = this.detectPatternVulnerabilities(content);
        detection.alerts.push(...patternResults.alerts);
        detection.vulnerabilities.push(...patternResults.vulnerabilities);
      }

      // Rule-based detection
      if (this.config.enableRuleBasedDetection) {
        const ruleResults = this.detectRuleBasedVulnerabilities(content);
        detection.alerts.push(...ruleResults.alerts);
        detection.vulnerabilities.push(...ruleResults.vulnerabilities);
      }

      session.metrics.detectionsCount++;
      session.metrics.alertsGenerated += detection.alerts.length;
      session.metrics.vulnerabilitiesFound += detection.vulnerabilities.length;

      this.emit('detection:alerts', { sessionId: sid, alerts: detection.alerts });
      this.emit('detection:completed', { sessionId: sid, detection });

      return detection;

    } catch (error) {
      logger.error('Live detection failed', { sessionId, error: error.message });
      return {
        alerts: [],
        vulnerabilities: [],
        error: error.message
      };
    }
  }

  /**
   * Detect vulnerabilities using pattern matching
   * @param {string} content - Code content
   * @returns {Object} Pattern detection results
   */
  detectPatternVulnerabilities(content) {
    const alerts = [];
    const vulnerabilities = [];
    const lines = content.split('\n');

    Object.entries(this.detectionRules).forEach(([ruleType, rule]) => {
      rule.patterns.forEach(pattern => {
        lines.forEach((line, index) => {
          if (pattern.test(line)) {
            const alert = {
              type: 'pattern',
              ruleType,
              severity: rule.severity,
              message: rule.message,
              line: index + 1,
              code: line.trim(),
              timestamp: new Date().toISOString()
            };

            alerts.push(alert);

            if (rule.severity === 'high' || rule.severity === 'critical') {
              vulnerabilities.push({
                name: ruleType,
                severity: rule.severity,
                description: rule.message,
                line: index + 1,
                codeSnippet: line.trim(),
                recommendation: this.getRecommendation(ruleType)
              });
            }
          }
        });
      });
    });

    return { alerts, vulnerabilities };
  }

  /**
   * Detect vulnerabilities using rule-based analysis
   * @param {string} content - Code content
   * @returns {Object} Rule-based detection results
   */
  detectRuleBasedVulnerabilities(content) {
    const alerts = [];
    const vulnerabilities = [];

    try {
      // Check for common vulnerability patterns
      const rules = [
        {
          name: 'missing_access_control',
          check: () => !content.includes('onlyOwner') && !content.includes('require(msg.sender'),
          severity: 'medium',
          message: 'Missing access control mechanisms'
        },
        {
          name: 'missing_reentrancy_guard',
          check: () => content.includes('.call(') && !content.includes('nonReentrant'),
          severity: 'high',
          message: 'External calls without reentrancy protection'
        },
        {
          name: 'unsafe_math',
          check: () => (content.includes('+') || content.includes('-') || content.includes('*')) && !content.includes('SafeMath'),
          severity: 'medium',
          message: 'Arithmetic operations without SafeMath'
        }
      ];

      rules.forEach(rule => {
        if (rule.check()) {
          const alert = {
            type: 'rule',
            ruleType: rule.name,
            severity: rule.severity,
            message: rule.message,
            timestamp: new Date().toISOString()
          };

          alerts.push(alert);

          if (rule.severity === 'high' || rule.severity === 'critical') {
            vulnerabilities.push({
              name: rule.name,
              severity: rule.severity,
              description: rule.message,
              recommendation: this.getRecommendation(rule.name)
            });
          }
        }
      });

    } catch (error) {
      logger.error('Rule-based detection failed', { error: error.message });
    }

    return { alerts, vulnerabilities };
  }

  /**
   * Get recommendation for a vulnerability type
   * @param {string} vulnerabilityType - Type of vulnerability
   * @returns {string} Recommendation
   */
  getRecommendation(vulnerabilityType) {
    const recommendations = {
      reentrancy: 'Use ReentrancyGuard modifier or checks-effects-interactions pattern',
      txOrigin: 'Replace tx.origin with msg.sender for authentication',
      uncheckedCall: 'Check return values of external calls',
      timestampDependence: 'Avoid using block.timestamp for critical logic',
      integerOverflow: 'Use SafeMath library or Solidity 0.8+ built-in overflow protection',
      missing_access_control: 'Implement proper access control using modifiers',
      missing_reentrancy_guard: 'Add ReentrancyGuard to functions with external calls',
      unsafe_math: 'Use SafeMath library for arithmetic operations'
    };

    return recommendations[vulnerabilityType] || 'Review code for potential security issues';
  }

  /**
   * Get service status
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      activeSessions: this.activeSessions.size,
      detectionRules: Object.keys(this.detectionRules).length,
      config: this.config
    };
  }

  /**
   * Cleanup service resources
   */
  cleanup() {
    this.activeSessions.clear();
    this.removeAllListeners();
    this.isInitialized = false;
    
    logger.info('Live vulnerability detector cleaned up');
  }
}

module.exports = new LiveVulnerabilityDetector();