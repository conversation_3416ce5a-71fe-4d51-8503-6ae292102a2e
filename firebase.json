{"hosting": {"public": "frontend/dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/landing.html", "destination": "/landing.html"}, {"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}]}, "functions": {"source": "functions", "runtime": "nodejs22", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}, "emulators": {"functions": {"port": 5001}, "hosting": {"port": 5000}, "ui": {"enabled": true}}}