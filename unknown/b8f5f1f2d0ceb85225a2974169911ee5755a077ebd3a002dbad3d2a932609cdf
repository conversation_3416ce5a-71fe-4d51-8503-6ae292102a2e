# Flash-Audit Production Environment Variables for Vercel Deployment
# Copy these variables to your Vercel project settings

# ===== FRONTEND ENVIRONMENT VARIABLES =====
# These should be prefixed with VITE_ for Vite to include them in the build

# API Configuration
VITE_API_BASE_URL=https://your-vercel-app.vercel.app
VITE_APP_NAME=FlashAudit
VITE_APP_VERSION=2.0.0
VITE_NODE_ENV=production
VITE_DEV_MODE=false
VITE_DEBUG_MODE=false

# Authentication Configuration
VITE_ENABLE_AUTH=true
VITE_JWT_STORAGE_KEY=flashaudit_token

# Supabase Configuration (Frontend)
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# Clerk Configuration (Frontend)
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your-clerk-publishable-key

# Feature Flags
VITE_ENABLE_MULTI_CHAIN=true
VITE_ENABLE_AI_ANALYSIS=true
VITE_ENABLE_TEAM_COLLABORATION=true
VITE_ENABLE_REAL_TIME_MONITORING=true

# ===== BACKEND ENVIRONMENT VARIABLES =====

# Server Configuration
NODE_ENV=production
PORT=3001

# Supabase Configuration (Backend)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Clerk Configuration (Backend)
CLERK_SECRET_KEY=sk_test_your-clerk-secret-key
CLERK_PUBLISHABLE_KEY=pk_test_your-clerk-publishable-key

# OpenRouter API Configuration
OPENROUTER_API_KEY=sk-or-v1-3010990c3182e869153e60225199f259e307e002db4f0da9aec7f502125c7ac
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Dual LLM Strategy Configuration
OPENROUTER_API_KEY_KIMI=sk-or-v1-3010990c3182e869153e60225199f259e307e002db4f0da9aec7f502125c7ac
KIMI_MODEL=moonshotai/kimi-dev-72b:free
OPENROUTER_API_KEY_GEMMA=sk-or-v1-3010990c3182e869153e60225199f259e307e002db4f0da9aec7f502125c7ac
GEMMA_MODEL=google/gemma-3n-e4b-it:free
DEFAULT_MODEL_STRATEGY=dual

# Blockchain RPC URLs
ETHEREUM_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/demo
POLYGON_RPC_URL=https://polygon-mainnet.g.alchemy.com/v2/demo
BSC_RPC_URL=https://bsc-dataseed.binance.org/
ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc
OPTIMISM_RPC_URL=https://mainnet.optimism.io
BASE_RPC_URL=https://mainnet.base.org

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-for-production
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Audit Configuration
MAX_CONTRACT_SIZE_BYTES=1048576
AUDIT_TIMEOUT_MS=30000
VULNERABILITY_THRESHOLD_HIGH=80
VULNERABILITY_THRESHOLD_MEDIUM=50

# Logging Configuration
LOG_LEVEL=warn
LOG_FILE_PATH=./logs/app.log

# TEE Monitor Configuration
TEE_LOG_ENABLED=true
TEE_LOG_PATH=./logs/auditLogs.json
TEE_ENCRYPTION_KEY=your-production-tee-encryption-key

# CORS Configuration
CORS_ORIGIN=https://your-vercel-app.vercel.app
SITE_URL=https://your-vercel-app.vercel.app

# Feature Flags (Backend)
ENABLE_REAL_TIME_MONITORING=true
ENABLE_CHAINIDE_INTEGRATION=true
CHAINIDE_WS_PORT=8080
CHAINIDE_MAX_CONNECTIONS=1000
CHAINIDE_ENABLE_COLLABORATION=true
CHAINIDE_ENABLE_REALTIME_ANALYSIS=true
CHAINIDE_ENABLE_CODE_COMPLETION=true
CHAINIDE_DEBOUNCE_DELAY=1000
CHAINIDE_MAX_QUEUE_SIZE=100

# Analytics and Monitoring
ANALYTICS_RETENTION_DAYS=90
METRICS_UPDATE_INTERVAL=30000
ENABLE_DETAILED_TRACKING=true
ENABLE_USER_PRIVACY=true
