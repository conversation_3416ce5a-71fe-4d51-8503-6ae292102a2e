{"name": "dao-smart-contract-auditor", "version": "2.0.0", "description": "Full-stack DAO Smart Contract Security Auditor with AI-powered analysis", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm start", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:frontend": "cd frontend && npm run build && npm run preview", "start:backend": "cd backend && npm start", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && echo 'Backend build complete'", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd frontend && rm -rf node_modules dist", "clean:backend": "cd backend && rm -rf node_modules", "setup": "npm run install:all && npm run setup:env", "setup:env": "node scripts/setup-environment.js", "check:health": "node scripts/health-check.js", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "vercel:build": "npm run install:all && npm run build:frontend", "vercel:dev": "npm run dev:frontend"}, "devDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.44.1", "concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/euii-ii/dao.git"}, "keywords": ["dao", "smart-contract", "security", "auditor", "blockchain", "web3", "solidity", "vulnerability", "llm", "ai", "multi-chain", "defi"], "author": "DAO Development Team", "license": "MIT", "optionalDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.24.0"}}