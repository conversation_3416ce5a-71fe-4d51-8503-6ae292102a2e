const fs = require('fs-extra');
const path = require('path');

async function copyBackend() {
  try {
    console.log('Copying backend files to functions directory...');
    
    // Copy the entire backend directory
    await fs.copy(
      path.join(__dirname, '..', 'backend'),
      path.join(__dirname, 'backend'),
      {
        filter: (src) => {
          // Exclude node_modules and other unnecessary files
          return !src.includes('node_modules') && 
                 !src.includes('.git') && 
                 !src.includes('coverage') &&
                 !src.includes('logs');
        }
      }
    );
    
    console.log('Backend files copied successfully!');
  } catch (error) {
    console.error('Error copying backend files:', error);
    process.exit(1);
  }
}

copyBackend();
