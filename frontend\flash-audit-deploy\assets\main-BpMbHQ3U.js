var tb=Object.defineProperty,nb=Object.defineProperties;var ab=Object.getOwnPropertyDescriptors;var ql=Object.getOwnPropertySymbols;var jp=Object.prototype.hasOwnProperty,Ep=Object.prototype.propertyIsEnumerable;var Ru=(s,a,r)=>a in s?tb(s,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):s[a]=r,se=(s,a)=>{for(var r in a||(a={}))jp.call(a,r)&&Ru(s,r,a[r]);if(ql)for(var r of ql(a))Ep.call(a,r)&&Ru(s,r,a[r]);return s},Ve=(s,a)=>nb(s,ab(a));var Fe=(s,a)=>{var r={};for(var l in s)jp.call(s,l)&&a.indexOf(l)<0&&(r[l]=s[l]);if(s!=null&&ql)for(var l of ql(s))a.indexOf(l)<0&&Ep.call(s,l)&&(r[l]=s[l]);return r};var ib=(s,a)=>()=>(a||s((a={exports:{}}).exports,a),a.exports);var xp=(s,a,r)=>Ru(s,typeof a!="symbol"?a+"":a,r);var E=(s,a,r)=>new Promise((l,o)=>{var u=g=>{try{m(r.next(g))}catch(v){o(v)}},f=g=>{try{m(r.throw(g))}catch(v){o(v)}},m=g=>g.done?l(g.value):Promise.resolve(g.value).then(u,f);m((r=r.apply(s,a)).next())});var v2=ib(ct=>{(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))l(o);new MutationObserver(o=>{for(const u of o)if(u.type==="childList")for(const f of u.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function r(o){const u={};return o.integrity&&(u.integrity=o.integrity),o.referrerPolicy&&(u.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?u.credentials="include":o.crossOrigin==="anonymous"?u.credentials="omit":u.credentials="same-origin",u}function l(o){if(o.ep)return;o.ep=!0;const u=r(o);fetch(o.href,u)}})();function Md(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}function sb(s){if(Object.prototype.hasOwnProperty.call(s,"__esModule"))return s;var a=s.default;if(typeof a=="function"){var r=function l(){return this instanceof l?Reflect.construct(a,arguments,this.constructor):a.apply(this,arguments)};r.prototype=a.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(s).forEach(function(l){var o=Object.getOwnPropertyDescriptor(s,l);Object.defineProperty(r,l,o.get?o:{enumerable:!0,get:function(){return s[l]}})}),r}var Mu={exports:{}},lr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kp;function rb(){if(kp)return lr;kp=1;var s=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function r(l,o,u){var f=null;if(u!==void 0&&(f=""+u),o.key!==void 0&&(f=""+o.key),"key"in o){u={};for(var m in o)m!=="key"&&(u[m]=o[m])}else u=o;return o=u.ref,{$$typeof:s,type:l,key:f,ref:o!==void 0?o:null,props:u}}return lr.Fragment=a,lr.jsx=r,lr.jsxs=r,lr}var Tp;function lb(){return Tp||(Tp=1,Mu.exports=rb()),Mu.exports}var h=lb(),Uu={exports:{}},_e={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cp;function ob(){if(Cp)return _e;Cp=1;var s=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),u=Symbol.for("react.consumer"),f=Symbol.for("react.context"),m=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),S=Symbol.iterator;function x(j){return j===null||typeof j!="object"?null:(j=S&&j[S]||j["@@iterator"],typeof j=="function"?j:null)}var C={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},U=Object.assign,Y={};function V(j,I,X){this.props=j,this.context=I,this.refs=Y,this.updater=X||C}V.prototype.isReactComponent={},V.prototype.setState=function(j,I){if(typeof j!="object"&&typeof j!="function"&&j!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,j,I,"setState")},V.prototype.forceUpdate=function(j){this.updater.enqueueForceUpdate(this,j,"forceUpdate")};function F(){}F.prototype=V.prototype;function J(j,I,X){this.props=j,this.context=I,this.refs=Y,this.updater=X||C}var q=J.prototype=new F;q.constructor=J,U(q,V.prototype),q.isPureReactComponent=!0;var te=Array.isArray,K={H:null,A:null,T:null,S:null,V:null},W=Object.prototype.hasOwnProperty;function le(j,I,X,Z,re,ve){return X=ve.ref,{$$typeof:s,type:j,key:I,ref:X!==void 0?X:null,props:ve}}function ne(j,I){return le(j.type,I,void 0,void 0,void 0,j.props)}function ye(j){return typeof j=="object"&&j!==null&&j.$$typeof===s}function ge(j){var I={"=":"=0",":":"=2"};return"$"+j.replace(/[=:]/g,function(X){return I[X]})}var ue=/\/+/g;function Ae(j,I){return typeof j=="object"&&j!==null&&j.key!=null?ge(""+j.key):I.toString(36)}function Le(){}function Qe(j){switch(j.status){case"fulfilled":return j.value;case"rejected":throw j.reason;default:switch(typeof j.status=="string"?j.then(Le,Le):(j.status="pending",j.then(function(I){j.status==="pending"&&(j.status="fulfilled",j.value=I)},function(I){j.status==="pending"&&(j.status="rejected",j.reason=I)})),j.status){case"fulfilled":return j.value;case"rejected":throw j.reason}}throw j}function Me(j,I,X,Z,re){var ve=typeof j;(ve==="undefined"||ve==="boolean")&&(j=null);var de=!1;if(j===null)de=!0;else switch(ve){case"bigint":case"string":case"number":de=!0;break;case"object":switch(j.$$typeof){case s:case a:de=!0;break;case y:return de=j._init,Me(de(j._payload),I,X,Z,re)}}if(de)return re=re(j),de=Z===""?"."+Ae(j,0):Z,te(re)?(X="",de!=null&&(X=de.replace(ue,"$&/")+"/"),Me(re,I,X,"",function(Bt){return Bt})):re!=null&&(ye(re)&&(re=ne(re,X+(re.key==null||j&&j.key===re.key?"":(""+re.key).replace(ue,"$&/")+"/")+de)),I.push(re)),1;de=0;var oe=Z===""?".":Z+":";if(te(j))for(var Pe=0;Pe<j.length;Pe++)Z=j[Pe],ve=oe+Ae(Z,Pe),de+=Me(Z,I,X,ve,re);else if(Pe=x(j),typeof Pe=="function")for(j=Pe.call(j),Pe=0;!(Z=j.next()).done;)Z=Z.value,ve=oe+Ae(Z,Pe++),de+=Me(Z,I,X,ve,re);else if(ve==="object"){if(typeof j.then=="function")return Me(Qe(j),I,X,Z,re);throw I=String(j),Error("Objects are not valid as a React child (found: "+(I==="[object Object]"?"object with keys {"+Object.keys(j).join(", ")+"}":I)+"). If you meant to render a collection of children, use an array instead.")}return de}function z(j,I,X){if(j==null)return j;var Z=[],re=0;return Me(j,Z,"","",function(ve){return I.call(X,ve,re++)}),Z}function G(j){if(j._status===-1){var I=j._result;I=I(),I.then(function(X){(j._status===0||j._status===-1)&&(j._status=1,j._result=X)},function(X){(j._status===0||j._status===-1)&&(j._status=2,j._result=X)}),j._status===-1&&(j._status=0,j._result=I)}if(j._status===1)return j._result.default;throw j._result}var ae=typeof reportError=="function"?reportError:function(j){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var I=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof j=="object"&&j!==null&&typeof j.message=="string"?String(j.message):String(j),error:j});if(!window.dispatchEvent(I))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",j);return}console.error(j)};function xe(){}return _e.Children={map:z,forEach:function(j,I,X){z(j,function(){I.apply(this,arguments)},X)},count:function(j){var I=0;return z(j,function(){I++}),I},toArray:function(j){return z(j,function(I){return I})||[]},only:function(j){if(!ye(j))throw Error("React.Children.only expected to receive a single React element child.");return j}},_e.Component=V,_e.Fragment=r,_e.Profiler=o,_e.PureComponent=J,_e.StrictMode=l,_e.Suspense=g,_e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=K,_e.__COMPILER_RUNTIME={__proto__:null,c:function(j){return K.H.useMemoCache(j)}},_e.cache=function(j){return function(){return j.apply(null,arguments)}},_e.cloneElement=function(j,I,X){if(j==null)throw Error("The argument must be a React element, but you passed "+j+".");var Z=U({},j.props),re=j.key,ve=void 0;if(I!=null)for(de in I.ref!==void 0&&(ve=void 0),I.key!==void 0&&(re=""+I.key),I)!W.call(I,de)||de==="key"||de==="__self"||de==="__source"||de==="ref"&&I.ref===void 0||(Z[de]=I[de]);var de=arguments.length-2;if(de===1)Z.children=X;else if(1<de){for(var oe=Array(de),Pe=0;Pe<de;Pe++)oe[Pe]=arguments[Pe+2];Z.children=oe}return le(j.type,re,void 0,void 0,ve,Z)},_e.createContext=function(j){return j={$$typeof:f,_currentValue:j,_currentValue2:j,_threadCount:0,Provider:null,Consumer:null},j.Provider=j,j.Consumer={$$typeof:u,_context:j},j},_e.createElement=function(j,I,X){var Z,re={},ve=null;if(I!=null)for(Z in I.key!==void 0&&(ve=""+I.key),I)W.call(I,Z)&&Z!=="key"&&Z!=="__self"&&Z!=="__source"&&(re[Z]=I[Z]);var de=arguments.length-2;if(de===1)re.children=X;else if(1<de){for(var oe=Array(de),Pe=0;Pe<de;Pe++)oe[Pe]=arguments[Pe+2];re.children=oe}if(j&&j.defaultProps)for(Z in de=j.defaultProps,de)re[Z]===void 0&&(re[Z]=de[Z]);return le(j,ve,void 0,void 0,null,re)},_e.createRef=function(){return{current:null}},_e.forwardRef=function(j){return{$$typeof:m,render:j}},_e.isValidElement=ye,_e.lazy=function(j){return{$$typeof:y,_payload:{_status:-1,_result:j},_init:G}},_e.memo=function(j,I){return{$$typeof:v,type:j,compare:I===void 0?null:I}},_e.startTransition=function(j){var I=K.T,X={};K.T=X;try{var Z=j(),re=K.S;re!==null&&re(X,Z),typeof Z=="object"&&Z!==null&&typeof Z.then=="function"&&Z.then(xe,ae)}catch(ve){ae(ve)}finally{K.T=I}},_e.unstable_useCacheRefresh=function(){return K.H.useCacheRefresh()},_e.use=function(j){return K.H.use(j)},_e.useActionState=function(j,I,X){return K.H.useActionState(j,I,X)},_e.useCallback=function(j,I){return K.H.useCallback(j,I)},_e.useContext=function(j){return K.H.useContext(j)},_e.useDebugValue=function(){},_e.useDeferredValue=function(j,I){return K.H.useDeferredValue(j,I)},_e.useEffect=function(j,I,X){var Z=K.H;if(typeof X=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Z.useEffect(j,I)},_e.useId=function(){return K.H.useId()},_e.useImperativeHandle=function(j,I,X){return K.H.useImperativeHandle(j,I,X)},_e.useInsertionEffect=function(j,I){return K.H.useInsertionEffect(j,I)},_e.useLayoutEffect=function(j,I){return K.H.useLayoutEffect(j,I)},_e.useMemo=function(j,I){return K.H.useMemo(j,I)},_e.useOptimistic=function(j,I){return K.H.useOptimistic(j,I)},_e.useReducer=function(j,I,X){return K.H.useReducer(j,I,X)},_e.useRef=function(j){return K.H.useRef(j)},_e.useState=function(j){return K.H.useState(j)},_e.useSyncExternalStore=function(j,I,X){return K.H.useSyncExternalStore(j,I,X)},_e.useTransition=function(){return K.H.useTransition()},_e.version="19.1.0",_e}var Ap;function fo(){return Ap||(Ap=1,Uu.exports=ob()),Uu.exports}var ee=fo();const P=Md(ee);var Du={exports:{}},or={},zu={exports:{}},Lu={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Op;function cb(){return Op||(Op=1,function(s){function a(z,G){var ae=z.length;z.push(G);e:for(;0<ae;){var xe=ae-1>>>1,j=z[xe];if(0<o(j,G))z[xe]=G,z[ae]=j,ae=xe;else break e}}function r(z){return z.length===0?null:z[0]}function l(z){if(z.length===0)return null;var G=z[0],ae=z.pop();if(ae!==G){z[0]=ae;e:for(var xe=0,j=z.length,I=j>>>1;xe<I;){var X=2*(xe+1)-1,Z=z[X],re=X+1,ve=z[re];if(0>o(Z,ae))re<j&&0>o(ve,Z)?(z[xe]=ve,z[re]=ae,xe=re):(z[xe]=Z,z[X]=ae,xe=X);else if(re<j&&0>o(ve,ae))z[xe]=ve,z[re]=ae,xe=re;else break e}}return G}function o(z,G){var ae=z.sortIndex-G.sortIndex;return ae!==0?ae:z.id-G.id}if(s.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var u=performance;s.unstable_now=function(){return u.now()}}else{var f=Date,m=f.now();s.unstable_now=function(){return f.now()-m}}var g=[],v=[],y=1,S=null,x=3,C=!1,U=!1,Y=!1,V=!1,F=typeof setTimeout=="function"?setTimeout:null,J=typeof clearTimeout=="function"?clearTimeout:null,q=typeof setImmediate!="undefined"?setImmediate:null;function te(z){for(var G=r(v);G!==null;){if(G.callback===null)l(v);else if(G.startTime<=z)l(v),G.sortIndex=G.expirationTime,a(g,G);else break;G=r(v)}}function K(z){if(Y=!1,te(z),!U)if(r(g)!==null)U=!0,W||(W=!0,Ae());else{var G=r(v);G!==null&&Me(K,G.startTime-z)}}var W=!1,le=-1,ne=5,ye=-1;function ge(){return V?!0:!(s.unstable_now()-ye<ne)}function ue(){if(V=!1,W){var z=s.unstable_now();ye=z;var G=!0;try{e:{U=!1,Y&&(Y=!1,J(le),le=-1),C=!0;var ae=x;try{t:{for(te(z),S=r(g);S!==null&&!(S.expirationTime>z&&ge());){var xe=S.callback;if(typeof xe=="function"){S.callback=null,x=S.priorityLevel;var j=xe(S.expirationTime<=z);if(z=s.unstable_now(),typeof j=="function"){S.callback=j,te(z),G=!0;break t}S===r(g)&&l(g),te(z)}else l(g);S=r(g)}if(S!==null)G=!0;else{var I=r(v);I!==null&&Me(K,I.startTime-z),G=!1}}break e}finally{S=null,x=ae,C=!1}G=void 0}}finally{G?Ae():W=!1}}}var Ae;if(typeof q=="function")Ae=function(){q(ue)};else if(typeof MessageChannel!="undefined"){var Le=new MessageChannel,Qe=Le.port2;Le.port1.onmessage=ue,Ae=function(){Qe.postMessage(null)}}else Ae=function(){F(ue,0)};function Me(z,G){le=F(function(){z(s.unstable_now())},G)}s.unstable_IdlePriority=5,s.unstable_ImmediatePriority=1,s.unstable_LowPriority=4,s.unstable_NormalPriority=3,s.unstable_Profiling=null,s.unstable_UserBlockingPriority=2,s.unstable_cancelCallback=function(z){z.callback=null},s.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ne=0<z?Math.floor(1e3/z):5},s.unstable_getCurrentPriorityLevel=function(){return x},s.unstable_next=function(z){switch(x){case 1:case 2:case 3:var G=3;break;default:G=x}var ae=x;x=G;try{return z()}finally{x=ae}},s.unstable_requestPaint=function(){V=!0},s.unstable_runWithPriority=function(z,G){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var ae=x;x=z;try{return G()}finally{x=ae}},s.unstable_scheduleCallback=function(z,G,ae){var xe=s.unstable_now();switch(typeof ae=="object"&&ae!==null?(ae=ae.delay,ae=typeof ae=="number"&&0<ae?xe+ae:xe):ae=xe,z){case 1:var j=-1;break;case 2:j=250;break;case 5:j=1073741823;break;case 4:j=1e4;break;default:j=5e3}return j=ae+j,z={id:y++,callback:G,priorityLevel:z,startTime:ae,expirationTime:j,sortIndex:-1},ae>xe?(z.sortIndex=ae,a(v,z),r(g)===null&&z===r(v)&&(Y?(J(le),le=-1):Y=!0,Me(K,ae-xe))):(z.sortIndex=j,a(g,z),U||C||(U=!0,W||(W=!0,Ae()))),z},s.unstable_shouldYield=ge,s.unstable_wrapCallback=function(z){var G=x;return function(){var ae=x;x=G;try{return z.apply(this,arguments)}finally{x=ae}}}}(Lu)),Lu}var Np;function ub(){return Np||(Np=1,zu.exports=cb()),zu.exports}var Pu={exports:{}},Ut={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rp;function db(){if(Rp)return Ut;Rp=1;var s=fo();function a(g){var v="https://react.dev/errors/"+g;if(1<arguments.length){v+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)v+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+g+"; visit "+v+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var l={d:{f:r,r:function(){throw Error(a(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},o=Symbol.for("react.portal");function u(g,v,y){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:S==null?null:""+S,children:g,containerInfo:v,implementation:y}}var f=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function m(g,v){if(g==="font")return"";if(typeof v=="string")return v==="use-credentials"?v:""}return Ut.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,Ut.createPortal=function(g,v){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!v||v.nodeType!==1&&v.nodeType!==9&&v.nodeType!==11)throw Error(a(299));return u(g,v,null,y)},Ut.flushSync=function(g){var v=f.T,y=l.p;try{if(f.T=null,l.p=2,g)return g()}finally{f.T=v,l.p=y,l.d.f()}},Ut.preconnect=function(g,v){typeof g=="string"&&(v?(v=v.crossOrigin,v=typeof v=="string"?v==="use-credentials"?v:"":void 0):v=null,l.d.C(g,v))},Ut.prefetchDNS=function(g){typeof g=="string"&&l.d.D(g)},Ut.preinit=function(g,v){if(typeof g=="string"&&v&&typeof v.as=="string"){var y=v.as,S=m(y,v.crossOrigin),x=typeof v.integrity=="string"?v.integrity:void 0,C=typeof v.fetchPriority=="string"?v.fetchPriority:void 0;y==="style"?l.d.S(g,typeof v.precedence=="string"?v.precedence:void 0,{crossOrigin:S,integrity:x,fetchPriority:C}):y==="script"&&l.d.X(g,{crossOrigin:S,integrity:x,fetchPriority:C,nonce:typeof v.nonce=="string"?v.nonce:void 0})}},Ut.preinitModule=function(g,v){if(typeof g=="string")if(typeof v=="object"&&v!==null){if(v.as==null||v.as==="script"){var y=m(v.as,v.crossOrigin);l.d.M(g,{crossOrigin:y,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0})}}else v==null&&l.d.M(g)},Ut.preload=function(g,v){if(typeof g=="string"&&typeof v=="object"&&v!==null&&typeof v.as=="string"){var y=v.as,S=m(y,v.crossOrigin);l.d.L(g,y,{crossOrigin:S,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0,type:typeof v.type=="string"?v.type:void 0,fetchPriority:typeof v.fetchPriority=="string"?v.fetchPriority:void 0,referrerPolicy:typeof v.referrerPolicy=="string"?v.referrerPolicy:void 0,imageSrcSet:typeof v.imageSrcSet=="string"?v.imageSrcSet:void 0,imageSizes:typeof v.imageSizes=="string"?v.imageSizes:void 0,media:typeof v.media=="string"?v.media:void 0})}},Ut.preloadModule=function(g,v){if(typeof g=="string")if(v){var y=m(v.as,v.crossOrigin);l.d.m(g,{as:typeof v.as=="string"&&v.as!=="script"?v.as:void 0,crossOrigin:y,integrity:typeof v.integrity=="string"?v.integrity:void 0})}else l.d.m(g)},Ut.requestFormReset=function(g){l.d.r(g)},Ut.unstable_batchedUpdates=function(g,v){return g(v)},Ut.useFormState=function(g,v,y){return f.H.useFormState(g,v,y)},Ut.useFormStatus=function(){return f.H.useHostTransitionStatus()},Ut.version="19.1.0",Ut}var Mp;function Ig(){if(Mp)return Pu.exports;Mp=1;function s(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(s)}catch(a){console.error(a)}}return s(),Pu.exports=db(),Pu.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Up;function fb(){if(Up)return or;Up=1;var s=ub(),a=fo(),r=Ig();function l(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function u(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function f(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function m(e){if(u(e)!==e)throw Error(l(188))}function g(e){var t=e.alternate;if(!t){if(t=u(e),t===null)throw Error(l(188));return t!==e?null:e}for(var n=e,i=t;;){var c=n.return;if(c===null)break;var d=c.alternate;if(d===null){if(i=c.return,i!==null){n=i;continue}break}if(c.child===d.child){for(d=c.child;d;){if(d===n)return m(c),e;if(d===i)return m(c),t;d=d.sibling}throw Error(l(188))}if(n.return!==i.return)n=c,i=d;else{for(var p=!1,b=c.child;b;){if(b===n){p=!0,n=c,i=d;break}if(b===i){p=!0,i=c,n=d;break}b=b.sibling}if(!p){for(b=d.child;b;){if(b===n){p=!0,n=d,i=c;break}if(b===i){p=!0,i=d,n=c;break}b=b.sibling}if(!p)throw Error(l(189))}}if(n.alternate!==i)throw Error(l(190))}if(n.tag!==3)throw Error(l(188));return n.stateNode.current===n?e:t}function v(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=v(e),t!==null)return t;e=e.sibling}return null}var y=Object.assign,S=Symbol.for("react.element"),x=Symbol.for("react.transitional.element"),C=Symbol.for("react.portal"),U=Symbol.for("react.fragment"),Y=Symbol.for("react.strict_mode"),V=Symbol.for("react.profiler"),F=Symbol.for("react.provider"),J=Symbol.for("react.consumer"),q=Symbol.for("react.context"),te=Symbol.for("react.forward_ref"),K=Symbol.for("react.suspense"),W=Symbol.for("react.suspense_list"),le=Symbol.for("react.memo"),ne=Symbol.for("react.lazy"),ye=Symbol.for("react.activity"),ge=Symbol.for("react.memo_cache_sentinel"),ue=Symbol.iterator;function Ae(e){return e===null||typeof e!="object"?null:(e=ue&&e[ue]||e["@@iterator"],typeof e=="function"?e:null)}var Le=Symbol.for("react.client.reference");function Qe(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Le?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case U:return"Fragment";case V:return"Profiler";case Y:return"StrictMode";case K:return"Suspense";case W:return"SuspenseList";case ye:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case C:return"Portal";case q:return(e.displayName||"Context")+".Provider";case J:return(e._context.displayName||"Context")+".Consumer";case te:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case le:return t=e.displayName||null,t!==null?t:Qe(e.type)||"Memo";case ne:t=e._payload,e=e._init;try{return Qe(e(t))}catch(n){}}return null}var Me=Array.isArray,z=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ae={pending:!1,data:null,method:null,action:null},xe=[],j=-1;function I(e){return{current:e}}function X(e){0>j||(e.current=xe[j],xe[j]=null,j--)}function Z(e,t){j++,xe[j]=e.current,e.current=t}var re=I(null),ve=I(null),de=I(null),oe=I(null);function Pe(e,t){switch(Z(de,t),Z(ve,e),Z(re,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Zm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Zm(t),e=Jm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}X(re),Z(re,e)}function Bt(){X(re),X(ve),X(de)}function an(e){e.memoizedState!==null&&Z(oe,e);var t=re.current,n=Jm(t,e.type);t!==n&&(Z(ve,e),Z(re,n))}function vn(e){ve.current===e&&(X(re),X(ve)),oe.current===e&&(X(oe),nr._currentValue=ae)}var ea=Object.prototype.hasOwnProperty,Ne=s.unstable_scheduleCallback,Oe=s.unstable_cancelCallback,rt=s.unstable_shouldYield,ze=s.unstable_requestPaint,Ze=s.unstable_now,Xt=s.unstable_getCurrentPriorityLevel,nt=s.unstable_ImmediatePriority,ut=s.unstable_UserBlockingPriority,Rt=s.unstable_NormalPriority,Da=s.unstable_LowPriority,ta=s.unstable_IdlePriority,ci=s.log,Ct=s.unstable_setDisableYieldValue,bt=null,ht=null;function sn(e){if(typeof ci=="function"&&Ct(e),ht&&typeof ht.setStrictMode=="function")try{ht.setStrictMode(bt,e)}catch(t){}}var mt=Math.clz32?Math.clz32:jo,So=Math.log,wo=Math.LN2;function jo(e){return e>>>=0,e===0?32:31-(So(e)/wo|0)|0}var ui=256,di=4194304;function Un(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function za(e,t,n){var i=e.pendingLanes;if(i===0)return 0;var c=0,d=e.suspendedLanes,p=e.pingedLanes;e=e.warmLanes;var b=i&134217727;return b!==0?(i=b&~d,i!==0?c=Un(i):(p&=b,p!==0?c=Un(p):n||(n=b&~e,n!==0&&(c=Un(n))))):(b=i&~d,b!==0?c=Un(b):p!==0?c=Un(p):n||(n=i&~e,n!==0&&(c=Un(n)))),c===0?0:t!==0&&t!==c&&(t&d)===0&&(d=c&-c,n=t&-t,d>=n||d===32&&(n&4194048)!==0)?t:c}function La(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function us(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Tr(){var e=ui;return ui<<=1,(ui&4194048)===0&&(ui=256),e}function Cr(){var e=di;return di<<=1,(di&62914560)===0&&(di=4194304),e}function Dn(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Pa(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Eo(e,t,n,i,c,d){var p=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var b=e.entanglements,w=e.expirationTimes,O=e.hiddenUpdates;for(n=p&~n;0<n;){var B=31-mt(n),$=1<<B;b[B]=0,w[B]=-1;var R=O[B];if(R!==null)for(O[B]=null,B=0;B<R.length;B++){var M=R[B];M!==null&&(M.lane&=-536870913)}n&=~$}i!==0&&Ar(e,i,0),d!==0&&c===0&&e.tag!==0&&(e.suspendedLanes|=d&~(p&~t))}function Ar(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var i=31-mt(t);e.entangledLanes|=t,e.entanglements[i]=e.entanglements[i]|1073741824|n&4194090}function Or(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var i=31-mt(n),c=1<<i;c&t|e[i]&t&&(e[i]|=t),n&=~c}}function fi(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function hi(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Nr(){var e=G.p;return e!==0?e:(e=window.event,e===void 0?32:vp(e.type))}function xo(e,t){var n=G.p;try{return G.p=e,t()}finally{G.p=n}}var kn=Math.random().toString(36).slice(2),_t="__reactFiber$"+kn,St="__reactProps$"+kn,yn="__reactContainer$"+kn,ds="__reactEvents$"+kn,ko="__reactListeners$"+kn,To="__reactHandles$"+kn,fs="__reactResources$"+kn,Ba="__reactMarker$"+kn;function hs(e){delete e[_t],delete e[St],delete e[ds],delete e[ko],delete e[To]}function na(e){var t=e[_t];if(t)return t;for(var n=e.parentNode;n;){if(t=n[yn]||n[_t]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=np(e);e!==null;){if(n=e[_t])return n;e=np(e)}return t}e=n,n=e.parentNode}return null}function aa(e){if(e=e[_t]||e[yn]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function qa(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(l(33))}function _(e){var t=e[fs];return t||(t=e[fs]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function N(e){e[Ba]=!0}var D=new Set,L={};function Q(e,t){he(e,t),he(e+"Capture",t)}function he(e,t){for(L[e]=t,e=0;e<t.length;e++)D.add(t[e])}var we=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Je={},pt={};function wt(e){return ea.call(pt,e)?!0:ea.call(Je,e)?!1:we.test(e)?pt[e]=!0:(Je[e]=!0,!1)}function Be(e,t,n){if(wt(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var i=t.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function qt(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function bn(e,t,n,i){if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+i)}}var Tn,Ht;function ia(e){if(Tn===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Tn=t&&t[1]||"",Ht=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Tn+e+Ht}var ms=!1;function Co(e,t){if(!e||ms)return"";ms=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(t){var $=function(){throw Error()};if(Object.defineProperty($.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct($,[])}catch(M){var R=M}Reflect.construct(e,[],$)}else{try{$.call()}catch(M){R=M}e.call($.prototype)}}else{try{throw Error()}catch(M){R=M}($=e())&&typeof $.catch=="function"&&$.catch(function(){})}}catch(M){if(M&&R&&typeof M.stack=="string")return[M.stack,R.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var c=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");c&&c.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var d=i.DetermineComponentFrameRoot(),p=d[0],b=d[1];if(p&&b){var w=p.split(`
`),O=b.split(`
`);for(c=i=0;i<w.length&&!w[i].includes("DetermineComponentFrameRoot");)i++;for(;c<O.length&&!O[c].includes("DetermineComponentFrameRoot");)c++;if(i===w.length||c===O.length)for(i=w.length-1,c=O.length-1;1<=i&&0<=c&&w[i]!==O[c];)c--;for(;1<=i&&0<=c;i--,c--)if(w[i]!==O[c]){if(i!==1||c!==1)do if(i--,c--,0>c||w[i]!==O[c]){var B=`
`+w[i].replace(" at new "," at ");return e.displayName&&B.includes("<anonymous>")&&(B=B.replace("<anonymous>",e.displayName)),B}while(1<=i&&0<=c);break}}}finally{ms=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?ia(n):""}function Xv(e){switch(e.tag){case 26:case 27:case 5:return ia(e.type);case 16:return ia("Lazy");case 13:return ia("Suspense");case 19:return ia("SuspenseList");case 0:case 15:return Co(e.type,!1);case 11:return Co(e.type.render,!1);case 1:return Co(e.type,!0);case 31:return ia("Activity");default:return""}}function Gd(e){try{var t="";do t+=Xv(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function rn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Vd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Qv(e){var t=Vd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof n!="undefined"&&typeof n.get=="function"&&typeof n.set=="function"){var c=n.get,d=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return c.call(this)},set:function(p){i=""+p,d.call(this,p)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(p){i=""+p},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Rr(e){e._valueTracker||(e._valueTracker=Qv(e))}function Yd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),i="";return e&&(i=Vd(e)?e.checked?"true":"false":e.value),e=i,e!==n?(t.setValue(e),!0):!1}function Mr(e){if(e=e||(typeof document!="undefined"?document:void 0),typeof e=="undefined")return null;try{return e.activeElement||e.body}catch(t){return e.body}}var Zv=/[\n"\\]/g;function ln(e){return e.replace(Zv,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Ao(e,t,n,i,c,d,p,b){e.name="",p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.type=p:e.removeAttribute("type"),t!=null?p==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+rn(t)):e.value!==""+rn(t)&&(e.value=""+rn(t)):p!=="submit"&&p!=="reset"||e.removeAttribute("value"),t!=null?Oo(e,p,rn(t)):n!=null?Oo(e,p,rn(n)):i!=null&&e.removeAttribute("value"),c==null&&d!=null&&(e.defaultChecked=!!d),c!=null&&(e.checked=c&&typeof c!="function"&&typeof c!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?e.name=""+rn(b):e.removeAttribute("name")}function Fd(e,t,n,i,c,d,p,b){if(d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.type=d),t!=null||n!=null){if(!(d!=="submit"&&d!=="reset"||t!=null))return;n=n!=null?""+rn(n):"",t=t!=null?""+rn(t):n,b||t===e.value||(e.value=t),e.defaultValue=t}i=i!=null?i:c,i=typeof i!="function"&&typeof i!="symbol"&&!!i,e.checked=b?e.checked:!!i,e.defaultChecked=!!i,p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"&&(e.name=p)}function Oo(e,t,n){t==="number"&&Mr(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function mi(e,t,n,i){if(e=e.options,t){t={};for(var c=0;c<n.length;c++)t["$"+n[c]]=!0;for(n=0;n<e.length;n++)c=t.hasOwnProperty("$"+e[n].value),e[n].selected!==c&&(e[n].selected=c),c&&i&&(e[n].defaultSelected=!0)}else{for(n=""+rn(n),t=null,c=0;c<e.length;c++){if(e[c].value===n){e[c].selected=!0,i&&(e[c].defaultSelected=!0);return}t!==null||e[c].disabled||(t=e[c])}t!==null&&(t.selected=!0)}}function Kd(e,t,n){if(t!=null&&(t=""+rn(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+rn(n):""}function Xd(e,t,n,i){if(t==null){if(i!=null){if(n!=null)throw Error(l(92));if(Me(i)){if(1<i.length)throw Error(l(93));i=i[0]}n=i}n==null&&(n=""),t=n}n=rn(t),e.defaultValue=n,i=e.textContent,i===n&&i!==""&&i!==null&&(e.value=i)}function pi(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Jv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Qd(e,t,n){var i=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":i?e.setProperty(t,n):typeof n!="number"||n===0||Jv.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Zd(e,t,n){if(t!=null&&typeof t!="object")throw Error(l(62));if(e=e.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||t!=null&&t.hasOwnProperty(i)||(i.indexOf("--")===0?e.setProperty(i,""):i==="float"?e.cssFloat="":e[i]="");for(var c in t)i=t[c],t.hasOwnProperty(c)&&n[c]!==i&&Qd(e,c,i)}else for(var d in t)t.hasOwnProperty(d)&&Qd(e,d,t[d])}function No(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Wv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),ey=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ur(e){return ey.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ro=null;function Mo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var gi=null,vi=null;function Jd(e){var t=aa(e);if(t&&(e=t.stateNode)){var n=e[St]||null;e:switch(e=t.stateNode,t.type){case"input":if(Ao(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ln(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var i=n[t];if(i!==e&&i.form===e.form){var c=i[St]||null;if(!c)throw Error(l(90));Ao(i,c.value,c.defaultValue,c.defaultValue,c.checked,c.defaultChecked,c.type,c.name)}}for(t=0;t<n.length;t++)i=n[t],i.form===e.form&&Yd(i)}break e;case"textarea":Kd(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&mi(e,!!n.multiple,t,!1)}}}var Uo=!1;function Wd(e,t,n){if(Uo)return e(t,n);Uo=!0;try{var i=e(t);return i}finally{if(Uo=!1,(gi!==null||vi!==null)&&(bl(),gi&&(t=gi,e=vi,vi=gi=null,Jd(t),e)))for(t=0;t<e.length;t++)Jd(e[t])}}function ps(e,t){var n=e.stateNode;if(n===null)return null;var i=n[St]||null;if(i===null)return null;n=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(l(231,t,typeof n));return n}var zn=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),Do=!1;if(zn)try{var gs={};Object.defineProperty(gs,"passive",{get:function(){Do=!0}}),window.addEventListener("test",gs,gs),window.removeEventListener("test",gs,gs)}catch(e){Do=!1}var sa=null,zo=null,Dr=null;function ef(){if(Dr)return Dr;var e,t=zo,n=t.length,i,c="value"in sa?sa.value:sa.textContent,d=c.length;for(e=0;e<n&&t[e]===c[e];e++);var p=n-e;for(i=1;i<=p&&t[n-i]===c[d-i];i++);return Dr=c.slice(e,1<i?1-i:void 0)}function zr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Lr(){return!0}function tf(){return!1}function It(e){function t(n,i,c,d,p){this._reactName=n,this._targetInst=c,this.type=i,this.nativeEvent=d,this.target=p,this.currentTarget=null;for(var b in e)e.hasOwnProperty(b)&&(n=e[b],this[b]=n?n(d):d[b]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Lr:tf,this.isPropagationStopped=tf,this}return y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Lr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Lr)},persist:function(){},isPersistent:Lr}),t}var Ha={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Pr=It(Ha),vs=y({},Ha,{view:0,detail:0}),ty=It(vs),Lo,Po,ys,Br=y({},vs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:qo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ys&&(ys&&e.type==="mousemove"?(Lo=e.screenX-ys.screenX,Po=e.screenY-ys.screenY):Po=Lo=0,ys=e),Lo)},movementY:function(e){return"movementY"in e?e.movementY:Po}}),nf=It(Br),ny=y({},Br,{dataTransfer:0}),ay=It(ny),iy=y({},vs,{relatedTarget:0}),Bo=It(iy),sy=y({},Ha,{animationName:0,elapsedTime:0,pseudoElement:0}),ry=It(sy),ly=y({},Ha,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),oy=It(ly),cy=y({},Ha,{data:0}),af=It(cy),uy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function hy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=fy[e])?!!t[e]:!1}function qo(){return hy}var my=y({},vs,{key:function(e){if(e.key){var t=uy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=zr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?dy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:qo,charCode:function(e){return e.type==="keypress"?zr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?zr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),py=It(my),gy=y({},Br,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),sf=It(gy),vy=y({},vs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:qo}),yy=It(vy),by=y({},Ha,{propertyName:0,elapsedTime:0,pseudoElement:0}),_y=It(by),Sy=y({},Br,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),wy=It(Sy),jy=y({},Ha,{newState:0,oldState:0}),Ey=It(jy),xy=[9,13,27,32],Ho=zn&&"CompositionEvent"in window,bs=null;zn&&"documentMode"in document&&(bs=document.documentMode);var ky=zn&&"TextEvent"in window&&!bs,rf=zn&&(!Ho||bs&&8<bs&&11>=bs),lf=" ",of=!1;function cf(e,t){switch(e){case"keyup":return xy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function uf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var yi=!1;function Ty(e,t){switch(e){case"compositionend":return uf(t);case"keypress":return t.which!==32?null:(of=!0,lf);case"textInput":return e=t.data,e===lf&&of?null:e;default:return null}}function Cy(e,t){if(yi)return e==="compositionend"||!Ho&&cf(e,t)?(e=ef(),Dr=zo=sa=null,yi=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return rf&&t.locale!=="ko"?null:t.data;default:return null}}var Ay={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function df(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ay[e.type]:t==="textarea"}function ff(e,t,n,i){gi?vi?vi.push(i):vi=[i]:gi=i,t=xl(t,"onChange"),0<t.length&&(n=new Pr("onChange","change",null,n,i),e.push({event:n,listeners:t}))}var _s=null,Ss=null;function Oy(e){Ym(e,0)}function qr(e){var t=qa(e);if(Yd(t))return e}function hf(e,t){if(e==="change")return t}var mf=!1;if(zn){var Io;if(zn){var $o="oninput"in document;if(!$o){var pf=document.createElement("div");pf.setAttribute("oninput","return;"),$o=typeof pf.oninput=="function"}Io=$o}else Io=!1;mf=Io&&(!document.documentMode||9<document.documentMode)}function gf(){_s&&(_s.detachEvent("onpropertychange",vf),Ss=_s=null)}function vf(e){if(e.propertyName==="value"&&qr(Ss)){var t=[];ff(t,Ss,e,Mo(e)),Wd(Oy,t)}}function Ny(e,t,n){e==="focusin"?(gf(),_s=t,Ss=n,_s.attachEvent("onpropertychange",vf)):e==="focusout"&&gf()}function Ry(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return qr(Ss)}function My(e,t){if(e==="click")return qr(t)}function Uy(e,t){if(e==="input"||e==="change")return qr(t)}function Dy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Qt=typeof Object.is=="function"?Object.is:Dy;function ws(e,t){if(Qt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var c=n[i];if(!ea.call(t,c)||!Qt(e[c],t[c]))return!1}return!0}function yf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function bf(e,t){var n=yf(e);e=0;for(var i;n;){if(n.nodeType===3){if(i=e+n.textContent.length,e<=t&&i>=t)return{node:n,offset:t-e};e=i}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=yf(n)}}function _f(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?_f(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Sf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Mr(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch(i){n=!1}if(n)e=t.contentWindow;else break;t=Mr(e.document)}return t}function Go(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var zy=zn&&"documentMode"in document&&11>=document.documentMode,bi=null,Vo=null,js=null,Yo=!1;function wf(e,t,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Yo||bi==null||bi!==Mr(i)||(i=bi,"selectionStart"in i&&Go(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),js&&ws(js,i)||(js=i,i=xl(Vo,"onSelect"),0<i.length&&(t=new Pr("onSelect","select",null,t,n),e.push({event:t,listeners:i}),t.target=bi)))}function Ia(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var _i={animationend:Ia("Animation","AnimationEnd"),animationiteration:Ia("Animation","AnimationIteration"),animationstart:Ia("Animation","AnimationStart"),transitionrun:Ia("Transition","TransitionRun"),transitionstart:Ia("Transition","TransitionStart"),transitioncancel:Ia("Transition","TransitionCancel"),transitionend:Ia("Transition","TransitionEnd")},Fo={},jf={};zn&&(jf=document.createElement("div").style,"AnimationEvent"in window||(delete _i.animationend.animation,delete _i.animationiteration.animation,delete _i.animationstart.animation),"TransitionEvent"in window||delete _i.transitionend.transition);function $a(e){if(Fo[e])return Fo[e];if(!_i[e])return e;var t=_i[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in jf)return Fo[e]=t[n];return e}var Ef=$a("animationend"),xf=$a("animationiteration"),kf=$a("animationstart"),Ly=$a("transitionrun"),Py=$a("transitionstart"),By=$a("transitioncancel"),Tf=$a("transitionend"),Cf=new Map,Ko="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ko.push("scrollEnd");function _n(e,t){Cf.set(e,t),Q(t,[e])}var Af=new WeakMap;function on(e,t){if(typeof e=="object"&&e!==null){var n=Af.get(e);return n!==void 0?n:(t={value:e,source:t,stack:Gd(t)},Af.set(e,t),t)}return{value:e,source:t,stack:Gd(t)}}var cn=[],Si=0,Xo=0;function Hr(){for(var e=Si,t=Xo=Si=0;t<e;){var n=cn[t];cn[t++]=null;var i=cn[t];cn[t++]=null;var c=cn[t];cn[t++]=null;var d=cn[t];if(cn[t++]=null,i!==null&&c!==null){var p=i.pending;p===null?c.next=c:(c.next=p.next,p.next=c),i.pending=c}d!==0&&Of(n,c,d)}}function Ir(e,t,n,i){cn[Si++]=e,cn[Si++]=t,cn[Si++]=n,cn[Si++]=i,Xo|=i,e.lanes|=i,e=e.alternate,e!==null&&(e.lanes|=i)}function Qo(e,t,n,i){return Ir(e,t,n,i),$r(e)}function wi(e,t){return Ir(e,null,null,t),$r(e)}function Of(e,t,n){e.lanes|=n;var i=e.alternate;i!==null&&(i.lanes|=n);for(var c=!1,d=e.return;d!==null;)d.childLanes|=n,i=d.alternate,i!==null&&(i.childLanes|=n),d.tag===22&&(e=d.stateNode,e===null||e._visibility&1||(c=!0)),e=d,d=d.return;return e.tag===3?(d=e.stateNode,c&&t!==null&&(c=31-mt(n),e=d.hiddenUpdates,i=e[c],i===null?e[c]=[t]:i.push(t),t.lane=n|536870912),d):null}function $r(e){if(50<Ks)throw Ks=0,nu=null,Error(l(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var ji={};function qy(e,t,n,i){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Zt(e,t,n,i){return new qy(e,t,n,i)}function Zo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ln(e,t){var n=e.alternate;return n===null?(n=Zt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Nf(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Gr(e,t,n,i,c,d){var p=0;if(i=e,typeof e=="function")Zo(e)&&(p=1);else if(typeof e=="string")p=I0(e,n,re.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ye:return e=Zt(31,n,t,c),e.elementType=ye,e.lanes=d,e;case U:return Ga(n.children,c,d,t);case Y:p=8,c|=24;break;case V:return e=Zt(12,n,t,c|2),e.elementType=V,e.lanes=d,e;case K:return e=Zt(13,n,t,c),e.elementType=K,e.lanes=d,e;case W:return e=Zt(19,n,t,c),e.elementType=W,e.lanes=d,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case F:case q:p=10;break e;case J:p=9;break e;case te:p=11;break e;case le:p=14;break e;case ne:p=16,i=null;break e}p=29,n=Error(l(130,e===null?"null":typeof e,"")),i=null}return t=Zt(p,n,t,c),t.elementType=e,t.type=i,t.lanes=d,t}function Ga(e,t,n,i){return e=Zt(7,e,i,t),e.lanes=n,e}function Jo(e,t,n){return e=Zt(6,e,null,t),e.lanes=n,e}function Wo(e,t,n){return t=Zt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ei=[],xi=0,Vr=null,Yr=0,un=[],dn=0,Va=null,Pn=1,Bn="";function Ya(e,t){Ei[xi++]=Yr,Ei[xi++]=Vr,Vr=e,Yr=t}function Rf(e,t,n){un[dn++]=Pn,un[dn++]=Bn,un[dn++]=Va,Va=e;var i=Pn;e=Bn;var c=32-mt(i)-1;i&=~(1<<c),n+=1;var d=32-mt(t)+c;if(30<d){var p=c-c%5;d=(i&(1<<p)-1).toString(32),i>>=p,c-=p,Pn=1<<32-mt(t)+c|n<<c|i,Bn=d+e}else Pn=1<<d|n<<c|i,Bn=e}function ec(e){e.return!==null&&(Ya(e,1),Rf(e,1,0))}function tc(e){for(;e===Vr;)Vr=Ei[--xi],Ei[xi]=null,Yr=Ei[--xi],Ei[xi]=null;for(;e===Va;)Va=un[--dn],un[dn]=null,Bn=un[--dn],un[dn]=null,Pn=un[--dn],un[dn]=null}var zt=null,at=null,De=!1,Fa=null,Cn=!1,nc=Error(l(519));function Ka(e){var t=Error(l(418,""));throw ks(on(t,e)),nc}function Mf(e){var t=e.stateNode,n=e.type,i=e.memoizedProps;switch(t[_t]=e,t[St]=i,n){case"dialog":Te("cancel",t),Te("close",t);break;case"iframe":case"object":case"embed":Te("load",t);break;case"video":case"audio":for(n=0;n<Qs.length;n++)Te(Qs[n],t);break;case"source":Te("error",t);break;case"img":case"image":case"link":Te("error",t),Te("load",t);break;case"details":Te("toggle",t);break;case"input":Te("invalid",t),Fd(t,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),Rr(t);break;case"select":Te("invalid",t);break;case"textarea":Te("invalid",t),Xd(t,i.value,i.defaultValue,i.children),Rr(t)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||i.suppressHydrationWarning===!0||Qm(t.textContent,n)?(i.popover!=null&&(Te("beforetoggle",t),Te("toggle",t)),i.onScroll!=null&&Te("scroll",t),i.onScrollEnd!=null&&Te("scrollend",t),i.onClick!=null&&(t.onclick=kl),t=!0):t=!1,t||Ka(e)}function Uf(e){for(zt=e.return;zt;)switch(zt.tag){case 5:case 13:Cn=!1;return;case 27:case 3:Cn=!0;return;default:zt=zt.return}}function Es(e){if(e!==zt)return!1;if(!De)return Uf(e),De=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||yu(e.type,e.memoizedProps)),n=!n),n&&at&&Ka(e),Uf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){at=wn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}at=null}}else t===27?(t=at,Sa(e.type)?(e=wu,wu=null,at=e):at=t):at=zt?wn(e.stateNode.nextSibling):null;return!0}function xs(){at=zt=null,De=!1}function Df(){var e=Fa;return e!==null&&(Vt===null?Vt=e:Vt.push.apply(Vt,e),Fa=null),e}function ks(e){Fa===null?Fa=[e]:Fa.push(e)}var ac=I(null),Xa=null,qn=null;function ra(e,t,n){Z(ac,t._currentValue),t._currentValue=n}function Hn(e){e._currentValue=ac.current,X(ac)}function ic(e,t,n){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===n)break;e=e.return}}function sc(e,t,n,i){var c=e.child;for(c!==null&&(c.return=e);c!==null;){var d=c.dependencies;if(d!==null){var p=c.child;d=d.firstContext;e:for(;d!==null;){var b=d;d=c;for(var w=0;w<t.length;w++)if(b.context===t[w]){d.lanes|=n,b=d.alternate,b!==null&&(b.lanes|=n),ic(d.return,n,e),i||(p=null);break e}d=b.next}}else if(c.tag===18){if(p=c.return,p===null)throw Error(l(341));p.lanes|=n,d=p.alternate,d!==null&&(d.lanes|=n),ic(p,n,e),p=null}else p=c.child;if(p!==null)p.return=c;else for(p=c;p!==null;){if(p===e){p=null;break}if(c=p.sibling,c!==null){c.return=p.return,p=c;break}p=p.return}c=p}}function Ts(e,t,n,i){e=null;for(var c=t,d=!1;c!==null;){if(!d){if((c.flags&524288)!==0)d=!0;else if((c.flags&262144)!==0)break}if(c.tag===10){var p=c.alternate;if(p===null)throw Error(l(387));if(p=p.memoizedProps,p!==null){var b=c.type;Qt(c.pendingProps.value,p.value)||(e!==null?e.push(b):e=[b])}}else if(c===oe.current){if(p=c.alternate,p===null)throw Error(l(387));p.memoizedState.memoizedState!==c.memoizedState.memoizedState&&(e!==null?e.push(nr):e=[nr])}c=c.return}e!==null&&sc(t,e,n,i),t.flags|=262144}function Fr(e){for(e=e.firstContext;e!==null;){if(!Qt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Qa(e){Xa=e,qn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Mt(e){return zf(Xa,e)}function Kr(e,t){return Xa===null&&Qa(e),zf(e,t)}function zf(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},qn===null){if(e===null)throw Error(l(308));qn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else qn=qn.next=t;return n}var Hy=typeof AbortController!="undefined"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,i){e.push(i)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},Iy=s.unstable_scheduleCallback,$y=s.unstable_NormalPriority,gt={$$typeof:q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function rc(){return{controller:new Hy,data:new Map,refCount:0}}function Cs(e){e.refCount--,e.refCount===0&&Iy($y,function(){e.controller.abort()})}var As=null,lc=0,ki=0,Ti=null;function Gy(e,t){if(As===null){var n=As=[];lc=0,ki=cu(),Ti={status:"pending",value:void 0,then:function(i){n.push(i)}}}return lc++,t.then(Lf,Lf),t}function Lf(){if(--lc===0&&As!==null){Ti!==null&&(Ti.status="fulfilled");var e=As;As=null,ki=0,Ti=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Vy(e,t){var n=[],i={status:"pending",value:null,reason:null,then:function(c){n.push(c)}};return e.then(function(){i.status="fulfilled",i.value=t;for(var c=0;c<n.length;c++)(0,n[c])(t)},function(c){for(i.status="rejected",i.reason=c,c=0;c<n.length;c++)(0,n[c])(void 0)}),i}var Pf=z.S;z.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Gy(e,t),Pf!==null&&Pf(e,t)};var Za=I(null);function oc(){var e=Za.current;return e!==null?e:Ke.pooledCache}function Xr(e,t){t===null?Z(Za,Za.current):Z(Za,t.pool)}function Bf(){var e=oc();return e===null?null:{parent:gt._currentValue,pool:e}}var Os=Error(l(460)),qf=Error(l(474)),Qr=Error(l(542)),cc={then:function(){}};function Hf(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Zr(){}function If(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Zr,Zr),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Gf(e),e;default:if(typeof t.status=="string")t.then(Zr,Zr);else{if(e=Ke,e!==null&&100<e.shellSuspendCounter)throw Error(l(482));e=t,e.status="pending",e.then(function(i){if(t.status==="pending"){var c=t;c.status="fulfilled",c.value=i}},function(i){if(t.status==="pending"){var c=t;c.status="rejected",c.reason=i}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Gf(e),e}throw Ns=t,Os}}var Ns=null;function $f(){if(Ns===null)throw Error(l(459));var e=Ns;return Ns=null,e}function Gf(e){if(e===Os||e===Qr)throw Error(l(483))}var la=!1;function uc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function dc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function oa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ca(e,t,n){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(qe&2)!==0){var c=i.pending;return c===null?t.next=t:(t.next=c.next,c.next=t),i.pending=t,t=$r(e),Of(e,null,n),t}return Ir(e,i,t,n),$r(e)}function Rs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,Or(e,n)}}function fc(e,t){var n=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var c=null,d=null;if(n=n.firstBaseUpdate,n!==null){do{var p={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};d===null?c=d=p:d=d.next=p,n=n.next}while(n!==null);d===null?c=d=t:d=d.next=t}else c=d=t;n={baseState:i.baseState,firstBaseUpdate:c,lastBaseUpdate:d,shared:i.shared,callbacks:i.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var hc=!1;function Ms(){if(hc){var e=Ti;if(e!==null)throw e}}function Us(e,t,n,i){hc=!1;var c=e.updateQueue;la=!1;var d=c.firstBaseUpdate,p=c.lastBaseUpdate,b=c.shared.pending;if(b!==null){c.shared.pending=null;var w=b,O=w.next;w.next=null,p===null?d=O:p.next=O,p=w;var B=e.alternate;B!==null&&(B=B.updateQueue,b=B.lastBaseUpdate,b!==p&&(b===null?B.firstBaseUpdate=O:b.next=O,B.lastBaseUpdate=w))}if(d!==null){var $=c.baseState;p=0,B=O=w=null,b=d;do{var R=b.lane&-536870913,M=R!==b.lane;if(M?(Re&R)===R:(i&R)===R){R!==0&&R===ki&&(hc=!0),B!==null&&(B=B.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});e:{var pe=e,fe=b;R=t;var Ge=n;switch(fe.tag){case 1:if(pe=fe.payload,typeof pe=="function"){$=pe.call(Ge,$,R);break e}$=pe;break e;case 3:pe.flags=pe.flags&-65537|128;case 0:if(pe=fe.payload,R=typeof pe=="function"?pe.call(Ge,$,R):pe,R==null)break e;$=y({},$,R);break e;case 2:la=!0}}R=b.callback,R!==null&&(e.flags|=64,M&&(e.flags|=8192),M=c.callbacks,M===null?c.callbacks=[R]:M.push(R))}else M={lane:R,tag:b.tag,payload:b.payload,callback:b.callback,next:null},B===null?(O=B=M,w=$):B=B.next=M,p|=R;if(b=b.next,b===null){if(b=c.shared.pending,b===null)break;M=b,b=M.next,M.next=null,c.lastBaseUpdate=M,c.shared.pending=null}}while(!0);B===null&&(w=$),c.baseState=w,c.firstBaseUpdate=O,c.lastBaseUpdate=B,d===null&&(c.shared.lanes=0),va|=p,e.lanes=p,e.memoizedState=$}}function Vf(e,t){if(typeof e!="function")throw Error(l(191,e));e.call(t)}function Yf(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)Vf(n[e],t)}var Ci=I(null),Jr=I(0);function Ff(e,t){e=Kn,Z(Jr,e),Z(Ci,t),Kn=e|t.baseLanes}function mc(){Z(Jr,Kn),Z(Ci,Ci.current)}function pc(){Kn=Jr.current,X(Ci),X(Jr)}var ua=0,je=null,Ie=null,dt=null,Wr=!1,Ai=!1,Ja=!1,el=0,Ds=0,Oi=null,Yy=0;function lt(){throw Error(l(321))}function gc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Qt(e[n],t[n]))return!1;return!0}function vc(e,t,n,i,c,d){return ua=d,je=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,z.H=e===null||e.memoizedState===null?Oh:Nh,Ja=!1,d=n(i,c),Ja=!1,Ai&&(d=Xf(t,n,i,c)),Kf(e),d}function Kf(e){z.H=rl;var t=Ie!==null&&Ie.next!==null;if(ua=0,dt=Ie=je=null,Wr=!1,Ds=0,Oi=null,t)throw Error(l(300));e===null||jt||(e=e.dependencies,e!==null&&Fr(e)&&(jt=!0))}function Xf(e,t,n,i){je=e;var c=0;do{if(Ai&&(Oi=null),Ds=0,Ai=!1,25<=c)throw Error(l(301));if(c+=1,dt=Ie=null,e.updateQueue!=null){var d=e.updateQueue;d.lastEffect=null,d.events=null,d.stores=null,d.memoCache!=null&&(d.memoCache.index=0)}z.H=Wy,d=t(n,i)}while(Ai);return d}function Fy(){var e=z.H,t=e.useState()[0];return t=typeof t.then=="function"?zs(t):t,e=e.useState()[0],(Ie!==null?Ie.memoizedState:null)!==e&&(je.flags|=1024),t}function yc(){var e=el!==0;return el=0,e}function bc(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function _c(e){if(Wr){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Wr=!1}ua=0,dt=Ie=je=null,Ai=!1,Ds=el=0,Oi=null}function $t(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return dt===null?je.memoizedState=dt=e:dt=dt.next=e,dt}function ft(){if(Ie===null){var e=je.alternate;e=e!==null?e.memoizedState:null}else e=Ie.next;var t=dt===null?je.memoizedState:dt.next;if(t!==null)dt=t,Ie=e;else{if(e===null)throw je.alternate===null?Error(l(467)):Error(l(310));Ie=e,e={memoizedState:Ie.memoizedState,baseState:Ie.baseState,baseQueue:Ie.baseQueue,queue:Ie.queue,next:null},dt===null?je.memoizedState=dt=e:dt=dt.next=e}return dt}function Sc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function zs(e){var t=Ds;return Ds+=1,Oi===null&&(Oi=[]),e=If(Oi,e,t),t=je,(dt===null?t.memoizedState:dt.next)===null&&(t=t.alternate,z.H=t===null||t.memoizedState===null?Oh:Nh),e}function tl(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return zs(e);if(e.$$typeof===q)return Mt(e)}throw Error(l(438,String(e)))}function wc(e){var t=null,n=je.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var i=je.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(t={data:i.data.map(function(c){return c.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Sc(),je.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),i=0;i<e;i++)n[i]=ge;return t.index++,n}function In(e,t){return typeof t=="function"?t(e):t}function nl(e){var t=ft();return jc(t,Ie,e)}function jc(e,t,n){var i=e.queue;if(i===null)throw Error(l(311));i.lastRenderedReducer=n;var c=e.baseQueue,d=i.pending;if(d!==null){if(c!==null){var p=c.next;c.next=d.next,d.next=p}t.baseQueue=c=d,i.pending=null}if(d=e.baseState,c===null)e.memoizedState=d;else{t=c.next;var b=p=null,w=null,O=t,B=!1;do{var $=O.lane&-536870913;if($!==O.lane?(Re&$)===$:(ua&$)===$){var R=O.revertLane;if(R===0)w!==null&&(w=w.next={lane:0,revertLane:0,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null}),$===ki&&(B=!0);else if((ua&R)===R){O=O.next,R===ki&&(B=!0);continue}else $={lane:0,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},w===null?(b=w=$,p=d):w=w.next=$,je.lanes|=R,va|=R;$=O.action,Ja&&n(d,$),d=O.hasEagerState?O.eagerState:n(d,$)}else R={lane:$,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},w===null?(b=w=R,p=d):w=w.next=R,je.lanes|=$,va|=$;O=O.next}while(O!==null&&O!==t);if(w===null?p=d:w.next=b,!Qt(d,e.memoizedState)&&(jt=!0,B&&(n=Ti,n!==null)))throw n;e.memoizedState=d,e.baseState=p,e.baseQueue=w,i.lastRenderedState=d}return c===null&&(i.lanes=0),[e.memoizedState,i.dispatch]}function Ec(e){var t=ft(),n=t.queue;if(n===null)throw Error(l(311));n.lastRenderedReducer=e;var i=n.dispatch,c=n.pending,d=t.memoizedState;if(c!==null){n.pending=null;var p=c=c.next;do d=e(d,p.action),p=p.next;while(p!==c);Qt(d,t.memoizedState)||(jt=!0),t.memoizedState=d,t.baseQueue===null&&(t.baseState=d),n.lastRenderedState=d}return[d,i]}function Qf(e,t,n){var i=je,c=ft(),d=De;if(d){if(n===void 0)throw Error(l(407));n=n()}else n=t();var p=!Qt((Ie||c).memoizedState,n);p&&(c.memoizedState=n,jt=!0),c=c.queue;var b=Wf.bind(null,i,c,e);if(Ls(2048,8,b,[e]),c.getSnapshot!==t||p||dt!==null&&dt.memoizedState.tag&1){if(i.flags|=2048,Ni(9,al(),Jf.bind(null,i,c,n,t),null),Ke===null)throw Error(l(349));d||(ua&124)!==0||Zf(i,t,n)}return n}function Zf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=je.updateQueue,t===null?(t=Sc(),je.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Jf(e,t,n,i){t.value=n,t.getSnapshot=i,eh(t)&&th(e)}function Wf(e,t,n){return n(function(){eh(t)&&th(e)})}function eh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Qt(e,n)}catch(i){return!0}}function th(e){var t=wi(e,2);t!==null&&nn(t,e,2)}function xc(e){var t=$t();if(typeof e=="function"){var n=e;if(e=n(),Ja){sn(!0);try{n()}finally{sn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:In,lastRenderedState:e},t}function nh(e,t,n,i){return e.baseState=n,jc(e,Ie,typeof i=="function"?i:In)}function Ky(e,t,n,i,c){if(sl(e))throw Error(l(485));if(e=t.action,e!==null){var d={payload:c,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(p){d.listeners.push(p)}};z.T!==null?n(!0):d.isTransition=!1,i(d),n=t.pending,n===null?(d.next=t.pending=d,ah(t,d)):(d.next=n.next,t.pending=n.next=d)}}function ah(e,t){var n=t.action,i=t.payload,c=e.state;if(t.isTransition){var d=z.T,p={};z.T=p;try{var b=n(c,i),w=z.S;w!==null&&w(p,b),ih(e,t,b)}catch(O){kc(e,t,O)}finally{z.T=d}}else try{d=n(c,i),ih(e,t,d)}catch(O){kc(e,t,O)}}function ih(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){sh(e,t,i)},function(i){return kc(e,t,i)}):sh(e,t,n)}function sh(e,t,n){t.status="fulfilled",t.value=n,rh(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,ah(e,n)))}function kc(e,t,n){var i=e.pending;if(e.pending=null,i!==null){i=i.next;do t.status="rejected",t.reason=n,rh(t),t=t.next;while(t!==i)}e.action=null}function rh(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function lh(e,t){return t}function oh(e,t){if(De){var n=Ke.formState;if(n!==null){e:{var i=je;if(De){if(at){t:{for(var c=at,d=Cn;c.nodeType!==8;){if(!d){c=null;break t}if(c=wn(c.nextSibling),c===null){c=null;break t}}d=c.data,c=d==="F!"||d==="F"?c:null}if(c){at=wn(c.nextSibling),i=c.data==="F!";break e}}Ka(i)}i=!1}i&&(t=n[0])}}return n=$t(),n.memoizedState=n.baseState=t,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:lh,lastRenderedState:t},n.queue=i,n=Th.bind(null,je,i),i.dispatch=n,i=xc(!1),d=Nc.bind(null,je,!1,i.queue),i=$t(),c={state:t,dispatch:null,action:e,pending:null},i.queue=c,n=Ky.bind(null,je,c,d,n),c.dispatch=n,i.memoizedState=e,[t,n,!1]}function ch(e){var t=ft();return uh(t,Ie,e)}function uh(e,t,n){if(t=jc(e,t,lh)[0],e=nl(In)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var i=zs(t)}catch(p){throw p===Os?Qr:p}else i=t;t=ft();var c=t.queue,d=c.dispatch;return n!==t.memoizedState&&(je.flags|=2048,Ni(9,al(),Xy.bind(null,c,n),null)),[i,d,e]}function Xy(e,t){e.action=t}function dh(e){var t=ft(),n=Ie;if(n!==null)return uh(t,n,e);ft(),t=t.memoizedState,n=ft();var i=n.queue.dispatch;return n.memoizedState=e,[t,i,!1]}function Ni(e,t,n,i){return e={tag:e,create:n,deps:i,inst:t,next:null},t=je.updateQueue,t===null&&(t=Sc(),je.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(i=n.next,n.next=e,e.next=i,t.lastEffect=e),e}function al(){return{destroy:void 0,resource:void 0}}function fh(){return ft().memoizedState}function il(e,t,n,i){var c=$t();i=i===void 0?null:i,je.flags|=e,c.memoizedState=Ni(1|t,al(),n,i)}function Ls(e,t,n,i){var c=ft();i=i===void 0?null:i;var d=c.memoizedState.inst;Ie!==null&&i!==null&&gc(i,Ie.memoizedState.deps)?c.memoizedState=Ni(t,d,n,i):(je.flags|=e,c.memoizedState=Ni(1|t,d,n,i))}function hh(e,t){il(8390656,8,e,t)}function mh(e,t){Ls(2048,8,e,t)}function ph(e,t){return Ls(4,2,e,t)}function gh(e,t){return Ls(4,4,e,t)}function vh(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function yh(e,t,n){n=n!=null?n.concat([e]):null,Ls(4,4,vh.bind(null,t,e),n)}function Tc(){}function bh(e,t){var n=ft();t=t===void 0?null:t;var i=n.memoizedState;return t!==null&&gc(t,i[1])?i[0]:(n.memoizedState=[e,t],e)}function _h(e,t){var n=ft();t=t===void 0?null:t;var i=n.memoizedState;if(t!==null&&gc(t,i[1]))return i[0];if(i=e(),Ja){sn(!0);try{e()}finally{sn(!1)}}return n.memoizedState=[i,t],i}function Cc(e,t,n){return n===void 0||(ua&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=jm(),je.lanes|=e,va|=e,n)}function Sh(e,t,n,i){return Qt(n,t)?n:Ci.current!==null?(e=Cc(e,n,i),Qt(e,t)||(jt=!0),e):(ua&42)===0?(jt=!0,e.memoizedState=n):(e=jm(),je.lanes|=e,va|=e,t)}function wh(e,t,n,i,c){var d=G.p;G.p=d!==0&&8>d?d:8;var p=z.T,b={};z.T=b,Nc(e,!1,t,n);try{var w=c(),O=z.S;if(O!==null&&O(b,w),w!==null&&typeof w=="object"&&typeof w.then=="function"){var B=Vy(w,i);Ps(e,t,B,tn(e))}else Ps(e,t,i,tn(e))}catch($){Ps(e,t,{then:function(){},status:"rejected",reason:$},tn())}finally{G.p=d,z.T=p}}function Qy(){}function Ac(e,t,n,i){if(e.tag!==5)throw Error(l(476));var c=jh(e).queue;wh(e,c,t,ae,n===null?Qy:function(){return Eh(e),n(i)})}function jh(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ae,baseState:ae,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:In,lastRenderedState:ae},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:In,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Eh(e){var t=jh(e).next.queue;Ps(e,t,{},tn())}function Oc(){return Mt(nr)}function xh(){return ft().memoizedState}function kh(){return ft().memoizedState}function Zy(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=tn();e=oa(n);var i=ca(t,e,n);i!==null&&(nn(i,t,n),Rs(i,t,n)),t={cache:rc()},e.payload=t;return}t=t.return}}function Jy(e,t,n){var i=tn();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},sl(e)?Ch(t,n):(n=Qo(e,t,n,i),n!==null&&(nn(n,e,i),Ah(n,t,i)))}function Th(e,t,n){var i=tn();Ps(e,t,n,i)}function Ps(e,t,n,i){var c={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(sl(e))Ch(t,c);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=t.lastRenderedReducer,d!==null))try{var p=t.lastRenderedState,b=d(p,n);if(c.hasEagerState=!0,c.eagerState=b,Qt(b,p))return Ir(e,t,c,0),Ke===null&&Hr(),!1}catch(w){}finally{}if(n=Qo(e,t,c,i),n!==null)return nn(n,e,i),Ah(n,t,i),!0}return!1}function Nc(e,t,n,i){if(i={lane:2,revertLane:cu(),action:i,hasEagerState:!1,eagerState:null,next:null},sl(e)){if(t)throw Error(l(479))}else t=Qo(e,n,i,2),t!==null&&nn(t,e,2)}function sl(e){var t=e.alternate;return e===je||t!==null&&t===je}function Ch(e,t){Ai=Wr=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ah(e,t,n){if((n&4194048)!==0){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,Or(e,n)}}var rl={readContext:Mt,use:tl,useCallback:lt,useContext:lt,useEffect:lt,useImperativeHandle:lt,useLayoutEffect:lt,useInsertionEffect:lt,useMemo:lt,useReducer:lt,useRef:lt,useState:lt,useDebugValue:lt,useDeferredValue:lt,useTransition:lt,useSyncExternalStore:lt,useId:lt,useHostTransitionStatus:lt,useFormState:lt,useActionState:lt,useOptimistic:lt,useMemoCache:lt,useCacheRefresh:lt},Oh={readContext:Mt,use:tl,useCallback:function(e,t){return $t().memoizedState=[e,t===void 0?null:t],e},useContext:Mt,useEffect:hh,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,il(4194308,4,vh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return il(4194308,4,e,t)},useInsertionEffect:function(e,t){il(4,2,e,t)},useMemo:function(e,t){var n=$t();t=t===void 0?null:t;var i=e();if(Ja){sn(!0);try{e()}finally{sn(!1)}}return n.memoizedState=[i,t],i},useReducer:function(e,t,n){var i=$t();if(n!==void 0){var c=n(t);if(Ja){sn(!0);try{n(t)}finally{sn(!1)}}}else c=t;return i.memoizedState=i.baseState=c,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:c},i.queue=e,e=e.dispatch=Jy.bind(null,je,e),[i.memoizedState,e]},useRef:function(e){var t=$t();return e={current:e},t.memoizedState=e},useState:function(e){e=xc(e);var t=e.queue,n=Th.bind(null,je,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Tc,useDeferredValue:function(e,t){var n=$t();return Cc(n,e,t)},useTransition:function(){var e=xc(!1);return e=wh.bind(null,je,e.queue,!0,!1),$t().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var i=je,c=$t();if(De){if(n===void 0)throw Error(l(407));n=n()}else{if(n=t(),Ke===null)throw Error(l(349));(Re&124)!==0||Zf(i,t,n)}c.memoizedState=n;var d={value:n,getSnapshot:t};return c.queue=d,hh(Wf.bind(null,i,d,e),[e]),i.flags|=2048,Ni(9,al(),Jf.bind(null,i,d,n,t),null),n},useId:function(){var e=$t(),t=Ke.identifierPrefix;if(De){var n=Bn,i=Pn;n=(i&~(1<<32-mt(i)-1)).toString(32)+n,t="«"+t+"R"+n,n=el++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=Yy++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Oc,useFormState:oh,useActionState:oh,useOptimistic:function(e){var t=$t();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Nc.bind(null,je,!0,n),n.dispatch=t,[e,t]},useMemoCache:wc,useCacheRefresh:function(){return $t().memoizedState=Zy.bind(null,je)}},Nh={readContext:Mt,use:tl,useCallback:bh,useContext:Mt,useEffect:mh,useImperativeHandle:yh,useInsertionEffect:ph,useLayoutEffect:gh,useMemo:_h,useReducer:nl,useRef:fh,useState:function(){return nl(In)},useDebugValue:Tc,useDeferredValue:function(e,t){var n=ft();return Sh(n,Ie.memoizedState,e,t)},useTransition:function(){var e=nl(In)[0],t=ft().memoizedState;return[typeof e=="boolean"?e:zs(e),t]},useSyncExternalStore:Qf,useId:xh,useHostTransitionStatus:Oc,useFormState:ch,useActionState:ch,useOptimistic:function(e,t){var n=ft();return nh(n,Ie,e,t)},useMemoCache:wc,useCacheRefresh:kh},Wy={readContext:Mt,use:tl,useCallback:bh,useContext:Mt,useEffect:mh,useImperativeHandle:yh,useInsertionEffect:ph,useLayoutEffect:gh,useMemo:_h,useReducer:Ec,useRef:fh,useState:function(){return Ec(In)},useDebugValue:Tc,useDeferredValue:function(e,t){var n=ft();return Ie===null?Cc(n,e,t):Sh(n,Ie.memoizedState,e,t)},useTransition:function(){var e=Ec(In)[0],t=ft().memoizedState;return[typeof e=="boolean"?e:zs(e),t]},useSyncExternalStore:Qf,useId:xh,useHostTransitionStatus:Oc,useFormState:dh,useActionState:dh,useOptimistic:function(e,t){var n=ft();return Ie!==null?nh(n,Ie,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:wc,useCacheRefresh:kh},Ri=null,Bs=0;function ll(e){var t=Bs;return Bs+=1,Ri===null&&(Ri=[]),If(Ri,e,t)}function qs(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function ol(e,t){throw t.$$typeof===S?Error(l(525)):(e=Object.prototype.toString.call(t),Error(l(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Rh(e){var t=e._init;return t(e._payload)}function Mh(e){function t(T,k){if(e){var A=T.deletions;A===null?(T.deletions=[k],T.flags|=16):A.push(k)}}function n(T,k){if(!e)return null;for(;k!==null;)t(T,k),k=k.sibling;return null}function i(T){for(var k=new Map;T!==null;)T.key!==null?k.set(T.key,T):k.set(T.index,T),T=T.sibling;return k}function c(T,k){return T=Ln(T,k),T.index=0,T.sibling=null,T}function d(T,k,A){return T.index=A,e?(A=T.alternate,A!==null?(A=A.index,A<k?(T.flags|=67108866,k):A):(T.flags|=67108866,k)):(T.flags|=1048576,k)}function p(T){return e&&T.alternate===null&&(T.flags|=67108866),T}function b(T,k,A,H){return k===null||k.tag!==6?(k=Jo(A,T.mode,H),k.return=T,k):(k=c(k,A),k.return=T,k)}function w(T,k,A,H){var ie=A.type;return ie===U?B(T,k,A.props.children,H,A.key):k!==null&&(k.elementType===ie||typeof ie=="object"&&ie!==null&&ie.$$typeof===ne&&Rh(ie)===k.type)?(k=c(k,A.props),qs(k,A),k.return=T,k):(k=Gr(A.type,A.key,A.props,null,T.mode,H),qs(k,A),k.return=T,k)}function O(T,k,A,H){return k===null||k.tag!==4||k.stateNode.containerInfo!==A.containerInfo||k.stateNode.implementation!==A.implementation?(k=Wo(A,T.mode,H),k.return=T,k):(k=c(k,A.children||[]),k.return=T,k)}function B(T,k,A,H,ie){return k===null||k.tag!==7?(k=Ga(A,T.mode,H,ie),k.return=T,k):(k=c(k,A),k.return=T,k)}function $(T,k,A){if(typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint")return k=Jo(""+k,T.mode,A),k.return=T,k;if(typeof k=="object"&&k!==null){switch(k.$$typeof){case x:return A=Gr(k.type,k.key,k.props,null,T.mode,A),qs(A,k),A.return=T,A;case C:return k=Wo(k,T.mode,A),k.return=T,k;case ne:var H=k._init;return k=H(k._payload),$(T,k,A)}if(Me(k)||Ae(k))return k=Ga(k,T.mode,A,null),k.return=T,k;if(typeof k.then=="function")return $(T,ll(k),A);if(k.$$typeof===q)return $(T,Kr(T,k),A);ol(T,k)}return null}function R(T,k,A,H){var ie=k!==null?k.key:null;if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return ie!==null?null:b(T,k,""+A,H);if(typeof A=="object"&&A!==null){switch(A.$$typeof){case x:return A.key===ie?w(T,k,A,H):null;case C:return A.key===ie?O(T,k,A,H):null;case ne:return ie=A._init,A=ie(A._payload),R(T,k,A,H)}if(Me(A)||Ae(A))return ie!==null?null:B(T,k,A,H,null);if(typeof A.then=="function")return R(T,k,ll(A),H);if(A.$$typeof===q)return R(T,k,Kr(T,A),H);ol(T,A)}return null}function M(T,k,A,H,ie){if(typeof H=="string"&&H!==""||typeof H=="number"||typeof H=="bigint")return T=T.get(A)||null,b(k,T,""+H,ie);if(typeof H=="object"&&H!==null){switch(H.$$typeof){case x:return T=T.get(H.key===null?A:H.key)||null,w(k,T,H,ie);case C:return T=T.get(H.key===null?A:H.key)||null,O(k,T,H,ie);case ne:var Ee=H._init;return H=Ee(H._payload),M(T,k,A,H,ie)}if(Me(H)||Ae(H))return T=T.get(A)||null,B(k,T,H,ie,null);if(typeof H.then=="function")return M(T,k,A,ll(H),ie);if(H.$$typeof===q)return M(T,k,A,Kr(k,H),ie);ol(k,H)}return null}function pe(T,k,A,H){for(var ie=null,Ee=null,ce=k,me=k=0,xt=null;ce!==null&&me<A.length;me++){ce.index>me?(xt=ce,ce=null):xt=ce.sibling;var Ue=R(T,ce,A[me],H);if(Ue===null){ce===null&&(ce=xt);break}e&&ce&&Ue.alternate===null&&t(T,ce),k=d(Ue,k,me),Ee===null?ie=Ue:Ee.sibling=Ue,Ee=Ue,ce=xt}if(me===A.length)return n(T,ce),De&&Ya(T,me),ie;if(ce===null){for(;me<A.length;me++)ce=$(T,A[me],H),ce!==null&&(k=d(ce,k,me),Ee===null?ie=ce:Ee.sibling=ce,Ee=ce);return De&&Ya(T,me),ie}for(ce=i(ce);me<A.length;me++)xt=M(ce,T,me,A[me],H),xt!==null&&(e&&xt.alternate!==null&&ce.delete(xt.key===null?me:xt.key),k=d(xt,k,me),Ee===null?ie=xt:Ee.sibling=xt,Ee=xt);return e&&ce.forEach(function(ka){return t(T,ka)}),De&&Ya(T,me),ie}function fe(T,k,A,H){if(A==null)throw Error(l(151));for(var ie=null,Ee=null,ce=k,me=k=0,xt=null,Ue=A.next();ce!==null&&!Ue.done;me++,Ue=A.next()){ce.index>me?(xt=ce,ce=null):xt=ce.sibling;var ka=R(T,ce,Ue.value,H);if(ka===null){ce===null&&(ce=xt);break}e&&ce&&ka.alternate===null&&t(T,ce),k=d(ka,k,me),Ee===null?ie=ka:Ee.sibling=ka,Ee=ka,ce=xt}if(Ue.done)return n(T,ce),De&&Ya(T,me),ie;if(ce===null){for(;!Ue.done;me++,Ue=A.next())Ue=$(T,Ue.value,H),Ue!==null&&(k=d(Ue,k,me),Ee===null?ie=Ue:Ee.sibling=Ue,Ee=Ue);return De&&Ya(T,me),ie}for(ce=i(ce);!Ue.done;me++,Ue=A.next())Ue=M(ce,T,me,Ue.value,H),Ue!==null&&(e&&Ue.alternate!==null&&ce.delete(Ue.key===null?me:Ue.key),k=d(Ue,k,me),Ee===null?ie=Ue:Ee.sibling=Ue,Ee=Ue);return e&&ce.forEach(function(eb){return t(T,eb)}),De&&Ya(T,me),ie}function Ge(T,k,A,H){if(typeof A=="object"&&A!==null&&A.type===U&&A.key===null&&(A=A.props.children),typeof A=="object"&&A!==null){switch(A.$$typeof){case x:e:{for(var ie=A.key;k!==null;){if(k.key===ie){if(ie=A.type,ie===U){if(k.tag===7){n(T,k.sibling),H=c(k,A.props.children),H.return=T,T=H;break e}}else if(k.elementType===ie||typeof ie=="object"&&ie!==null&&ie.$$typeof===ne&&Rh(ie)===k.type){n(T,k.sibling),H=c(k,A.props),qs(H,A),H.return=T,T=H;break e}n(T,k);break}else t(T,k);k=k.sibling}A.type===U?(H=Ga(A.props.children,T.mode,H,A.key),H.return=T,T=H):(H=Gr(A.type,A.key,A.props,null,T.mode,H),qs(H,A),H.return=T,T=H)}return p(T);case C:e:{for(ie=A.key;k!==null;){if(k.key===ie)if(k.tag===4&&k.stateNode.containerInfo===A.containerInfo&&k.stateNode.implementation===A.implementation){n(T,k.sibling),H=c(k,A.children||[]),H.return=T,T=H;break e}else{n(T,k);break}else t(T,k);k=k.sibling}H=Wo(A,T.mode,H),H.return=T,T=H}return p(T);case ne:return ie=A._init,A=ie(A._payload),Ge(T,k,A,H)}if(Me(A))return pe(T,k,A,H);if(Ae(A)){if(ie=Ae(A),typeof ie!="function")throw Error(l(150));return A=ie.call(A),fe(T,k,A,H)}if(typeof A.then=="function")return Ge(T,k,ll(A),H);if(A.$$typeof===q)return Ge(T,k,Kr(T,A),H);ol(T,A)}return typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint"?(A=""+A,k!==null&&k.tag===6?(n(T,k.sibling),H=c(k,A),H.return=T,T=H):(n(T,k),H=Jo(A,T.mode,H),H.return=T,T=H),p(T)):n(T,k)}return function(T,k,A,H){try{Bs=0;var ie=Ge(T,k,A,H);return Ri=null,ie}catch(ce){if(ce===Os||ce===Qr)throw ce;var Ee=Zt(29,ce,null,T.mode);return Ee.lanes=H,Ee.return=T,Ee}finally{}}}var Mi=Mh(!0),Uh=Mh(!1),fn=I(null),An=null;function da(e){var t=e.alternate;Z(vt,vt.current&1),Z(fn,e),An===null&&(t===null||Ci.current!==null||t.memoizedState!==null)&&(An=e)}function Dh(e){if(e.tag===22){if(Z(vt,vt.current),Z(fn,e),An===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(An=e)}}else fa()}function fa(){Z(vt,vt.current),Z(fn,fn.current)}function $n(e){X(fn),An===e&&(An=null),X(vt)}var vt=I(0);function cl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Su(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Rc(e,t,n,i){t=e.memoizedState,n=n(i,t),n=n==null?t:y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Mc={enqueueSetState:function(e,t,n){e=e._reactInternals;var i=tn(),c=oa(i);c.payload=t,n!=null&&(c.callback=n),t=ca(e,c,i),t!==null&&(nn(t,e,i),Rs(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var i=tn(),c=oa(i);c.tag=1,c.payload=t,n!=null&&(c.callback=n),t=ca(e,c,i),t!==null&&(nn(t,e,i),Rs(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=tn(),i=oa(n);i.tag=2,t!=null&&(i.callback=t),t=ca(e,i,n),t!==null&&(nn(t,e,n),Rs(t,e,n))}};function zh(e,t,n,i,c,d,p){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,d,p):t.prototype&&t.prototype.isPureReactComponent?!ws(n,i)||!ws(c,d):!0}function Lh(e,t,n,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,i),t.state!==e&&Mc.enqueueReplaceState(t,t.state,null)}function Wa(e,t){var n=t;if("ref"in t){n={};for(var i in t)i!=="ref"&&(n[i]=t[i])}if(e=e.defaultProps){n===t&&(n=y({},n));for(var c in e)n[c]===void 0&&(n[c]=e[c])}return n}var ul=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Ph(e){ul(e)}function Bh(e){console.error(e)}function qh(e){ul(e)}function dl(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(i){setTimeout(function(){throw i})}}function Hh(e,t,n){try{var i=e.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(c){setTimeout(function(){throw c})}}function Uc(e,t,n){return n=oa(n),n.tag=3,n.payload={element:null},n.callback=function(){dl(e,t)},n}function Ih(e){return e=oa(e),e.tag=3,e}function $h(e,t,n,i){var c=n.type.getDerivedStateFromError;if(typeof c=="function"){var d=i.value;e.payload=function(){return c(d)},e.callback=function(){Hh(t,n,i)}}var p=n.stateNode;p!==null&&typeof p.componentDidCatch=="function"&&(e.callback=function(){Hh(t,n,i),typeof c!="function"&&(ya===null?ya=new Set([this]):ya.add(this));var b=i.stack;this.componentDidCatch(i.value,{componentStack:b!==null?b:""})})}function e0(e,t,n,i,c){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(t=n.alternate,t!==null&&Ts(t,n,c,!0),n=fn.current,n!==null){switch(n.tag){case 13:return An===null?iu():n.alternate===null&&it===0&&(it=3),n.flags&=-257,n.flags|=65536,n.lanes=c,i===cc?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([i]):t.add(i),ru(e,i,c)),!1;case 22:return n.flags|=65536,i===cc?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([i]):n.add(i)),ru(e,i,c)),!1}throw Error(l(435,n.tag))}return ru(e,i,c),iu(),!1}if(De)return t=fn.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=c,i!==nc&&(e=Error(l(422),{cause:i}),ks(on(e,n)))):(i!==nc&&(t=Error(l(423),{cause:i}),ks(on(t,n))),e=e.current.alternate,e.flags|=65536,c&=-c,e.lanes|=c,i=on(i,n),c=Uc(e.stateNode,i,c),fc(e,c),it!==4&&(it=2)),!1;var d=Error(l(520),{cause:i});if(d=on(d,n),Fs===null?Fs=[d]:Fs.push(d),it!==4&&(it=2),t===null)return!0;i=on(i,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=c&-c,n.lanes|=e,e=Uc(n.stateNode,i,e),fc(n,e),!1;case 1:if(t=n.type,d=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||d!==null&&typeof d.componentDidCatch=="function"&&(ya===null||!ya.has(d))))return n.flags|=65536,c&=-c,n.lanes|=c,c=Ih(c),$h(c,e,n,i),fc(n,c),!1}n=n.return}while(n!==null);return!1}var Gh=Error(l(461)),jt=!1;function At(e,t,n,i){t.child=e===null?Uh(t,null,n,i):Mi(t,e.child,n,i)}function Vh(e,t,n,i,c){n=n.render;var d=t.ref;if("ref"in i){var p={};for(var b in i)b!=="ref"&&(p[b]=i[b])}else p=i;return Qa(t),i=vc(e,t,n,p,d,c),b=yc(),e!==null&&!jt?(bc(e,t,c),Gn(e,t,c)):(De&&b&&ec(t),t.flags|=1,At(e,t,i,c),t.child)}function Yh(e,t,n,i,c){if(e===null){var d=n.type;return typeof d=="function"&&!Zo(d)&&d.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=d,Fh(e,t,d,i,c)):(e=Gr(n.type,null,i,t,t.mode,c),e.ref=t.ref,e.return=t,t.child=e)}if(d=e.child,!Ic(e,c)){var p=d.memoizedProps;if(n=n.compare,n=n!==null?n:ws,n(p,i)&&e.ref===t.ref)return Gn(e,t,c)}return t.flags|=1,e=Ln(d,i),e.ref=t.ref,e.return=t,t.child=e}function Fh(e,t,n,i,c){if(e!==null){var d=e.memoizedProps;if(ws(d,i)&&e.ref===t.ref)if(jt=!1,t.pendingProps=i=d,Ic(e,c))(e.flags&131072)!==0&&(jt=!0);else return t.lanes=e.lanes,Gn(e,t,c)}return Dc(e,t,n,i,c)}function Kh(e,t,n){var i=t.pendingProps,c=i.children,d=e!==null?e.memoizedState:null;if(i.mode==="hidden"){if((t.flags&128)!==0){if(i=d!==null?d.baseLanes|n:n,e!==null){for(c=t.child=e.child,d=0;c!==null;)d=d|c.lanes|c.childLanes,c=c.sibling;t.childLanes=d&~i}else t.childLanes=0,t.child=null;return Xh(e,t,i,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Xr(t,d!==null?d.cachePool:null),d!==null?Ff(t,d):mc(),Dh(t);else return t.lanes=t.childLanes=536870912,Xh(e,t,d!==null?d.baseLanes|n:n,n)}else d!==null?(Xr(t,d.cachePool),Ff(t,d),fa(),t.memoizedState=null):(e!==null&&Xr(t,null),mc(),fa());return At(e,t,c,n),t.child}function Xh(e,t,n,i){var c=oc();return c=c===null?null:{parent:gt._currentValue,pool:c},t.memoizedState={baseLanes:n,cachePool:c},e!==null&&Xr(t,null),mc(),Dh(t),e!==null&&Ts(e,t,i,!0),null}function fl(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(l(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function Dc(e,t,n,i,c){return Qa(t),n=vc(e,t,n,i,void 0,c),i=yc(),e!==null&&!jt?(bc(e,t,c),Gn(e,t,c)):(De&&i&&ec(t),t.flags|=1,At(e,t,n,c),t.child)}function Qh(e,t,n,i,c,d){return Qa(t),t.updateQueue=null,n=Xf(t,i,n,c),Kf(e),i=yc(),e!==null&&!jt?(bc(e,t,d),Gn(e,t,d)):(De&&i&&ec(t),t.flags|=1,At(e,t,n,d),t.child)}function Zh(e,t,n,i,c){if(Qa(t),t.stateNode===null){var d=ji,p=n.contextType;typeof p=="object"&&p!==null&&(d=Mt(p)),d=new n(i,d),t.memoizedState=d.state!==null&&d.state!==void 0?d.state:null,d.updater=Mc,t.stateNode=d,d._reactInternals=t,d=t.stateNode,d.props=i,d.state=t.memoizedState,d.refs={},uc(t),p=n.contextType,d.context=typeof p=="object"&&p!==null?Mt(p):ji,d.state=t.memoizedState,p=n.getDerivedStateFromProps,typeof p=="function"&&(Rc(t,n,p,i),d.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof d.getSnapshotBeforeUpdate=="function"||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(p=d.state,typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount(),p!==d.state&&Mc.enqueueReplaceState(d,d.state,null),Us(t,i,d,c),Ms(),d.state=t.memoizedState),typeof d.componentDidMount=="function"&&(t.flags|=4194308),i=!0}else if(e===null){d=t.stateNode;var b=t.memoizedProps,w=Wa(n,b);d.props=w;var O=d.context,B=n.contextType;p=ji,typeof B=="object"&&B!==null&&(p=Mt(B));var $=n.getDerivedStateFromProps;B=typeof $=="function"||typeof d.getSnapshotBeforeUpdate=="function",b=t.pendingProps!==b,B||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(b||O!==p)&&Lh(t,d,i,p),la=!1;var R=t.memoizedState;d.state=R,Us(t,i,d,c),Ms(),O=t.memoizedState,b||R!==O||la?(typeof $=="function"&&(Rc(t,n,$,i),O=t.memoizedState),(w=la||zh(t,n,w,i,R,O,p))?(B||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount()),typeof d.componentDidMount=="function"&&(t.flags|=4194308)):(typeof d.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=O),d.props=i,d.state=O,d.context=p,i=w):(typeof d.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{d=t.stateNode,dc(e,t),p=t.memoizedProps,B=Wa(n,p),d.props=B,$=t.pendingProps,R=d.context,O=n.contextType,w=ji,typeof O=="object"&&O!==null&&(w=Mt(O)),b=n.getDerivedStateFromProps,(O=typeof b=="function"||typeof d.getSnapshotBeforeUpdate=="function")||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(p!==$||R!==w)&&Lh(t,d,i,w),la=!1,R=t.memoizedState,d.state=R,Us(t,i,d,c),Ms();var M=t.memoizedState;p!==$||R!==M||la||e!==null&&e.dependencies!==null&&Fr(e.dependencies)?(typeof b=="function"&&(Rc(t,n,b,i),M=t.memoizedState),(B=la||zh(t,n,B,i,R,M,w)||e!==null&&e.dependencies!==null&&Fr(e.dependencies))?(O||typeof d.UNSAFE_componentWillUpdate!="function"&&typeof d.componentWillUpdate!="function"||(typeof d.componentWillUpdate=="function"&&d.componentWillUpdate(i,M,w),typeof d.UNSAFE_componentWillUpdate=="function"&&d.UNSAFE_componentWillUpdate(i,M,w)),typeof d.componentDidUpdate=="function"&&(t.flags|=4),typeof d.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof d.componentDidUpdate!="function"||p===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||p===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=M),d.props=i,d.state=M,d.context=w,i=B):(typeof d.componentDidUpdate!="function"||p===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||p===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),i=!1)}return d=i,fl(e,t),i=(t.flags&128)!==0,d||i?(d=t.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:d.render(),t.flags|=1,e!==null&&i?(t.child=Mi(t,e.child,null,c),t.child=Mi(t,null,n,c)):At(e,t,n,c),t.memoizedState=d.state,e=t.child):e=Gn(e,t,c),e}function Jh(e,t,n,i){return xs(),t.flags|=256,At(e,t,n,i),t.child}var zc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Lc(e){return{baseLanes:e,cachePool:Bf()}}function Pc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=hn),e}function Wh(e,t,n){var i=t.pendingProps,c=!1,d=(t.flags&128)!==0,p;if((p=d)||(p=e!==null&&e.memoizedState===null?!1:(vt.current&2)!==0),p&&(c=!0,t.flags&=-129),p=(t.flags&32)!==0,t.flags&=-33,e===null){if(De){if(c?da(t):fa(),De){var b=at,w;if(w=b){e:{for(w=b,b=Cn;w.nodeType!==8;){if(!b){b=null;break e}if(w=wn(w.nextSibling),w===null){b=null;break e}}b=w}b!==null?(t.memoizedState={dehydrated:b,treeContext:Va!==null?{id:Pn,overflow:Bn}:null,retryLane:536870912,hydrationErrors:null},w=Zt(18,null,null,0),w.stateNode=b,w.return=t,t.child=w,zt=t,at=null,w=!0):w=!1}w||Ka(t)}if(b=t.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return Su(b)?t.lanes=32:t.lanes=536870912,null;$n(t)}return b=i.children,i=i.fallback,c?(fa(),c=t.mode,b=hl({mode:"hidden",children:b},c),i=Ga(i,c,n,null),b.return=t,i.return=t,b.sibling=i,t.child=b,c=t.child,c.memoizedState=Lc(n),c.childLanes=Pc(e,p,n),t.memoizedState=zc,i):(da(t),Bc(t,b))}if(w=e.memoizedState,w!==null&&(b=w.dehydrated,b!==null)){if(d)t.flags&256?(da(t),t.flags&=-257,t=qc(e,t,n)):t.memoizedState!==null?(fa(),t.child=e.child,t.flags|=128,t=null):(fa(),c=i.fallback,b=t.mode,i=hl({mode:"visible",children:i.children},b),c=Ga(c,b,n,null),c.flags|=2,i.return=t,c.return=t,i.sibling=c,t.child=i,Mi(t,e.child,null,n),i=t.child,i.memoizedState=Lc(n),i.childLanes=Pc(e,p,n),t.memoizedState=zc,t=c);else if(da(t),Su(b)){if(p=b.nextSibling&&b.nextSibling.dataset,p)var O=p.dgst;p=O,i=Error(l(419)),i.stack="",i.digest=p,ks({value:i,source:null,stack:null}),t=qc(e,t,n)}else if(jt||Ts(e,t,n,!1),p=(n&e.childLanes)!==0,jt||p){if(p=Ke,p!==null&&(i=n&-n,i=(i&42)!==0?1:fi(i),i=(i&(p.suspendedLanes|n))!==0?0:i,i!==0&&i!==w.retryLane))throw w.retryLane=i,wi(e,i),nn(p,e,i),Gh;b.data==="$?"||iu(),t=qc(e,t,n)}else b.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=w.treeContext,at=wn(b.nextSibling),zt=t,De=!0,Fa=null,Cn=!1,e!==null&&(un[dn++]=Pn,un[dn++]=Bn,un[dn++]=Va,Pn=e.id,Bn=e.overflow,Va=t),t=Bc(t,i.children),t.flags|=4096);return t}return c?(fa(),c=i.fallback,b=t.mode,w=e.child,O=w.sibling,i=Ln(w,{mode:"hidden",children:i.children}),i.subtreeFlags=w.subtreeFlags&65011712,O!==null?c=Ln(O,c):(c=Ga(c,b,n,null),c.flags|=2),c.return=t,i.return=t,i.sibling=c,t.child=i,i=c,c=t.child,b=e.child.memoizedState,b===null?b=Lc(n):(w=b.cachePool,w!==null?(O=gt._currentValue,w=w.parent!==O?{parent:O,pool:O}:w):w=Bf(),b={baseLanes:b.baseLanes|n,cachePool:w}),c.memoizedState=b,c.childLanes=Pc(e,p,n),t.memoizedState=zc,i):(da(t),n=e.child,e=n.sibling,n=Ln(n,{mode:"visible",children:i.children}),n.return=t,n.sibling=null,e!==null&&(p=t.deletions,p===null?(t.deletions=[e],t.flags|=16):p.push(e)),t.child=n,t.memoizedState=null,n)}function Bc(e,t){return t=hl({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function hl(e,t){return e=Zt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function qc(e,t,n){return Mi(t,e.child,null,n),e=Bc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function em(e,t,n){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),ic(e.return,t,n)}function Hc(e,t,n,i,c){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:c}:(d.isBackwards=t,d.rendering=null,d.renderingStartTime=0,d.last=i,d.tail=n,d.tailMode=c)}function tm(e,t,n){var i=t.pendingProps,c=i.revealOrder,d=i.tail;if(At(e,t,i.children,n),i=vt.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&em(e,n,t);else if(e.tag===19)em(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}switch(Z(vt,i),c){case"forwards":for(n=t.child,c=null;n!==null;)e=n.alternate,e!==null&&cl(e)===null&&(c=n),n=n.sibling;n=c,n===null?(c=t.child,t.child=null):(c=n.sibling,n.sibling=null),Hc(t,!1,c,n,d);break;case"backwards":for(n=null,c=t.child,t.child=null;c!==null;){if(e=c.alternate,e!==null&&cl(e)===null){t.child=c;break}e=c.sibling,c.sibling=n,n=c,c=e}Hc(t,!0,n,null,d);break;case"together":Hc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Gn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),va|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Ts(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(l(153));if(t.child!==null){for(e=t.child,n=Ln(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ln(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Ic(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Fr(e)))}function t0(e,t,n){switch(t.tag){case 3:Pe(t,t.stateNode.containerInfo),ra(t,gt,e.memoizedState.cache),xs();break;case 27:case 5:an(t);break;case 4:Pe(t,t.stateNode.containerInfo);break;case 10:ra(t,t.type,t.memoizedProps.value);break;case 13:var i=t.memoizedState;if(i!==null)return i.dehydrated!==null?(da(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Wh(e,t,n):(da(t),e=Gn(e,t,n),e!==null?e.sibling:null);da(t);break;case 19:var c=(e.flags&128)!==0;if(i=(n&t.childLanes)!==0,i||(Ts(e,t,n,!1),i=(n&t.childLanes)!==0),c){if(i)return tm(e,t,n);t.flags|=128}if(c=t.memoizedState,c!==null&&(c.rendering=null,c.tail=null,c.lastEffect=null),Z(vt,vt.current),i)break;return null;case 22:case 23:return t.lanes=0,Kh(e,t,n);case 24:ra(t,gt,e.memoizedState.cache)}return Gn(e,t,n)}function nm(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)jt=!0;else{if(!Ic(e,n)&&(t.flags&128)===0)return jt=!1,t0(e,t,n);jt=(e.flags&131072)!==0}else jt=!1,De&&(t.flags&1048576)!==0&&Rf(t,Yr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var i=t.elementType,c=i._init;if(i=c(i._payload),t.type=i,typeof i=="function")Zo(i)?(e=Wa(i,e),t.tag=1,t=Zh(null,t,i,e,n)):(t.tag=0,t=Dc(null,t,i,e,n));else{if(i!=null){if(c=i.$$typeof,c===te){t.tag=11,t=Vh(null,t,i,e,n);break e}else if(c===le){t.tag=14,t=Yh(null,t,i,e,n);break e}}throw t=Qe(i)||i,Error(l(306,t,""))}}return t;case 0:return Dc(e,t,t.type,t.pendingProps,n);case 1:return i=t.type,c=Wa(i,t.pendingProps),Zh(e,t,i,c,n);case 3:e:{if(Pe(t,t.stateNode.containerInfo),e===null)throw Error(l(387));i=t.pendingProps;var d=t.memoizedState;c=d.element,dc(e,t),Us(t,i,null,n);var p=t.memoizedState;if(i=p.cache,ra(t,gt,i),i!==d.cache&&sc(t,[gt],n,!0),Ms(),i=p.element,d.isDehydrated)if(d={element:i,isDehydrated:!1,cache:p.cache},t.updateQueue.baseState=d,t.memoizedState=d,t.flags&256){t=Jh(e,t,i,n);break e}else if(i!==c){c=on(Error(l(424)),t),ks(c),t=Jh(e,t,i,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(at=wn(e.firstChild),zt=t,De=!0,Fa=null,Cn=!0,n=Uh(t,null,i,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(xs(),i===c){t=Gn(e,t,n);break e}At(e,t,i,n)}t=t.child}return t;case 26:return fl(e,t),e===null?(n=rp(t.type,null,t.pendingProps,null))?t.memoizedState=n:De||(n=t.type,e=t.pendingProps,i=Tl(de.current).createElement(n),i[_t]=t,i[St]=e,Nt(i,n,e),N(i),t.stateNode=i):t.memoizedState=rp(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return an(t),e===null&&De&&(i=t.stateNode=ap(t.type,t.pendingProps,de.current),zt=t,Cn=!0,c=at,Sa(t.type)?(wu=c,at=wn(i.firstChild)):at=c),At(e,t,t.pendingProps.children,n),fl(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&De&&((c=i=at)&&(i=A0(i,t.type,t.pendingProps,Cn),i!==null?(t.stateNode=i,zt=t,at=wn(i.firstChild),Cn=!1,c=!0):c=!1),c||Ka(t)),an(t),c=t.type,d=t.pendingProps,p=e!==null?e.memoizedProps:null,i=d.children,yu(c,d)?i=null:p!==null&&yu(c,p)&&(t.flags|=32),t.memoizedState!==null&&(c=vc(e,t,Fy,null,null,n),nr._currentValue=c),fl(e,t),At(e,t,i,n),t.child;case 6:return e===null&&De&&((e=n=at)&&(n=O0(n,t.pendingProps,Cn),n!==null?(t.stateNode=n,zt=t,at=null,e=!0):e=!1),e||Ka(t)),null;case 13:return Wh(e,t,n);case 4:return Pe(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=Mi(t,null,i,n):At(e,t,i,n),t.child;case 11:return Vh(e,t,t.type,t.pendingProps,n);case 7:return At(e,t,t.pendingProps,n),t.child;case 8:return At(e,t,t.pendingProps.children,n),t.child;case 12:return At(e,t,t.pendingProps.children,n),t.child;case 10:return i=t.pendingProps,ra(t,t.type,i.value),At(e,t,i.children,n),t.child;case 9:return c=t.type._context,i=t.pendingProps.children,Qa(t),c=Mt(c),i=i(c),t.flags|=1,At(e,t,i,n),t.child;case 14:return Yh(e,t,t.type,t.pendingProps,n);case 15:return Fh(e,t,t.type,t.pendingProps,n);case 19:return tm(e,t,n);case 31:return i=t.pendingProps,n=t.mode,i={mode:i.mode,children:i.children},e===null?(n=hl(i,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=Ln(e.child,i),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Kh(e,t,n);case 24:return Qa(t),i=Mt(gt),e===null?(c=oc(),c===null&&(c=Ke,d=rc(),c.pooledCache=d,d.refCount++,d!==null&&(c.pooledCacheLanes|=n),c=d),t.memoizedState={parent:i,cache:c},uc(t),ra(t,gt,c)):((e.lanes&n)!==0&&(dc(e,t),Us(t,null,null,n),Ms()),c=e.memoizedState,d=t.memoizedState,c.parent!==i?(c={parent:i,cache:i},t.memoizedState=c,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=c),ra(t,gt,i)):(i=d.cache,ra(t,gt,i),i!==c.cache&&sc(t,[gt],n,!0))),At(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(l(156,t.tag))}function Vn(e){e.flags|=4}function am(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!dp(t)){if(t=fn.current,t!==null&&((Re&4194048)===Re?An!==null:(Re&62914560)!==Re&&(Re&536870912)===0||t!==An))throw Ns=cc,qf;e.flags|=8192}}function ml(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Cr():536870912,e.lanes|=t,Li|=t)}function Hs(e,t){if(!De)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function et(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,i=0;if(t)for(var c=e.child;c!==null;)n|=c.lanes|c.childLanes,i|=c.subtreeFlags&65011712,i|=c.flags&65011712,c.return=e,c=c.sibling;else for(c=e.child;c!==null;)n|=c.lanes|c.childLanes,i|=c.subtreeFlags,i|=c.flags,c.return=e,c=c.sibling;return e.subtreeFlags|=i,e.childLanes=n,t}function n0(e,t,n){var i=t.pendingProps;switch(tc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return et(t),null;case 1:return et(t),null;case 3:return n=t.stateNode,i=null,e!==null&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),Hn(gt),Bt(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Es(t)?Vn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Df())),et(t),null;case 26:return n=t.memoizedState,e===null?(Vn(t),n!==null?(et(t),am(t,n)):(et(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Vn(t),et(t),am(t,n)):(et(t),t.flags&=-16777217):(e.memoizedProps!==i&&Vn(t),et(t),t.flags&=-16777217),null;case 27:vn(t),n=de.current;var c=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==i&&Vn(t);else{if(!i){if(t.stateNode===null)throw Error(l(166));return et(t),null}e=re.current,Es(t)?Mf(t):(e=ap(c,i,n),t.stateNode=e,Vn(t))}return et(t),null;case 5:if(vn(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==i&&Vn(t);else{if(!i){if(t.stateNode===null)throw Error(l(166));return et(t),null}if(e=re.current,Es(t))Mf(t);else{switch(c=Tl(de.current),e){case 1:e=c.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=c.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=c.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=c.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=c.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof i.is=="string"?c.createElement("select",{is:i.is}):c.createElement("select"),i.multiple?e.multiple=!0:i.size&&(e.size=i.size);break;default:e=typeof i.is=="string"?c.createElement(n,{is:i.is}):c.createElement(n)}}e[_t]=t,e[St]=i;e:for(c=t.child;c!==null;){if(c.tag===5||c.tag===6)e.appendChild(c.stateNode);else if(c.tag!==4&&c.tag!==27&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===t)break e;for(;c.sibling===null;){if(c.return===null||c.return===t)break e;c=c.return}c.sibling.return=c.return,c=c.sibling}t.stateNode=e;e:switch(Nt(e,n,i),n){case"button":case"input":case"select":case"textarea":e=!!i.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Vn(t)}}return et(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==i&&Vn(t);else{if(typeof i!="string"&&t.stateNode===null)throw Error(l(166));if(e=de.current,Es(t)){if(e=t.stateNode,n=t.memoizedProps,i=null,c=zt,c!==null)switch(c.tag){case 27:case 5:i=c.memoizedProps}e[_t]=t,e=!!(e.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||Qm(e.nodeValue,n)),e||Ka(t)}else e=Tl(e).createTextNode(i),e[_t]=t,t.stateNode=e}return et(t),null;case 13:if(i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(c=Es(t),i!==null&&i.dehydrated!==null){if(e===null){if(!c)throw Error(l(318));if(c=t.memoizedState,c=c!==null?c.dehydrated:null,!c)throw Error(l(317));c[_t]=t}else xs(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;et(t),c=!1}else c=Df(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=c),c=!0;if(!c)return t.flags&256?($n(t),t):($n(t),null)}if($n(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=i!==null,e=e!==null&&e.memoizedState!==null,n){i=t.child,c=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(c=i.alternate.memoizedState.cachePool.pool);var d=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(d=i.memoizedState.cachePool.pool),d!==c&&(i.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),ml(t,t.updateQueue),et(t),null;case 4:return Bt(),e===null&&hu(t.stateNode.containerInfo),et(t),null;case 10:return Hn(t.type),et(t),null;case 19:if(X(vt),c=t.memoizedState,c===null)return et(t),null;if(i=(t.flags&128)!==0,d=c.rendering,d===null)if(i)Hs(c,!1);else{if(it!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(d=cl(e),d!==null){for(t.flags|=128,Hs(c,!1),e=d.updateQueue,t.updateQueue=e,ml(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Nf(n,e),n=n.sibling;return Z(vt,vt.current&1|2),t.child}e=e.sibling}c.tail!==null&&Ze()>vl&&(t.flags|=128,i=!0,Hs(c,!1),t.lanes=4194304)}else{if(!i)if(e=cl(d),e!==null){if(t.flags|=128,i=!0,e=e.updateQueue,t.updateQueue=e,ml(t,e),Hs(c,!0),c.tail===null&&c.tailMode==="hidden"&&!d.alternate&&!De)return et(t),null}else 2*Ze()-c.renderingStartTime>vl&&n!==536870912&&(t.flags|=128,i=!0,Hs(c,!1),t.lanes=4194304);c.isBackwards?(d.sibling=t.child,t.child=d):(e=c.last,e!==null?e.sibling=d:t.child=d,c.last=d)}return c.tail!==null?(t=c.tail,c.rendering=t,c.tail=t.sibling,c.renderingStartTime=Ze(),t.sibling=null,e=vt.current,Z(vt,i?e&1|2:e&1),t):(et(t),null);case 22:case 23:return $n(t),pc(),i=t.memoizedState!==null,e!==null?e.memoizedState!==null!==i&&(t.flags|=8192):i&&(t.flags|=8192),i?(n&536870912)!==0&&(t.flags&128)===0&&(et(t),t.subtreeFlags&6&&(t.flags|=8192)):et(t),n=t.updateQueue,n!==null&&ml(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),i=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),i!==n&&(t.flags|=2048),e!==null&&X(Za),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Hn(gt),et(t),null;case 25:return null;case 30:return null}throw Error(l(156,t.tag))}function a0(e,t){switch(tc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Hn(gt),Bt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return vn(t),null;case 13:if($n(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(l(340));xs()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return X(vt),null;case 4:return Bt(),null;case 10:return Hn(t.type),null;case 22:case 23:return $n(t),pc(),e!==null&&X(Za),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Hn(gt),null;case 25:return null;default:return null}}function im(e,t){switch(tc(t),t.tag){case 3:Hn(gt),Bt();break;case 26:case 27:case 5:vn(t);break;case 4:Bt();break;case 13:$n(t);break;case 19:X(vt);break;case 10:Hn(t.type);break;case 22:case 23:$n(t),pc(),e!==null&&X(Za);break;case 24:Hn(gt)}}function Is(e,t){try{var n=t.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var c=i.next;n=c;do{if((n.tag&e)===e){i=void 0;var d=n.create,p=n.inst;i=d(),p.destroy=i}n=n.next}while(n!==c)}}catch(b){Ye(t,t.return,b)}}function ha(e,t,n){try{var i=t.updateQueue,c=i!==null?i.lastEffect:null;if(c!==null){var d=c.next;i=d;do{if((i.tag&e)===e){var p=i.inst,b=p.destroy;if(b!==void 0){p.destroy=void 0,c=t;var w=n,O=b;try{O()}catch(B){Ye(c,w,B)}}}i=i.next}while(i!==d)}}catch(B){Ye(t,t.return,B)}}function sm(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{Yf(t,n)}catch(i){Ye(e,e.return,i)}}}function rm(e,t,n){n.props=Wa(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(i){Ye(e,t,i)}}function $s(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var i=e.stateNode;break;case 30:i=e.stateNode;break;default:i=e.stateNode}typeof n=="function"?e.refCleanup=n(i):n.current=i}}catch(c){Ye(e,t,c)}}function On(e,t){var n=e.ref,i=e.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(c){Ye(e,t,c)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(c){Ye(e,t,c)}else n.current=null}function lm(e){var t=e.type,n=e.memoizedProps,i=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break e;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(c){Ye(e,e.return,c)}}function $c(e,t,n){try{var i=e.stateNode;E0(i,e.type,n,t),i[St]=t}catch(c){Ye(e,e.return,c)}}function om(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Sa(e.type)||e.tag===4}function Gc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||om(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Sa(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Vc(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=kl));else if(i!==4&&(i===27&&Sa(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Vc(e,t,n),e=e.sibling;e!==null;)Vc(e,t,n),e=e.sibling}function pl(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(i!==4&&(i===27&&Sa(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(pl(e,t,n),e=e.sibling;e!==null;)pl(e,t,n),e=e.sibling}function cm(e){var t=e.stateNode,n=e.memoizedProps;try{for(var i=e.type,c=t.attributes;c.length;)t.removeAttributeNode(c[0]);Nt(t,i,n),t[_t]=e,t[St]=n}catch(d){Ye(e,e.return,d)}}var Yn=!1,ot=!1,Yc=!1,um=typeof WeakSet=="function"?WeakSet:Set,Et=null;function i0(e,t){if(e=e.containerInfo,gu=Ml,e=Sf(e),Go(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var c=i.anchorOffset,d=i.focusNode;i=i.focusOffset;try{n.nodeType,d.nodeType}catch(fe){n=null;break e}var p=0,b=-1,w=-1,O=0,B=0,$=e,R=null;t:for(;;){for(var M;$!==n||c!==0&&$.nodeType!==3||(b=p+c),$!==d||i!==0&&$.nodeType!==3||(w=p+i),$.nodeType===3&&(p+=$.nodeValue.length),(M=$.firstChild)!==null;)R=$,$=M;for(;;){if($===e)break t;if(R===n&&++O===c&&(b=p),R===d&&++B===i&&(w=p),(M=$.nextSibling)!==null)break;$=R,R=$.parentNode}$=M}n=b===-1||w===-1?null:{start:b,end:w}}else n=null}n=n||{start:0,end:0}}else n=null;for(vu={focusedElem:e,selectionRange:n},Ml=!1,Et=t;Et!==null;)if(t=Et,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Et=e;else for(;Et!==null;){switch(t=Et,d=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&d!==null){e=void 0,n=t,c=d.memoizedProps,d=d.memoizedState,i=n.stateNode;try{var pe=Wa(n.type,c,n.elementType===n.type);e=i.getSnapshotBeforeUpdate(pe,d),i.__reactInternalSnapshotBeforeUpdate=e}catch(fe){Ye(n,n.return,fe)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)_u(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":_u(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(l(163))}if(e=t.sibling,e!==null){e.return=t.return,Et=e;break}Et=t.return}}function dm(e,t,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:ma(e,n),i&4&&Is(5,n);break;case 1:if(ma(e,n),i&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(p){Ye(n,n.return,p)}else{var c=Wa(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(c,t,e.__reactInternalSnapshotBeforeUpdate)}catch(p){Ye(n,n.return,p)}}i&64&&sm(n),i&512&&$s(n,n.return);break;case 3:if(ma(e,n),i&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Yf(e,t)}catch(p){Ye(n,n.return,p)}}break;case 27:t===null&&i&4&&cm(n);case 26:case 5:ma(e,n),t===null&&i&4&&lm(n),i&512&&$s(n,n.return);break;case 12:ma(e,n);break;case 13:ma(e,n),i&4&&mm(e,n),i&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=h0.bind(null,n),N0(e,n))));break;case 22:if(i=n.memoizedState!==null||Yn,!i){t=t!==null&&t.memoizedState!==null||ot,c=Yn;var d=ot;Yn=i,(ot=t)&&!d?pa(e,n,(n.subtreeFlags&8772)!==0):ma(e,n),Yn=c,ot=d}break;case 30:break;default:ma(e,n)}}function fm(e){var t=e.alternate;t!==null&&(e.alternate=null,fm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&hs(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var We=null,Gt=!1;function Fn(e,t,n){for(n=n.child;n!==null;)hm(e,t,n),n=n.sibling}function hm(e,t,n){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount(bt,n)}catch(d){}switch(n.tag){case 26:ot||On(n,t),Fn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:ot||On(n,t);var i=We,c=Gt;Sa(n.type)&&(We=n.stateNode,Gt=!1),Fn(e,t,n),Js(n.stateNode),We=i,Gt=c;break;case 5:ot||On(n,t);case 6:if(i=We,c=Gt,We=null,Fn(e,t,n),We=i,Gt=c,We!==null)if(Gt)try{(We.nodeType===9?We.body:We.nodeName==="HTML"?We.ownerDocument.body:We).removeChild(n.stateNode)}catch(d){Ye(n,t,d)}else try{We.removeChild(n.stateNode)}catch(d){Ye(n,t,d)}break;case 18:We!==null&&(Gt?(e=We,tp(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),rr(e)):tp(We,n.stateNode));break;case 4:i=We,c=Gt,We=n.stateNode.containerInfo,Gt=!0,Fn(e,t,n),We=i,Gt=c;break;case 0:case 11:case 14:case 15:ot||ha(2,n,t),ot||ha(4,n,t),Fn(e,t,n);break;case 1:ot||(On(n,t),i=n.stateNode,typeof i.componentWillUnmount=="function"&&rm(n,t,i)),Fn(e,t,n);break;case 21:Fn(e,t,n);break;case 22:ot=(i=ot)||n.memoizedState!==null,Fn(e,t,n),ot=i;break;default:Fn(e,t,n)}}function mm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{rr(e)}catch(n){Ye(t,t.return,n)}}function s0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new um),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new um),t;default:throw Error(l(435,e.tag))}}function Fc(e,t){var n=s0(e);t.forEach(function(i){var c=m0.bind(null,e,i);n.has(i)||(n.add(i),i.then(c,c))})}function Jt(e,t){var n=t.deletions;if(n!==null)for(var i=0;i<n.length;i++){var c=n[i],d=e,p=t,b=p;e:for(;b!==null;){switch(b.tag){case 27:if(Sa(b.type)){We=b.stateNode,Gt=!1;break e}break;case 5:We=b.stateNode,Gt=!1;break e;case 3:case 4:We=b.stateNode.containerInfo,Gt=!0;break e}b=b.return}if(We===null)throw Error(l(160));hm(d,p,c),We=null,Gt=!1,d=c.alternate,d!==null&&(d.return=null),c.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)pm(t,e),t=t.sibling}var Sn=null;function pm(e,t){var n=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Jt(t,e),Wt(e),i&4&&(ha(3,e,e.return),Is(3,e),ha(5,e,e.return));break;case 1:Jt(t,e),Wt(e),i&512&&(ot||n===null||On(n,n.return)),i&64&&Yn&&(e=e.updateQueue,e!==null&&(i=e.callbacks,i!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var c=Sn;if(Jt(t,e),Wt(e),i&512&&(ot||n===null||On(n,n.return)),i&4){var d=n!==null?n.memoizedState:null;if(i=e.memoizedState,n===null)if(i===null)if(e.stateNode===null){e:{i=e.type,n=e.memoizedProps,c=c.ownerDocument||c;t:switch(i){case"title":d=c.getElementsByTagName("title")[0],(!d||d[Ba]||d[_t]||d.namespaceURI==="http://www.w3.org/2000/svg"||d.hasAttribute("itemprop"))&&(d=c.createElement(i),c.head.insertBefore(d,c.querySelector("head > title"))),Nt(d,i,n),d[_t]=e,N(d),i=d;break e;case"link":var p=cp("link","href",c).get(i+(n.href||""));if(p){for(var b=0;b<p.length;b++)if(d=p[b],d.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&d.getAttribute("rel")===(n.rel==null?null:n.rel)&&d.getAttribute("title")===(n.title==null?null:n.title)&&d.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){p.splice(b,1);break t}}d=c.createElement(i),Nt(d,i,n),c.head.appendChild(d);break;case"meta":if(p=cp("meta","content",c).get(i+(n.content||""))){for(b=0;b<p.length;b++)if(d=p[b],d.getAttribute("content")===(n.content==null?null:""+n.content)&&d.getAttribute("name")===(n.name==null?null:n.name)&&d.getAttribute("property")===(n.property==null?null:n.property)&&d.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&d.getAttribute("charset")===(n.charSet==null?null:n.charSet)){p.splice(b,1);break t}}d=c.createElement(i),Nt(d,i,n),c.head.appendChild(d);break;default:throw Error(l(468,i))}d[_t]=e,N(d),i=d}e.stateNode=i}else up(c,e.type,e.stateNode);else e.stateNode=op(c,i,e.memoizedProps);else d!==i?(d===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):d.count--,i===null?up(c,e.type,e.stateNode):op(c,i,e.memoizedProps)):i===null&&e.stateNode!==null&&$c(e,e.memoizedProps,n.memoizedProps)}break;case 27:Jt(t,e),Wt(e),i&512&&(ot||n===null||On(n,n.return)),n!==null&&i&4&&$c(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Jt(t,e),Wt(e),i&512&&(ot||n===null||On(n,n.return)),e.flags&32){c=e.stateNode;try{pi(c,"")}catch(M){Ye(e,e.return,M)}}i&4&&e.stateNode!=null&&(c=e.memoizedProps,$c(e,c,n!==null?n.memoizedProps:c)),i&1024&&(Yc=!0);break;case 6:if(Jt(t,e),Wt(e),i&4){if(e.stateNode===null)throw Error(l(162));i=e.memoizedProps,n=e.stateNode;try{n.nodeValue=i}catch(M){Ye(e,e.return,M)}}break;case 3:if(Ol=null,c=Sn,Sn=Cl(t.containerInfo),Jt(t,e),Sn=c,Wt(e),i&4&&n!==null&&n.memoizedState.isDehydrated)try{rr(t.containerInfo)}catch(M){Ye(e,e.return,M)}Yc&&(Yc=!1,gm(e));break;case 4:i=Sn,Sn=Cl(e.stateNode.containerInfo),Jt(t,e),Wt(e),Sn=i;break;case 12:Jt(t,e),Wt(e);break;case 13:Jt(t,e),Wt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Wc=Ze()),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,Fc(e,i)));break;case 22:c=e.memoizedState!==null;var w=n!==null&&n.memoizedState!==null,O=Yn,B=ot;if(Yn=O||c,ot=B||w,Jt(t,e),ot=B,Yn=O,Wt(e),i&8192)e:for(t=e.stateNode,t._visibility=c?t._visibility&-2:t._visibility|1,c&&(n===null||w||Yn||ot||ei(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){w=n=t;try{if(d=w.stateNode,c)p=d.style,typeof p.setProperty=="function"?p.setProperty("display","none","important"):p.display="none";else{b=w.stateNode;var $=w.memoizedProps.style,R=$!=null&&$.hasOwnProperty("display")?$.display:null;b.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(M){Ye(w,w.return,M)}}}else if(t.tag===6){if(n===null){w=t;try{w.stateNode.nodeValue=c?"":w.memoizedProps}catch(M){Ye(w,w.return,M)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}i&4&&(i=e.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,Fc(e,n))));break;case 19:Jt(t,e),Wt(e),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,Fc(e,i)));break;case 30:break;case 21:break;default:Jt(t,e),Wt(e)}}function Wt(e){var t=e.flags;if(t&2){try{for(var n,i=e.return;i!==null;){if(om(i)){n=i;break}i=i.return}if(n==null)throw Error(l(160));switch(n.tag){case 27:var c=n.stateNode,d=Gc(e);pl(e,d,c);break;case 5:var p=n.stateNode;n.flags&32&&(pi(p,""),n.flags&=-33);var b=Gc(e);pl(e,b,p);break;case 3:case 4:var w=n.stateNode.containerInfo,O=Gc(e);Vc(e,O,w);break;default:throw Error(l(161))}}catch(B){Ye(e,e.return,B)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function gm(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;gm(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function ma(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)dm(e,t.alternate,t),t=t.sibling}function ei(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ha(4,t,t.return),ei(t);break;case 1:On(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&rm(t,t.return,n),ei(t);break;case 27:Js(t.stateNode);case 26:case 5:On(t,t.return),ei(t);break;case 22:t.memoizedState===null&&ei(t);break;case 30:ei(t);break;default:ei(t)}e=e.sibling}}function pa(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var i=t.alternate,c=e,d=t,p=d.flags;switch(d.tag){case 0:case 11:case 15:pa(c,d,n),Is(4,d);break;case 1:if(pa(c,d,n),i=d,c=i.stateNode,typeof c.componentDidMount=="function")try{c.componentDidMount()}catch(O){Ye(i,i.return,O)}if(i=d,c=i.updateQueue,c!==null){var b=i.stateNode;try{var w=c.shared.hiddenCallbacks;if(w!==null)for(c.shared.hiddenCallbacks=null,c=0;c<w.length;c++)Vf(w[c],b)}catch(O){Ye(i,i.return,O)}}n&&p&64&&sm(d),$s(d,d.return);break;case 27:cm(d);case 26:case 5:pa(c,d,n),n&&i===null&&p&4&&lm(d),$s(d,d.return);break;case 12:pa(c,d,n);break;case 13:pa(c,d,n),n&&p&4&&mm(c,d);break;case 22:d.memoizedState===null&&pa(c,d,n),$s(d,d.return);break;case 30:break;default:pa(c,d,n)}t=t.sibling}}function Kc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Cs(n))}function Xc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Cs(e))}function Nn(e,t,n,i){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)vm(e,t,n,i),t=t.sibling}function vm(e,t,n,i){var c=t.flags;switch(t.tag){case 0:case 11:case 15:Nn(e,t,n,i),c&2048&&Is(9,t);break;case 1:Nn(e,t,n,i);break;case 3:Nn(e,t,n,i),c&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Cs(e)));break;case 12:if(c&2048){Nn(e,t,n,i),e=t.stateNode;try{var d=t.memoizedProps,p=d.id,b=d.onPostCommit;typeof b=="function"&&b(p,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(w){Ye(t,t.return,w)}}else Nn(e,t,n,i);break;case 13:Nn(e,t,n,i);break;case 23:break;case 22:d=t.stateNode,p=t.alternate,t.memoizedState!==null?d._visibility&2?Nn(e,t,n,i):Gs(e,t):d._visibility&2?Nn(e,t,n,i):(d._visibility|=2,Ui(e,t,n,i,(t.subtreeFlags&10256)!==0)),c&2048&&Kc(p,t);break;case 24:Nn(e,t,n,i),c&2048&&Xc(t.alternate,t);break;default:Nn(e,t,n,i)}}function Ui(e,t,n,i,c){for(c=c&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var d=e,p=t,b=n,w=i,O=p.flags;switch(p.tag){case 0:case 11:case 15:Ui(d,p,b,w,c),Is(8,p);break;case 23:break;case 22:var B=p.stateNode;p.memoizedState!==null?B._visibility&2?Ui(d,p,b,w,c):Gs(d,p):(B._visibility|=2,Ui(d,p,b,w,c)),c&&O&2048&&Kc(p.alternate,p);break;case 24:Ui(d,p,b,w,c),c&&O&2048&&Xc(p.alternate,p);break;default:Ui(d,p,b,w,c)}t=t.sibling}}function Gs(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,i=t,c=i.flags;switch(i.tag){case 22:Gs(n,i),c&2048&&Kc(i.alternate,i);break;case 24:Gs(n,i),c&2048&&Xc(i.alternate,i);break;default:Gs(n,i)}t=t.sibling}}var Vs=8192;function Di(e){if(e.subtreeFlags&Vs)for(e=e.child;e!==null;)ym(e),e=e.sibling}function ym(e){switch(e.tag){case 26:Di(e),e.flags&Vs&&e.memoizedState!==null&&G0(Sn,e.memoizedState,e.memoizedProps);break;case 5:Di(e);break;case 3:case 4:var t=Sn;Sn=Cl(e.stateNode.containerInfo),Di(e),Sn=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Vs,Vs=16777216,Di(e),Vs=t):Di(e));break;default:Di(e)}}function bm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Ys(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];Et=i,Sm(i,e)}bm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)_m(e),e=e.sibling}function _m(e){switch(e.tag){case 0:case 11:case 15:Ys(e),e.flags&2048&&ha(9,e,e.return);break;case 3:Ys(e);break;case 12:Ys(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,gl(e)):Ys(e);break;default:Ys(e)}}function gl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];Et=i,Sm(i,e)}bm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:ha(8,t,t.return),gl(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,gl(t));break;default:gl(t)}e=e.sibling}}function Sm(e,t){for(;Et!==null;){var n=Et;switch(n.tag){case 0:case 11:case 15:ha(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:Cs(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,Et=i;else e:for(n=e;Et!==null;){i=Et;var c=i.sibling,d=i.return;if(fm(i),i===n){Et=null;break e}if(c!==null){c.return=d,Et=c;break e}Et=d}}}var r0={getCacheForType:function(e){var t=Mt(gt),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},l0=typeof WeakMap=="function"?WeakMap:Map,qe=0,Ke=null,ke=null,Re=0,He=0,en=null,ga=!1,zi=!1,Qc=!1,Kn=0,it=0,va=0,ti=0,Zc=0,hn=0,Li=0,Fs=null,Vt=null,Jc=!1,Wc=0,vl=1/0,yl=null,ya=null,Ot=0,ba=null,Pi=null,Bi=0,eu=0,tu=null,wm=null,Ks=0,nu=null;function tn(){if((qe&2)!==0&&Re!==0)return Re&-Re;if(z.T!==null){var e=ki;return e!==0?e:cu()}return Nr()}function jm(){hn===0&&(hn=(Re&536870912)===0||De?Tr():536870912);var e=fn.current;return e!==null&&(e.flags|=32),hn}function nn(e,t,n){(e===Ke&&(He===2||He===9)||e.cancelPendingCommit!==null)&&(qi(e,0),_a(e,Re,hn,!1)),Pa(e,n),((qe&2)===0||e!==Ke)&&(e===Ke&&((qe&2)===0&&(ti|=n),it===4&&_a(e,Re,hn,!1)),Rn(e))}function Em(e,t,n){if((qe&6)!==0)throw Error(l(327));var i=!n&&(t&124)===0&&(t&e.expiredLanes)===0||La(e,t),c=i?u0(e,t):su(e,t,!0),d=i;do{if(c===0){zi&&!i&&_a(e,t,0,!1);break}else{if(n=e.current.alternate,d&&!o0(n)){c=su(e,t,!1),d=!1;continue}if(c===2){if(d=t,e.errorRecoveryDisabledLanes&d)var p=0;else p=e.pendingLanes&-536870913,p=p!==0?p:p&536870912?536870912:0;if(p!==0){t=p;e:{var b=e;c=Fs;var w=b.current.memoizedState.isDehydrated;if(w&&(qi(b,p).flags|=256),p=su(b,p,!1),p!==2){if(Qc&&!w){b.errorRecoveryDisabledLanes|=d,ti|=d,c=4;break e}d=Vt,Vt=c,d!==null&&(Vt===null?Vt=d:Vt.push.apply(Vt,d))}c=p}if(d=!1,c!==2)continue}}if(c===1){qi(e,0),_a(e,t,0,!0);break}e:{switch(i=e,d=c,d){case 0:case 1:throw Error(l(345));case 4:if((t&4194048)!==t)break;case 6:_a(i,t,hn,!ga);break e;case 2:Vt=null;break;case 3:case 5:break;default:throw Error(l(329))}if((t&62914560)===t&&(c=Wc+300-Ze(),10<c)){if(_a(i,t,hn,!ga),za(i,0,!0)!==0)break e;i.timeoutHandle=Wm(xm.bind(null,i,n,Vt,yl,Jc,t,hn,ti,Li,ga,d,2,-0,0),c);break e}xm(i,n,Vt,yl,Jc,t,hn,ti,Li,ga,d,0,-0,0)}}break}while(!0);Rn(e)}function xm(e,t,n,i,c,d,p,b,w,O,B,$,R,M){if(e.timeoutHandle=-1,$=t.subtreeFlags,($&8192||($&16785408)===16785408)&&(tr={stylesheets:null,count:0,unsuspend:$0},ym(t),$=V0(),$!==null)){e.cancelPendingCommit=$(Rm.bind(null,e,t,d,n,i,c,p,b,w,B,1,R,M)),_a(e,d,p,!O);return}Rm(e,t,d,n,i,c,p,b,w)}function o0(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var c=n[i],d=c.getSnapshot;c=c.value;try{if(!Qt(d(),c))return!1}catch(p){return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function _a(e,t,n,i){t&=~Zc,t&=~ti,e.suspendedLanes|=t,e.pingedLanes&=~t,i&&(e.warmLanes|=t),i=e.expirationTimes;for(var c=t;0<c;){var d=31-mt(c),p=1<<d;i[d]=-1,c&=~p}n!==0&&Ar(e,n,t)}function bl(){return(qe&6)===0?(Xs(0),!1):!0}function au(){if(ke!==null){if(He===0)var e=ke.return;else e=ke,qn=Xa=null,_c(e),Ri=null,Bs=0,e=ke;for(;e!==null;)im(e.alternate,e),e=e.return;ke=null}}function qi(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,k0(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),au(),Ke=e,ke=n=Ln(e.current,null),Re=t,He=0,en=null,ga=!1,zi=La(e,t),Qc=!1,Li=hn=Zc=ti=va=it=0,Vt=Fs=null,Jc=!1,(t&8)!==0&&(t|=t&32);var i=e.entangledLanes;if(i!==0)for(e=e.entanglements,i&=t;0<i;){var c=31-mt(i),d=1<<c;t|=e[c],i&=~d}return Kn=t,Hr(),n}function km(e,t){je=null,z.H=rl,t===Os||t===Qr?(t=$f(),He=3):t===qf?(t=$f(),He=4):He=t===Gh?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,en=t,ke===null&&(it=1,dl(e,on(t,e.current)))}function Tm(){var e=z.H;return z.H=rl,e===null?rl:e}function Cm(){var e=z.A;return z.A=r0,e}function iu(){it=4,ga||(Re&4194048)!==Re&&fn.current!==null||(zi=!0),(va&134217727)===0&&(ti&134217727)===0||Ke===null||_a(Ke,Re,hn,!1)}function su(e,t,n){var i=qe;qe|=2;var c=Tm(),d=Cm();(Ke!==e||Re!==t)&&(yl=null,qi(e,t)),t=!1;var p=it;e:do try{if(He!==0&&ke!==null){var b=ke,w=en;switch(He){case 8:au(),p=6;break e;case 3:case 2:case 9:case 6:fn.current===null&&(t=!0);var O=He;if(He=0,en=null,Hi(e,b,w,O),n&&zi){p=0;break e}break;default:O=He,He=0,en=null,Hi(e,b,w,O)}}c0(),p=it;break}catch(B){km(e,B)}while(!0);return t&&e.shellSuspendCounter++,qn=Xa=null,qe=i,z.H=c,z.A=d,ke===null&&(Ke=null,Re=0,Hr()),p}function c0(){for(;ke!==null;)Am(ke)}function u0(e,t){var n=qe;qe|=2;var i=Tm(),c=Cm();Ke!==e||Re!==t?(yl=null,vl=Ze()+500,qi(e,t)):zi=La(e,t);e:do try{if(He!==0&&ke!==null){t=ke;var d=en;t:switch(He){case 1:He=0,en=null,Hi(e,t,d,1);break;case 2:case 9:if(Hf(d)){He=0,en=null,Om(t);break}t=function(){He!==2&&He!==9||Ke!==e||(He=7),Rn(e)},d.then(t,t);break e;case 3:He=7;break e;case 4:He=5;break e;case 7:Hf(d)?(He=0,en=null,Om(t)):(He=0,en=null,Hi(e,t,d,7));break;case 5:var p=null;switch(ke.tag){case 26:p=ke.memoizedState;case 5:case 27:var b=ke;if(!p||dp(p)){He=0,en=null;var w=b.sibling;if(w!==null)ke=w;else{var O=b.return;O!==null?(ke=O,_l(O)):ke=null}break t}}He=0,en=null,Hi(e,t,d,5);break;case 6:He=0,en=null,Hi(e,t,d,6);break;case 8:au(),it=6;break e;default:throw Error(l(462))}}d0();break}catch(B){km(e,B)}while(!0);return qn=Xa=null,z.H=i,z.A=c,qe=n,ke!==null?0:(Ke=null,Re=0,Hr(),it)}function d0(){for(;ke!==null&&!rt();)Am(ke)}function Am(e){var t=nm(e.alternate,e,Kn);e.memoizedProps=e.pendingProps,t===null?_l(e):ke=t}function Om(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Qh(n,t,t.pendingProps,t.type,void 0,Re);break;case 11:t=Qh(n,t,t.pendingProps,t.type.render,t.ref,Re);break;case 5:_c(t);default:im(n,t),t=ke=Nf(t,Kn),t=nm(n,t,Kn)}e.memoizedProps=e.pendingProps,t===null?_l(e):ke=t}function Hi(e,t,n,i){qn=Xa=null,_c(t),Ri=null,Bs=0;var c=t.return;try{if(e0(e,c,t,n,Re)){it=1,dl(e,on(n,e.current)),ke=null;return}}catch(d){if(c!==null)throw ke=c,d;it=1,dl(e,on(n,e.current)),ke=null;return}t.flags&32768?(De||i===1?e=!0:zi||(Re&536870912)!==0?e=!1:(ga=e=!0,(i===2||i===9||i===3||i===6)&&(i=fn.current,i!==null&&i.tag===13&&(i.flags|=16384))),Nm(t,e)):_l(t)}function _l(e){var t=e;do{if((t.flags&32768)!==0){Nm(t,ga);return}e=t.return;var n=n0(t.alternate,t,Kn);if(n!==null){ke=n;return}if(t=t.sibling,t!==null){ke=t;return}ke=t=e}while(t!==null);it===0&&(it=5)}function Nm(e,t){do{var n=a0(e.alternate,e);if(n!==null){n.flags&=32767,ke=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){ke=e;return}ke=e=n}while(e!==null);it=6,ke=null}function Rm(e,t,n,i,c,d,p,b,w){e.cancelPendingCommit=null;do Sl();while(Ot!==0);if((qe&6)!==0)throw Error(l(327));if(t!==null){if(t===e.current)throw Error(l(177));if(d=t.lanes|t.childLanes,d|=Xo,Eo(e,n,d,p,b,w),e===Ke&&(ke=Ke=null,Re=0),Pi=t,ba=e,Bi=n,eu=d,tu=c,wm=i,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,p0(Rt,function(){return Lm(),null})):(e.callbackNode=null,e.callbackPriority=0),i=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||i){i=z.T,z.T=null,c=G.p,G.p=2,p=qe,qe|=4;try{i0(e,t,n)}finally{qe=p,G.p=c,z.T=i}}Ot=1,Mm(),Um(),Dm()}}function Mm(){if(Ot===1){Ot=0;var e=ba,t=Pi,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=z.T,z.T=null;var i=G.p;G.p=2;var c=qe;qe|=4;try{pm(t,e);var d=vu,p=Sf(e.containerInfo),b=d.focusedElem,w=d.selectionRange;if(p!==b&&b&&b.ownerDocument&&_f(b.ownerDocument.documentElement,b)){if(w!==null&&Go(b)){var O=w.start,B=w.end;if(B===void 0&&(B=O),"selectionStart"in b)b.selectionStart=O,b.selectionEnd=Math.min(B,b.value.length);else{var $=b.ownerDocument||document,R=$&&$.defaultView||window;if(R.getSelection){var M=R.getSelection(),pe=b.textContent.length,fe=Math.min(w.start,pe),Ge=w.end===void 0?fe:Math.min(w.end,pe);!M.extend&&fe>Ge&&(p=Ge,Ge=fe,fe=p);var T=bf(b,fe),k=bf(b,Ge);if(T&&k&&(M.rangeCount!==1||M.anchorNode!==T.node||M.anchorOffset!==T.offset||M.focusNode!==k.node||M.focusOffset!==k.offset)){var A=$.createRange();A.setStart(T.node,T.offset),M.removeAllRanges(),fe>Ge?(M.addRange(A),M.extend(k.node,k.offset)):(A.setEnd(k.node,k.offset),M.addRange(A))}}}}for($=[],M=b;M=M.parentNode;)M.nodeType===1&&$.push({element:M,left:M.scrollLeft,top:M.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<$.length;b++){var H=$[b];H.element.scrollLeft=H.left,H.element.scrollTop=H.top}}Ml=!!gu,vu=gu=null}finally{qe=c,G.p=i,z.T=n}}e.current=t,Ot=2}}function Um(){if(Ot===2){Ot=0;var e=ba,t=Pi,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=z.T,z.T=null;var i=G.p;G.p=2;var c=qe;qe|=4;try{dm(e,t.alternate,t)}finally{qe=c,G.p=i,z.T=n}}Ot=3}}function Dm(){if(Ot===4||Ot===3){Ot=0,ze();var e=ba,t=Pi,n=Bi,i=wm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ot=5:(Ot=0,Pi=ba=null,zm(e,e.pendingLanes));var c=e.pendingLanes;if(c===0&&(ya=null),hi(n),t=t.stateNode,ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot(bt,t,void 0,(t.current.flags&128)===128)}catch(w){}if(i!==null){t=z.T,c=G.p,G.p=2,z.T=null;try{for(var d=e.onRecoverableError,p=0;p<i.length;p++){var b=i[p];d(b.value,{componentStack:b.stack})}}finally{z.T=t,G.p=c}}(Bi&3)!==0&&Sl(),Rn(e),c=e.pendingLanes,(n&4194090)!==0&&(c&42)!==0?e===nu?Ks++:(Ks=0,nu=e):Ks=0,Xs(0)}}function zm(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Cs(t)))}function Sl(e){return Mm(),Um(),Dm(),Lm()}function Lm(){if(Ot!==5)return!1;var e=ba,t=eu;eu=0;var n=hi(Bi),i=z.T,c=G.p;try{G.p=32>n?32:n,z.T=null,n=tu,tu=null;var d=ba,p=Bi;if(Ot=0,Pi=ba=null,Bi=0,(qe&6)!==0)throw Error(l(331));var b=qe;if(qe|=4,_m(d.current),vm(d,d.current,p,n),qe=b,Xs(0,!1),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot(bt,d)}catch(w){}return!0}finally{G.p=c,z.T=i,zm(e,t)}}function Pm(e,t,n){t=on(n,t),t=Uc(e.stateNode,t,2),e=ca(e,t,2),e!==null&&(Pa(e,2),Rn(e))}function Ye(e,t,n){if(e.tag===3)Pm(e,e,n);else for(;t!==null;){if(t.tag===3){Pm(t,e,n);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(ya===null||!ya.has(i))){e=on(n,e),n=Ih(2),i=ca(t,n,2),i!==null&&($h(n,i,t,e),Pa(i,2),Rn(i));break}}t=t.return}}function ru(e,t,n){var i=e.pingCache;if(i===null){i=e.pingCache=new l0;var c=new Set;i.set(t,c)}else c=i.get(t),c===void 0&&(c=new Set,i.set(t,c));c.has(n)||(Qc=!0,c.add(n),e=f0.bind(null,e,t,n),t.then(e,e))}function f0(e,t,n){var i=e.pingCache;i!==null&&i.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Ke===e&&(Re&n)===n&&(it===4||it===3&&(Re&62914560)===Re&&300>Ze()-Wc?(qe&2)===0&&qi(e,0):Zc|=n,Li===Re&&(Li=0)),Rn(e)}function Bm(e,t){t===0&&(t=Cr()),e=wi(e,t),e!==null&&(Pa(e,t),Rn(e))}function h0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Bm(e,n)}function m0(e,t){var n=0;switch(e.tag){case 13:var i=e.stateNode,c=e.memoizedState;c!==null&&(n=c.retryLane);break;case 19:i=e.stateNode;break;case 22:i=e.stateNode._retryCache;break;default:throw Error(l(314))}i!==null&&i.delete(t),Bm(e,n)}function p0(e,t){return Ne(e,t)}var wl=null,Ii=null,lu=!1,jl=!1,ou=!1,ni=0;function Rn(e){e!==Ii&&e.next===null&&(Ii===null?wl=Ii=e:Ii=Ii.next=e),jl=!0,lu||(lu=!0,v0())}function Xs(e,t){if(!ou&&jl){ou=!0;do for(var n=!1,i=wl;i!==null;){if(e!==0){var c=i.pendingLanes;if(c===0)var d=0;else{var p=i.suspendedLanes,b=i.pingedLanes;d=(1<<31-mt(42|e)+1)-1,d&=c&~(p&~b),d=d&201326741?d&201326741|1:d?d|2:0}d!==0&&(n=!0,$m(i,d))}else d=Re,d=za(i,i===Ke?d:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(d&3)===0||La(i,d)||(n=!0,$m(i,d));i=i.next}while(n);ou=!1}}function g0(){qm()}function qm(){jl=lu=!1;var e=0;ni!==0&&(x0()&&(e=ni),ni=0);for(var t=Ze(),n=null,i=wl;i!==null;){var c=i.next,d=Hm(i,t);d===0?(i.next=null,n===null?wl=c:n.next=c,c===null&&(Ii=n)):(n=i,(e!==0||(d&3)!==0)&&(jl=!0)),i=c}Xs(e)}function Hm(e,t){for(var n=e.suspendedLanes,i=e.pingedLanes,c=e.expirationTimes,d=e.pendingLanes&-62914561;0<d;){var p=31-mt(d),b=1<<p,w=c[p];w===-1?((b&n)===0||(b&i)!==0)&&(c[p]=us(b,t)):w<=t&&(e.expiredLanes|=b),d&=~b}if(t=Ke,n=Re,n=za(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i=e.callbackNode,n===0||e===t&&(He===2||He===9)||e.cancelPendingCommit!==null)return i!==null&&i!==null&&Oe(i),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||La(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(i!==null&&Oe(i),hi(n)){case 2:case 8:n=ut;break;case 32:n=Rt;break;case 268435456:n=ta;break;default:n=Rt}return i=Im.bind(null,e),n=Ne(n,i),e.callbackPriority=t,e.callbackNode=n,t}return i!==null&&i!==null&&Oe(i),e.callbackPriority=2,e.callbackNode=null,2}function Im(e,t){if(Ot!==0&&Ot!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Sl()&&e.callbackNode!==n)return null;var i=Re;return i=za(e,e===Ke?i:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i===0?null:(Em(e,i,t),Hm(e,Ze()),e.callbackNode!=null&&e.callbackNode===n?Im.bind(null,e):null)}function $m(e,t){if(Sl())return null;Em(e,t,!0)}function v0(){T0(function(){(qe&6)!==0?Ne(nt,g0):qm()})}function cu(){return ni===0&&(ni=Tr()),ni}function Gm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Ur(""+e)}function Vm(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function y0(e,t,n,i,c){if(t==="submit"&&n&&n.stateNode===c){var d=Gm((c[St]||null).action),p=i.submitter;p&&(t=(t=p[St]||null)?Gm(t.formAction):p.getAttribute("formAction"),t!==null&&(d=t,p=null));var b=new Pr("action","action",null,i,c);e.push({event:b,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(ni!==0){var w=p?Vm(c,p):new FormData(c);Ac(n,{pending:!0,data:w,method:c.method,action:d},null,w)}}else typeof d=="function"&&(b.preventDefault(),w=p?Vm(c,p):new FormData(c),Ac(n,{pending:!0,data:w,method:c.method,action:d},d,w))},currentTarget:c}]})}}for(var uu=0;uu<Ko.length;uu++){var du=Ko[uu],b0=du.toLowerCase(),_0=du[0].toUpperCase()+du.slice(1);_n(b0,"on"+_0)}_n(Ef,"onAnimationEnd"),_n(xf,"onAnimationIteration"),_n(kf,"onAnimationStart"),_n("dblclick","onDoubleClick"),_n("focusin","onFocus"),_n("focusout","onBlur"),_n(Ly,"onTransitionRun"),_n(Py,"onTransitionStart"),_n(By,"onTransitionCancel"),_n(Tf,"onTransitionEnd"),he("onMouseEnter",["mouseout","mouseover"]),he("onMouseLeave",["mouseout","mouseover"]),he("onPointerEnter",["pointerout","pointerover"]),he("onPointerLeave",["pointerout","pointerover"]),Q("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Q("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Q("onBeforeInput",["compositionend","keypress","textInput","paste"]),Q("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Q("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Q("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Qs="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),S0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Qs));function Ym(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var i=e[n],c=i.event;i=i.listeners;e:{var d=void 0;if(t)for(var p=i.length-1;0<=p;p--){var b=i[p],w=b.instance,O=b.currentTarget;if(b=b.listener,w!==d&&c.isPropagationStopped())break e;d=b,c.currentTarget=O;try{d(c)}catch(B){ul(B)}c.currentTarget=null,d=w}else for(p=0;p<i.length;p++){if(b=i[p],w=b.instance,O=b.currentTarget,b=b.listener,w!==d&&c.isPropagationStopped())break e;d=b,c.currentTarget=O;try{d(c)}catch(B){ul(B)}c.currentTarget=null,d=w}}}}function Te(e,t){var n=t[ds];n===void 0&&(n=t[ds]=new Set);var i=e+"__bubble";n.has(i)||(Fm(t,e,2,!1),n.add(i))}function fu(e,t,n){var i=0;t&&(i|=4),Fm(n,e,i,t)}var El="_reactListening"+Math.random().toString(36).slice(2);function hu(e){if(!e[El]){e[El]=!0,D.forEach(function(n){n!=="selectionchange"&&(S0.has(n)||fu(n,!1,e),fu(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[El]||(t[El]=!0,fu("selectionchange",!1,t))}}function Fm(e,t,n,i){switch(vp(t)){case 2:var c=K0;break;case 8:c=X0;break;default:c=Tu}n=c.bind(null,t,n,e),c=void 0,!Do||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(c=!0),i?c!==void 0?e.addEventListener(t,n,{capture:!0,passive:c}):e.addEventListener(t,n,!0):c!==void 0?e.addEventListener(t,n,{passive:c}):e.addEventListener(t,n,!1)}function mu(e,t,n,i,c){var d=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var p=i.tag;if(p===3||p===4){var b=i.stateNode.containerInfo;if(b===c)break;if(p===4)for(p=i.return;p!==null;){var w=p.tag;if((w===3||w===4)&&p.stateNode.containerInfo===c)return;p=p.return}for(;b!==null;){if(p=na(b),p===null)return;if(w=p.tag,w===5||w===6||w===26||w===27){i=d=p;continue e}b=b.parentNode}}i=i.return}Wd(function(){var O=d,B=Mo(n),$=[];e:{var R=Cf.get(e);if(R!==void 0){var M=Pr,pe=e;switch(e){case"keypress":if(zr(n)===0)break e;case"keydown":case"keyup":M=py;break;case"focusin":pe="focus",M=Bo;break;case"focusout":pe="blur",M=Bo;break;case"beforeblur":case"afterblur":M=Bo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":M=nf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":M=ay;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":M=yy;break;case Ef:case xf:case kf:M=ry;break;case Tf:M=_y;break;case"scroll":case"scrollend":M=ty;break;case"wheel":M=wy;break;case"copy":case"cut":case"paste":M=oy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":M=sf;break;case"toggle":case"beforetoggle":M=Ey}var fe=(t&4)!==0,Ge=!fe&&(e==="scroll"||e==="scrollend"),T=fe?R!==null?R+"Capture":null:R;fe=[];for(var k=O,A;k!==null;){var H=k;if(A=H.stateNode,H=H.tag,H!==5&&H!==26&&H!==27||A===null||T===null||(H=ps(k,T),H!=null&&fe.push(Zs(k,H,A))),Ge)break;k=k.return}0<fe.length&&(R=new M(R,pe,null,n,B),$.push({event:R,listeners:fe}))}}if((t&7)===0){e:{if(R=e==="mouseover"||e==="pointerover",M=e==="mouseout"||e==="pointerout",R&&n!==Ro&&(pe=n.relatedTarget||n.fromElement)&&(na(pe)||pe[yn]))break e;if((M||R)&&(R=B.window===B?B:(R=B.ownerDocument)?R.defaultView||R.parentWindow:window,M?(pe=n.relatedTarget||n.toElement,M=O,pe=pe?na(pe):null,pe!==null&&(Ge=u(pe),fe=pe.tag,pe!==Ge||fe!==5&&fe!==27&&fe!==6)&&(pe=null)):(M=null,pe=O),M!==pe)){if(fe=nf,H="onMouseLeave",T="onMouseEnter",k="mouse",(e==="pointerout"||e==="pointerover")&&(fe=sf,H="onPointerLeave",T="onPointerEnter",k="pointer"),Ge=M==null?R:qa(M),A=pe==null?R:qa(pe),R=new fe(H,k+"leave",M,n,B),R.target=Ge,R.relatedTarget=A,H=null,na(B)===O&&(fe=new fe(T,k+"enter",pe,n,B),fe.target=A,fe.relatedTarget=Ge,H=fe),Ge=H,M&&pe)t:{for(fe=M,T=pe,k=0,A=fe;A;A=$i(A))k++;for(A=0,H=T;H;H=$i(H))A++;for(;0<k-A;)fe=$i(fe),k--;for(;0<A-k;)T=$i(T),A--;for(;k--;){if(fe===T||T!==null&&fe===T.alternate)break t;fe=$i(fe),T=$i(T)}fe=null}else fe=null;M!==null&&Km($,R,M,fe,!1),pe!==null&&Ge!==null&&Km($,Ge,pe,fe,!0)}}e:{if(R=O?qa(O):window,M=R.nodeName&&R.nodeName.toLowerCase(),M==="select"||M==="input"&&R.type==="file")var ie=hf;else if(df(R))if(mf)ie=Uy;else{ie=Ry;var Ee=Ny}else M=R.nodeName,!M||M.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?O&&No(O.elementType)&&(ie=hf):ie=My;if(ie&&(ie=ie(e,O))){ff($,ie,n,B);break e}Ee&&Ee(e,R,O),e==="focusout"&&O&&R.type==="number"&&O.memoizedProps.value!=null&&Oo(R,"number",R.value)}switch(Ee=O?qa(O):window,e){case"focusin":(df(Ee)||Ee.contentEditable==="true")&&(bi=Ee,Vo=O,js=null);break;case"focusout":js=Vo=bi=null;break;case"mousedown":Yo=!0;break;case"contextmenu":case"mouseup":case"dragend":Yo=!1,wf($,n,B);break;case"selectionchange":if(zy)break;case"keydown":case"keyup":wf($,n,B)}var ce;if(Ho)e:{switch(e){case"compositionstart":var me="onCompositionStart";break e;case"compositionend":me="onCompositionEnd";break e;case"compositionupdate":me="onCompositionUpdate";break e}me=void 0}else yi?cf(e,n)&&(me="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(me="onCompositionStart");me&&(rf&&n.locale!=="ko"&&(yi||me!=="onCompositionStart"?me==="onCompositionEnd"&&yi&&(ce=ef()):(sa=B,zo="value"in sa?sa.value:sa.textContent,yi=!0)),Ee=xl(O,me),0<Ee.length&&(me=new af(me,e,null,n,B),$.push({event:me,listeners:Ee}),ce?me.data=ce:(ce=uf(n),ce!==null&&(me.data=ce)))),(ce=ky?Ty(e,n):Cy(e,n))&&(me=xl(O,"onBeforeInput"),0<me.length&&(Ee=new af("onBeforeInput","beforeinput",null,n,B),$.push({event:Ee,listeners:me}),Ee.data=ce)),y0($,e,O,n,B)}Ym($,t)})}function Zs(e,t,n){return{instance:e,listener:t,currentTarget:n}}function xl(e,t){for(var n=t+"Capture",i=[];e!==null;){var c=e,d=c.stateNode;if(c=c.tag,c!==5&&c!==26&&c!==27||d===null||(c=ps(e,n),c!=null&&i.unshift(Zs(e,c,d)),c=ps(e,t),c!=null&&i.push(Zs(e,c,d))),e.tag===3)return i;e=e.return}return[]}function $i(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Km(e,t,n,i,c){for(var d=t._reactName,p=[];n!==null&&n!==i;){var b=n,w=b.alternate,O=b.stateNode;if(b=b.tag,w!==null&&w===i)break;b!==5&&b!==26&&b!==27||O===null||(w=O,c?(O=ps(n,d),O!=null&&p.unshift(Zs(n,O,w))):c||(O=ps(n,d),O!=null&&p.push(Zs(n,O,w)))),n=n.return}p.length!==0&&e.push({event:t,listeners:p})}var w0=/\r\n?/g,j0=/\u0000|\uFFFD/g;function Xm(e){return(typeof e=="string"?e:""+e).replace(w0,`
`).replace(j0,"")}function Qm(e,t){return t=Xm(t),Xm(e)===t}function kl(){}function $e(e,t,n,i,c,d){switch(n){case"children":typeof i=="string"?t==="body"||t==="textarea"&&i===""||pi(e,i):(typeof i=="number"||typeof i=="bigint")&&t!=="body"&&pi(e,""+i);break;case"className":qt(e,"class",i);break;case"tabIndex":qt(e,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":qt(e,n,i);break;case"style":Zd(e,i,d);break;case"data":if(t!=="object"){qt(e,"data",i);break}case"src":case"href":if(i===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=Ur(""+i),e.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof d=="function"&&(n==="formAction"?(t!=="input"&&$e(e,t,"name",c.name,c,null),$e(e,t,"formEncType",c.formEncType,c,null),$e(e,t,"formMethod",c.formMethod,c,null),$e(e,t,"formTarget",c.formTarget,c,null)):($e(e,t,"encType",c.encType,c,null),$e(e,t,"method",c.method,c,null),$e(e,t,"target",c.target,c,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=Ur(""+i),e.setAttribute(n,i);break;case"onClick":i!=null&&(e.onclick=kl);break;case"onScroll":i!=null&&Te("scroll",e);break;case"onScrollEnd":i!=null&&Te("scrollend",e);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(l(61));if(n=i.__html,n!=null){if(c.children!=null)throw Error(l(60));e.innerHTML=n}}break;case"multiple":e.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":e.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){e.removeAttribute("xlink:href");break}n=Ur(""+i),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""+i):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":i===!0?e.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,i):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?e.setAttribute(n,i):e.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?e.removeAttribute(n):e.setAttribute(n,i);break;case"popover":Te("beforetoggle",e),Te("toggle",e),Be(e,"popover",i);break;case"xlinkActuate":bn(e,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":bn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":bn(e,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":bn(e,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":bn(e,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":bn(e,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":bn(e,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":bn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":bn(e,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":Be(e,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Wv.get(n)||n,Be(e,n,i))}}function pu(e,t,n,i,c,d){switch(n){case"style":Zd(e,i,d);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(l(61));if(n=i.__html,n!=null){if(c.children!=null)throw Error(l(60));e.innerHTML=n}}break;case"children":typeof i=="string"?pi(e,i):(typeof i=="number"||typeof i=="bigint")&&pi(e,""+i);break;case"onScroll":i!=null&&Te("scroll",e);break;case"onScrollEnd":i!=null&&Te("scrollend",e);break;case"onClick":i!=null&&(e.onclick=kl);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!L.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(c=n.endsWith("Capture"),t=n.slice(2,c?n.length-7:void 0),d=e[St]||null,d=d!=null?d[n]:null,typeof d=="function"&&e.removeEventListener(t,d,c),typeof i=="function")){typeof d!="function"&&d!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,i,c);break e}n in e?e[n]=i:i===!0?e.setAttribute(n,""):Be(e,n,i)}}}function Nt(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Te("error",e),Te("load",e);var i=!1,c=!1,d;for(d in n)if(n.hasOwnProperty(d)){var p=n[d];if(p!=null)switch(d){case"src":i=!0;break;case"srcSet":c=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,t));default:$e(e,t,d,p,n,null)}}c&&$e(e,t,"srcSet",n.srcSet,n,null),i&&$e(e,t,"src",n.src,n,null);return;case"input":Te("invalid",e);var b=d=p=c=null,w=null,O=null;for(i in n)if(n.hasOwnProperty(i)){var B=n[i];if(B!=null)switch(i){case"name":c=B;break;case"type":p=B;break;case"checked":w=B;break;case"defaultChecked":O=B;break;case"value":d=B;break;case"defaultValue":b=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(l(137,t));break;default:$e(e,t,i,B,n,null)}}Fd(e,d,b,w,O,p,c,!1),Rr(e);return;case"select":Te("invalid",e),i=p=d=null;for(c in n)if(n.hasOwnProperty(c)&&(b=n[c],b!=null))switch(c){case"value":d=b;break;case"defaultValue":p=b;break;case"multiple":i=b;default:$e(e,t,c,b,n,null)}t=d,n=p,e.multiple=!!i,t!=null?mi(e,!!i,t,!1):n!=null&&mi(e,!!i,n,!0);return;case"textarea":Te("invalid",e),d=c=i=null;for(p in n)if(n.hasOwnProperty(p)&&(b=n[p],b!=null))switch(p){case"value":i=b;break;case"defaultValue":c=b;break;case"children":d=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(l(91));break;default:$e(e,t,p,b,n,null)}Xd(e,i,c,d),Rr(e);return;case"option":for(w in n)if(n.hasOwnProperty(w)&&(i=n[w],i!=null))switch(w){case"selected":e.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:$e(e,t,w,i,n,null)}return;case"dialog":Te("beforetoggle",e),Te("toggle",e),Te("cancel",e),Te("close",e);break;case"iframe":case"object":Te("load",e);break;case"video":case"audio":for(i=0;i<Qs.length;i++)Te(Qs[i],e);break;case"image":Te("error",e),Te("load",e);break;case"details":Te("toggle",e);break;case"embed":case"source":case"link":Te("error",e),Te("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(O in n)if(n.hasOwnProperty(O)&&(i=n[O],i!=null))switch(O){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,t));default:$e(e,t,O,i,n,null)}return;default:if(No(t)){for(B in n)n.hasOwnProperty(B)&&(i=n[B],i!==void 0&&pu(e,t,B,i,n,void 0));return}}for(b in n)n.hasOwnProperty(b)&&(i=n[b],i!=null&&$e(e,t,b,i,n,null))}function E0(e,t,n,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var c=null,d=null,p=null,b=null,w=null,O=null,B=null;for(M in n){var $=n[M];if(n.hasOwnProperty(M)&&$!=null)switch(M){case"checked":break;case"value":break;case"defaultValue":w=$;default:i.hasOwnProperty(M)||$e(e,t,M,null,i,$)}}for(var R in i){var M=i[R];if($=n[R],i.hasOwnProperty(R)&&(M!=null||$!=null))switch(R){case"type":d=M;break;case"name":c=M;break;case"checked":O=M;break;case"defaultChecked":B=M;break;case"value":p=M;break;case"defaultValue":b=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(l(137,t));break;default:M!==$&&$e(e,t,R,M,i,$)}}Ao(e,p,b,w,O,B,d,c);return;case"select":M=p=b=R=null;for(d in n)if(w=n[d],n.hasOwnProperty(d)&&w!=null)switch(d){case"value":break;case"multiple":M=w;default:i.hasOwnProperty(d)||$e(e,t,d,null,i,w)}for(c in i)if(d=i[c],w=n[c],i.hasOwnProperty(c)&&(d!=null||w!=null))switch(c){case"value":R=d;break;case"defaultValue":b=d;break;case"multiple":p=d;default:d!==w&&$e(e,t,c,d,i,w)}t=b,n=p,i=M,R!=null?mi(e,!!n,R,!1):!!i!=!!n&&(t!=null?mi(e,!!n,t,!0):mi(e,!!n,n?[]:"",!1));return;case"textarea":M=R=null;for(b in n)if(c=n[b],n.hasOwnProperty(b)&&c!=null&&!i.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:$e(e,t,b,null,i,c)}for(p in i)if(c=i[p],d=n[p],i.hasOwnProperty(p)&&(c!=null||d!=null))switch(p){case"value":R=c;break;case"defaultValue":M=c;break;case"children":break;case"dangerouslySetInnerHTML":if(c!=null)throw Error(l(91));break;default:c!==d&&$e(e,t,p,c,i,d)}Kd(e,R,M);return;case"option":for(var pe in n)if(R=n[pe],n.hasOwnProperty(pe)&&R!=null&&!i.hasOwnProperty(pe))switch(pe){case"selected":e.selected=!1;break;default:$e(e,t,pe,null,i,R)}for(w in i)if(R=i[w],M=n[w],i.hasOwnProperty(w)&&R!==M&&(R!=null||M!=null))switch(w){case"selected":e.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:$e(e,t,w,R,i,M)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var fe in n)R=n[fe],n.hasOwnProperty(fe)&&R!=null&&!i.hasOwnProperty(fe)&&$e(e,t,fe,null,i,R);for(O in i)if(R=i[O],M=n[O],i.hasOwnProperty(O)&&R!==M&&(R!=null||M!=null))switch(O){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(l(137,t));break;default:$e(e,t,O,R,i,M)}return;default:if(No(t)){for(var Ge in n)R=n[Ge],n.hasOwnProperty(Ge)&&R!==void 0&&!i.hasOwnProperty(Ge)&&pu(e,t,Ge,void 0,i,R);for(B in i)R=i[B],M=n[B],!i.hasOwnProperty(B)||R===M||R===void 0&&M===void 0||pu(e,t,B,R,i,M);return}}for(var T in n)R=n[T],n.hasOwnProperty(T)&&R!=null&&!i.hasOwnProperty(T)&&$e(e,t,T,null,i,R);for($ in i)R=i[$],M=n[$],!i.hasOwnProperty($)||R===M||R==null&&M==null||$e(e,t,$,R,i,M)}var gu=null,vu=null;function Tl(e){return e.nodeType===9?e:e.ownerDocument}function Zm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Jm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function yu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var bu=null;function x0(){var e=window.event;return e&&e.type==="popstate"?e===bu?!1:(bu=e,!0):(bu=null,!1)}var Wm=typeof setTimeout=="function"?setTimeout:void 0,k0=typeof clearTimeout=="function"?clearTimeout:void 0,ep=typeof Promise=="function"?Promise:void 0,T0=typeof queueMicrotask=="function"?queueMicrotask:typeof ep!="undefined"?function(e){return ep.resolve(null).then(e).catch(C0)}:Wm;function C0(e){setTimeout(function(){throw e})}function Sa(e){return e==="head"}function tp(e,t){var n=t,i=0,c=0;do{var d=n.nextSibling;if(e.removeChild(n),d&&d.nodeType===8)if(n=d.data,n==="/$"){if(0<i&&8>i){n=i;var p=e.ownerDocument;if(n&1&&Js(p.documentElement),n&2&&Js(p.body),n&4)for(n=p.head,Js(n),p=n.firstChild;p;){var b=p.nextSibling,w=p.nodeName;p[Ba]||w==="SCRIPT"||w==="STYLE"||w==="LINK"&&p.rel.toLowerCase()==="stylesheet"||n.removeChild(p),p=b}}if(c===0){e.removeChild(d),rr(t);return}c--}else n==="$"||n==="$?"||n==="$!"?c++:i=n.charCodeAt(0)-48;else i=0;n=d}while(n);rr(t)}function _u(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":_u(n),hs(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function A0(e,t,n,i){for(;e.nodeType===1;){var c=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!i&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(i){if(!e[Ba])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(d=e.getAttribute("rel"),d==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(d!==c.rel||e.getAttribute("href")!==(c.href==null||c.href===""?null:c.href)||e.getAttribute("crossorigin")!==(c.crossOrigin==null?null:c.crossOrigin)||e.getAttribute("title")!==(c.title==null?null:c.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(d=e.getAttribute("src"),(d!==(c.src==null?null:c.src)||e.getAttribute("type")!==(c.type==null?null:c.type)||e.getAttribute("crossorigin")!==(c.crossOrigin==null?null:c.crossOrigin))&&d&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var d=c.name==null?null:""+c.name;if(c.type==="hidden"&&e.getAttribute("name")===d)return e}else return e;if(e=wn(e.nextSibling),e===null)break}return null}function O0(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=wn(e.nextSibling),e===null))return null;return e}function Su(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function N0(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var i=function(){t(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),e._reactRetry=i}}function wn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var wu=null;function np(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function ap(e,t,n){switch(t=Tl(n),e){case"html":if(e=t.documentElement,!e)throw Error(l(452));return e;case"head":if(e=t.head,!e)throw Error(l(453));return e;case"body":if(e=t.body,!e)throw Error(l(454));return e;default:throw Error(l(451))}}function Js(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);hs(e)}var mn=new Map,ip=new Set;function Cl(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Xn=G.d;G.d={f:R0,r:M0,D:U0,C:D0,L:z0,m:L0,X:B0,S:P0,M:q0};function R0(){var e=Xn.f(),t=bl();return e||t}function M0(e){var t=aa(e);t!==null&&t.tag===5&&t.type==="form"?Eh(t):Xn.r(e)}var Gi=typeof document=="undefined"?null:document;function sp(e,t,n){var i=Gi;if(i&&typeof t=="string"&&t){var c=ln(t);c='link[rel="'+e+'"][href="'+c+'"]',typeof n=="string"&&(c+='[crossorigin="'+n+'"]'),ip.has(c)||(ip.add(c),e={rel:e,crossOrigin:n,href:t},i.querySelector(c)===null&&(t=i.createElement("link"),Nt(t,"link",e),N(t),i.head.appendChild(t)))}}function U0(e){Xn.D(e),sp("dns-prefetch",e,null)}function D0(e,t){Xn.C(e,t),sp("preconnect",e,t)}function z0(e,t,n){Xn.L(e,t,n);var i=Gi;if(i&&e&&t){var c='link[rel="preload"][as="'+ln(t)+'"]';t==="image"&&n&&n.imageSrcSet?(c+='[imagesrcset="'+ln(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(c+='[imagesizes="'+ln(n.imageSizes)+'"]')):c+='[href="'+ln(e)+'"]';var d=c;switch(t){case"style":d=Vi(e);break;case"script":d=Yi(e)}mn.has(d)||(e=y({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),mn.set(d,e),i.querySelector(c)!==null||t==="style"&&i.querySelector(Ws(d))||t==="script"&&i.querySelector(er(d))||(t=i.createElement("link"),Nt(t,"link",e),N(t),i.head.appendChild(t)))}}function L0(e,t){Xn.m(e,t);var n=Gi;if(n&&e){var i=t&&typeof t.as=="string"?t.as:"script",c='link[rel="modulepreload"][as="'+ln(i)+'"][href="'+ln(e)+'"]',d=c;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":d=Yi(e)}if(!mn.has(d)&&(e=y({rel:"modulepreload",href:e},t),mn.set(d,e),n.querySelector(c)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(er(d)))return}i=n.createElement("link"),Nt(i,"link",e),N(i),n.head.appendChild(i)}}}function P0(e,t,n){Xn.S(e,t,n);var i=Gi;if(i&&e){var c=_(i).hoistableStyles,d=Vi(e);t=t||"default";var p=c.get(d);if(!p){var b={loading:0,preload:null};if(p=i.querySelector(Ws(d)))b.loading=5;else{e=y({rel:"stylesheet",href:e,"data-precedence":t},n),(n=mn.get(d))&&ju(e,n);var w=p=i.createElement("link");N(w),Nt(w,"link",e),w._p=new Promise(function(O,B){w.onload=O,w.onerror=B}),w.addEventListener("load",function(){b.loading|=1}),w.addEventListener("error",function(){b.loading|=2}),b.loading|=4,Al(p,t,i)}p={type:"stylesheet",instance:p,count:1,state:b},c.set(d,p)}}}function B0(e,t){Xn.X(e,t);var n=Gi;if(n&&e){var i=_(n).hoistableScripts,c=Yi(e),d=i.get(c);d||(d=n.querySelector(er(c)),d||(e=y({src:e,async:!0},t),(t=mn.get(c))&&Eu(e,t),d=n.createElement("script"),N(d),Nt(d,"link",e),n.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},i.set(c,d))}}function q0(e,t){Xn.M(e,t);var n=Gi;if(n&&e){var i=_(n).hoistableScripts,c=Yi(e),d=i.get(c);d||(d=n.querySelector(er(c)),d||(e=y({src:e,async:!0,type:"module"},t),(t=mn.get(c))&&Eu(e,t),d=n.createElement("script"),N(d),Nt(d,"link",e),n.head.appendChild(d)),d={type:"script",instance:d,count:1,state:null},i.set(c,d))}}function rp(e,t,n,i){var c=(c=de.current)?Cl(c):null;if(!c)throw Error(l(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=Vi(n.href),n=_(c).hoistableStyles,i=n.get(t),i||(i={type:"style",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=Vi(n.href);var d=_(c).hoistableStyles,p=d.get(e);if(p||(c=c.ownerDocument||c,p={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},d.set(e,p),(d=c.querySelector(Ws(e)))&&!d._p&&(p.instance=d,p.state.loading=5),mn.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},mn.set(e,n),d||H0(c,e,n,p.state))),t&&i===null)throw Error(l(528,""));return p}if(t&&i!==null)throw Error(l(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Yi(n),n=_(c).hoistableScripts,i=n.get(t),i||(i={type:"script",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,e))}}function Vi(e){return'href="'+ln(e)+'"'}function Ws(e){return'link[rel="stylesheet"]['+e+"]"}function lp(e){return y({},e,{"data-precedence":e.precedence,precedence:null})}function H0(e,t,n,i){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?i.loading=1:(t=e.createElement("link"),i.preload=t,t.addEventListener("load",function(){return i.loading|=1}),t.addEventListener("error",function(){return i.loading|=2}),Nt(t,"link",n),N(t),e.head.appendChild(t))}function Yi(e){return'[src="'+ln(e)+'"]'}function er(e){return"script[async]"+e}function op(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var i=e.querySelector('style[data-href~="'+ln(n.href)+'"]');if(i)return t.instance=i,N(i),i;var c=y({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(e.ownerDocument||e).createElement("style"),N(i),Nt(i,"style",c),Al(i,n.precedence,e),t.instance=i;case"stylesheet":c=Vi(n.href);var d=e.querySelector(Ws(c));if(d)return t.state.loading|=4,t.instance=d,N(d),d;i=lp(n),(c=mn.get(c))&&ju(i,c),d=(e.ownerDocument||e).createElement("link"),N(d);var p=d;return p._p=new Promise(function(b,w){p.onload=b,p.onerror=w}),Nt(d,"link",i),t.state.loading|=4,Al(d,n.precedence,e),t.instance=d;case"script":return d=Yi(n.src),(c=e.querySelector(er(d)))?(t.instance=c,N(c),c):(i=n,(c=mn.get(d))&&(i=y({},n),Eu(i,c)),e=e.ownerDocument||e,c=e.createElement("script"),N(c),Nt(c,"link",i),e.head.appendChild(c),t.instance=c);case"void":return null;default:throw Error(l(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(i=t.instance,t.state.loading|=4,Al(i,n.precedence,e));return t.instance}function Al(e,t,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),c=i.length?i[i.length-1]:null,d=c,p=0;p<i.length;p++){var b=i[p];if(b.dataset.precedence===t)d=b;else if(d!==c)break}d?d.parentNode.insertBefore(e,d.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function ju(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Eu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ol=null;function cp(e,t,n){if(Ol===null){var i=new Map,c=Ol=new Map;c.set(n,i)}else c=Ol,i=c.get(n),i||(i=new Map,c.set(n,i));if(i.has(e))return i;for(i.set(e,null),n=n.getElementsByTagName(e),c=0;c<n.length;c++){var d=n[c];if(!(d[Ba]||d[_t]||e==="link"&&d.getAttribute("rel")==="stylesheet")&&d.namespaceURI!=="http://www.w3.org/2000/svg"){var p=d.getAttribute(t)||"";p=e+p;var b=i.get(p);b?b.push(d):i.set(p,[d])}}return i}function up(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function I0(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function dp(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var tr=null;function $0(){}function G0(e,t,n){if(tr===null)throw Error(l(475));var i=tr;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var c=Vi(n.href),d=e.querySelector(Ws(c));if(d){e=d._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(i.count++,i=Nl.bind(i),e.then(i,i)),t.state.loading|=4,t.instance=d,N(d);return}d=e.ownerDocument||e,n=lp(n),(c=mn.get(c))&&ju(n,c),d=d.createElement("link"),N(d);var p=d;p._p=new Promise(function(b,w){p.onload=b,p.onerror=w}),Nt(d,"link",n),t.instance=d}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(i.count++,t=Nl.bind(i),e.addEventListener("load",t),e.addEventListener("error",t))}}function V0(){if(tr===null)throw Error(l(475));var e=tr;return e.stylesheets&&e.count===0&&xu(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&xu(e,e.stylesheets),e.unsuspend){var i=e.unsuspend;e.unsuspend=null,i()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Nl(){if(this.count--,this.count===0){if(this.stylesheets)xu(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Rl=null;function xu(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Rl=new Map,t.forEach(Y0,e),Rl=null,Nl.call(e))}function Y0(e,t){if(!(t.state.loading&4)){var n=Rl.get(e);if(n)var i=n.get(null);else{n=new Map,Rl.set(e,n);for(var c=e.querySelectorAll("link[data-precedence],style[data-precedence]"),d=0;d<c.length;d++){var p=c[d];(p.nodeName==="LINK"||p.getAttribute("media")!=="not all")&&(n.set(p.dataset.precedence,p),i=p)}i&&n.set(null,i)}c=t.instance,p=c.getAttribute("data-precedence"),d=n.get(p)||i,d===i&&n.set(null,c),n.set(p,c),this.count++,i=Nl.bind(this),c.addEventListener("load",i),c.addEventListener("error",i),d?d.parentNode.insertBefore(c,d.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(c,e.firstChild)),t.state.loading|=4}}var nr={$$typeof:q,Provider:null,Consumer:null,_currentValue:ae,_currentValue2:ae,_threadCount:0};function F0(e,t,n,i,c,d,p,b){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Dn(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Dn(0),this.hiddenUpdates=Dn(null),this.identifierPrefix=i,this.onUncaughtError=c,this.onCaughtError=d,this.onRecoverableError=p,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function fp(e,t,n,i,c,d,p,b,w,O,B,$){return e=new F0(e,t,n,p,b,w,O,$),t=1,d===!0&&(t|=24),d=Zt(3,null,null,t),e.current=d,d.stateNode=e,t=rc(),t.refCount++,e.pooledCache=t,t.refCount++,d.memoizedState={element:i,isDehydrated:n,cache:t},uc(d),e}function hp(e){return e?(e=ji,e):ji}function mp(e,t,n,i,c,d){c=hp(c),i.context===null?i.context=c:i.pendingContext=c,i=oa(t),i.payload={element:n},d=d===void 0?null:d,d!==null&&(i.callback=d),n=ca(e,i,t),n!==null&&(nn(n,e,t),Rs(n,e,t))}function pp(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ku(e,t){pp(e,t),(e=e.alternate)&&pp(e,t)}function gp(e){if(e.tag===13){var t=wi(e,67108864);t!==null&&nn(t,e,67108864),ku(e,67108864)}}var Ml=!0;function K0(e,t,n,i){var c=z.T;z.T=null;var d=G.p;try{G.p=2,Tu(e,t,n,i)}finally{G.p=d,z.T=c}}function X0(e,t,n,i){var c=z.T;z.T=null;var d=G.p;try{G.p=8,Tu(e,t,n,i)}finally{G.p=d,z.T=c}}function Tu(e,t,n,i){if(Ml){var c=Cu(i);if(c===null)mu(e,t,i,Ul,n),yp(e,i);else if(Z0(c,e,t,n,i))i.stopPropagation();else if(yp(e,i),t&4&&-1<Q0.indexOf(e)){for(;c!==null;){var d=aa(c);if(d!==null)switch(d.tag){case 3:if(d=d.stateNode,d.current.memoizedState.isDehydrated){var p=Un(d.pendingLanes);if(p!==0){var b=d;for(b.pendingLanes|=2,b.entangledLanes|=2;p;){var w=1<<31-mt(p);b.entanglements[1]|=w,p&=~w}Rn(d),(qe&6)===0&&(vl=Ze()+500,Xs(0))}}break;case 13:b=wi(d,2),b!==null&&nn(b,d,2),bl(),ku(d,2)}if(d=Cu(i),d===null&&mu(e,t,i,Ul,n),d===c)break;c=d}c!==null&&i.stopPropagation()}else mu(e,t,i,null,n)}}function Cu(e){return e=Mo(e),Au(e)}var Ul=null;function Au(e){if(Ul=null,e=na(e),e!==null){var t=u(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=f(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Ul=e,null}function vp(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Xt()){case nt:return 2;case ut:return 8;case Rt:case Da:return 32;case ta:return 268435456;default:return 32}default:return 32}}var Ou=!1,wa=null,ja=null,Ea=null,ar=new Map,ir=new Map,xa=[],Q0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function yp(e,t){switch(e){case"focusin":case"focusout":wa=null;break;case"dragenter":case"dragleave":ja=null;break;case"mouseover":case"mouseout":Ea=null;break;case"pointerover":case"pointerout":ar.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ir.delete(t.pointerId)}}function sr(e,t,n,i,c,d){return e===null||e.nativeEvent!==d?(e={blockedOn:t,domEventName:n,eventSystemFlags:i,nativeEvent:d,targetContainers:[c]},t!==null&&(t=aa(t),t!==null&&gp(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,c!==null&&t.indexOf(c)===-1&&t.push(c),e)}function Z0(e,t,n,i,c){switch(t){case"focusin":return wa=sr(wa,e,t,n,i,c),!0;case"dragenter":return ja=sr(ja,e,t,n,i,c),!0;case"mouseover":return Ea=sr(Ea,e,t,n,i,c),!0;case"pointerover":var d=c.pointerId;return ar.set(d,sr(ar.get(d)||null,e,t,n,i,c)),!0;case"gotpointercapture":return d=c.pointerId,ir.set(d,sr(ir.get(d)||null,e,t,n,i,c)),!0}return!1}function bp(e){var t=na(e.target);if(t!==null){var n=u(t);if(n!==null){if(t=n.tag,t===13){if(t=f(n),t!==null){e.blockedOn=t,xo(e.priority,function(){if(n.tag===13){var i=tn();i=fi(i);var c=wi(n,i);c!==null&&nn(c,n,i),ku(n,i)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Dl(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Cu(e.nativeEvent);if(n===null){n=e.nativeEvent;var i=new n.constructor(n.type,n);Ro=i,n.target.dispatchEvent(i),Ro=null}else return t=aa(n),t!==null&&gp(t),e.blockedOn=n,!1;t.shift()}return!0}function _p(e,t,n){Dl(e)&&n.delete(t)}function J0(){Ou=!1,wa!==null&&Dl(wa)&&(wa=null),ja!==null&&Dl(ja)&&(ja=null),Ea!==null&&Dl(Ea)&&(Ea=null),ar.forEach(_p),ir.forEach(_p)}function zl(e,t){e.blockedOn===t&&(e.blockedOn=null,Ou||(Ou=!0,s.unstable_scheduleCallback(s.unstable_NormalPriority,J0)))}var Ll=null;function Sp(e){Ll!==e&&(Ll=e,s.unstable_scheduleCallback(s.unstable_NormalPriority,function(){Ll===e&&(Ll=null);for(var t=0;t<e.length;t+=3){var n=e[t],i=e[t+1],c=e[t+2];if(typeof i!="function"){if(Au(i||n)===null)continue;break}var d=aa(n);d!==null&&(e.splice(t,3),t-=3,Ac(d,{pending:!0,data:c,method:n.method,action:i},i,c))}}))}function rr(e){function t(w){return zl(w,e)}wa!==null&&zl(wa,e),ja!==null&&zl(ja,e),Ea!==null&&zl(Ea,e),ar.forEach(t),ir.forEach(t);for(var n=0;n<xa.length;n++){var i=xa[n];i.blockedOn===e&&(i.blockedOn=null)}for(;0<xa.length&&(n=xa[0],n.blockedOn===null);)bp(n),n.blockedOn===null&&xa.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var c=n[i],d=n[i+1],p=c[St]||null;if(typeof d=="function")p||Sp(n);else if(p){var b=null;if(d&&d.hasAttribute("formAction")){if(c=d,p=d[St]||null)b=p.formAction;else if(Au(c)!==null)continue}else b=p.action;typeof b=="function"?n[i+1]=b:(n.splice(i,3),i-=3),Sp(n)}}}function Nu(e){this._internalRoot=e}Pl.prototype.render=Nu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(l(409));var n=t.current,i=tn();mp(n,i,e,t,null,null)},Pl.prototype.unmount=Nu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;mp(e.current,2,null,e,null,null),bl(),t[yn]=null}};function Pl(e){this._internalRoot=e}Pl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nr();e={blockedOn:null,target:e,priority:t};for(var n=0;n<xa.length&&t!==0&&t<xa[n].priority;n++);xa.splice(n,0,e),n===0&&bp(e)}};var wp=a.version;if(wp!=="19.1.0")throw Error(l(527,wp,"19.1.0"));G.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(l(188)):(e=Object.keys(e).join(","),Error(l(268,e)));return e=g(t),e=e!==null?v(e):null,e=e===null?null:e.stateNode,e};var W0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"){var Bl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Bl.isDisabled&&Bl.supportsFiber)try{bt=Bl.inject(W0),ht=Bl}catch(e){}}return or.createRoot=function(e,t){if(!o(e))throw Error(l(299));var n=!1,i="",c=Ph,d=Bh,p=qh,b=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onUncaughtError!==void 0&&(c=t.onUncaughtError),t.onCaughtError!==void 0&&(d=t.onCaughtError),t.onRecoverableError!==void 0&&(p=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(b=t.unstable_transitionCallbacks)),t=fp(e,1,!1,null,null,n,i,c,d,p,b,null),e[yn]=t.current,hu(e),new Nu(t)},or.hydrateRoot=function(e,t,n){if(!o(e))throw Error(l(299));var i=!1,c="",d=Ph,p=Bh,b=qh,w=null,O=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(c=n.identifierPrefix),n.onUncaughtError!==void 0&&(d=n.onUncaughtError),n.onCaughtError!==void 0&&(p=n.onCaughtError),n.onRecoverableError!==void 0&&(b=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(w=n.unstable_transitionCallbacks),n.formState!==void 0&&(O=n.formState)),t=fp(e,1,!0,t,n!=null?n:null,i,c,d,p,b,w,O),t.context=hp(null),n=t.current,i=tn(),i=fi(i),c=oa(i),c.callback=null,ca(n,c,i),n=i,t.current.lanes=n,Pa(t,n),Rn(t),e[yn]=t.current,hu(e),new Pl(t)},or.version="19.1.0",or}var Dp;function hb(){if(Dp)return Du.exports;Dp=1;function s(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(s)}catch(a){console.error(a)}}return s(),Du.exports=fb(),Du.exports}var mb=hb();const pb=Md(mb);var $g={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},zp=P.createContext&&P.createContext($g),gb=["attr","size","title"];function vb(s,a){if(s==null)return{};var r=yb(s,a),l,o;if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(s);for(o=0;o<u.length;o++)l=u[o],!(a.indexOf(l)>=0)&&Object.prototype.propertyIsEnumerable.call(s,l)&&(r[l]=s[l])}return r}function yb(s,a){if(s==null)return{};var r={};for(var l in s)if(Object.prototype.hasOwnProperty.call(s,l)){if(a.indexOf(l)>=0)continue;r[l]=s[l]}return r}function ro(){return ro=Object.assign?Object.assign.bind():function(s){for(var a=1;a<arguments.length;a++){var r=arguments[a];for(var l in r)Object.prototype.hasOwnProperty.call(r,l)&&(s[l]=r[l])}return s},ro.apply(this,arguments)}function Lp(s,a){var r=Object.keys(s);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(s);a&&(l=l.filter(function(o){return Object.getOwnPropertyDescriptor(s,o).enumerable})),r.push.apply(r,l)}return r}function lo(s){for(var a=1;a<arguments.length;a++){var r=arguments[a]!=null?arguments[a]:{};a%2?Lp(Object(r),!0).forEach(function(l){bb(s,l,r[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(r)):Lp(Object(r)).forEach(function(l){Object.defineProperty(s,l,Object.getOwnPropertyDescriptor(r,l))})}return s}function bb(s,a,r){return a=_b(a),a in s?Object.defineProperty(s,a,{value:r,enumerable:!0,configurable:!0,writable:!0}):s[a]=r,s}function _b(s){var a=Sb(s,"string");return typeof a=="symbol"?a:a+""}function Sb(s,a){if(typeof s!="object"||!s)return s;var r=s[Symbol.toPrimitive];if(r!==void 0){var l=r.call(s,a);if(typeof l!="object")return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return(a==="string"?String:Number)(s)}function Gg(s){return s&&s.map((a,r)=>P.createElement(a.tag,lo({key:r},a.attr),Gg(a.child)))}function Ft(s){return a=>P.createElement(wb,ro({attr:lo({},s.attr)},a),Gg(s.child))}function wb(s){var a=r=>{var{attr:l,size:o,title:u}=s,f=vb(s,gb),m=o||r.size||"1em",g;return r.className&&(g=r.className),s.className&&(g=(g?g+" ":"")+s.className),P.createElement("svg",ro({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,l,f,{className:g,style:lo(lo({color:s.color||r.color},r.style),s.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),u&&P.createElement("title",null,u),s.children)};return zp!==void 0?P.createElement(zp.Consumer,null,r=>a(r)):a($g)}function Pp(s){return Ft({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M239.1 6.3l-208 78c-18.7 7-31.1 25-31.1 45v225.1c0 18.2 10.3 34.8 26.5 42.9l208 104c13.5 6.8 29.4 6.8 42.9 0l208-104c16.3-8.1 26.5-24.8 26.5-42.9V129.3c0-20-12.4-37.9-31.1-44.9l-208-78C262 2.2 250 2.2 239.1 6.3zM256 68.4l192 72v1.1l-192 78-192-78v-1.1l192-72zm32 356V275.5l160-65v133.9l-160 80z"},child:[]}]})(s)}function Hl(s){return Ft({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M448 73.143v45.714C448 159.143 347.667 192 224 192S0 159.143 0 118.857V73.143C0 32.857 100.333 0 224 0s224 32.857 224 73.143zM448 176v102.857C448 319.143 347.667 352 224 352S0 319.143 0 278.857V176c48.125 33.143 136.208 48.572 224 48.572S399.874 209.143 448 176zm0 160v102.857C448 479.143 347.667 512 224 512S0 479.143 0 438.857V336c48.125 33.143 136.208 48.572 224 48.572S399.874 369.143 448 336z"},child:[]}]})(s)}function jb(s){return Ft({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M216 0h80c13.3 0 24 10.7 24 24v168h87.7c17.8 0 26.7 21.5 14.1 34.1L269.7 378.3c-7.5 7.5-19.8 7.5-27.3 0L90.1 226.1c-12.6-12.6-3.7-34.1 14.1-34.1H192V24c0-13.3 10.7-24 24-24zm296 376v112c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V376c0-13.3 10.7-24 24-24h146.7l49 49c20.1 20.1 52.5 20.1 72.6 0l49-49H488c13.3 0 24 10.7 24 24zm-124 88c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zm64 0c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z"},child:[]}]})(s)}function Eb(s){return Ft({attr:{viewBox:"0 0 192 512"},child:[{tag:"path",attr:{d:"M96 184c39.8 0 72 32.2 72 72s-32.2 72-72 72-72-32.2-72-72 32.2-72 72-72zM24 80c0 39.8 32.2 72 72 72s72-32.2 72-72S135.8 8 96 8 24 40.2 24 80zm0 352c0 39.8 32.2 72 72 72s72-32.2 72-72-32.2-72-72-72-72 32.2-72 72z"},child:[]}]})(s)}function Bp(s){return Ft({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm160-14.1v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z"},child:[]}]})(s)}function qp(s){return Ft({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M464,128H272L208,64H48A48,48,0,0,0,0,112V400a48,48,0,0,0,48,48H464a48,48,0,0,0,48-48V176A48,48,0,0,0,464,128ZM359.5,296a16,16,0,0,1-16,16h-64v64a16,16,0,0,1-16,16h-16a16,16,0,0,1-16-16V312h-64a16,16,0,0,1-16-16V280a16,16,0,0,1,16-16h64V200a16,16,0,0,1,16-16h16a16,16,0,0,1,16,16v64h64a16,16,0,0,1,16,16Z"},child:[]}]})(s)}function Il(s){return Ft({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M464 128H272l-64-64H48C21.49 64 0 85.49 0 112v288c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V176c0-26.51-21.49-48-48-48z"},child:[]}]})(s)}function xb(s){return Ft({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 255.531c.253 136.64-111.18 248.372-247.82 248.468-59.015.042-113.223-20.53-155.822-54.911-11.077-8.94-11.905-25.541-1.839-35.607l11.267-11.267c8.609-8.609 22.353-9.551 31.891-1.984C173.062 425.135 212.781 440 256 440c101.705 0 184-82.311 184-184 0-101.705-82.311-184-184-184-48.814 0-93.149 18.969-126.068 49.932l50.754 50.754c10.08 10.08 2.941 27.314-11.313 27.314H24c-8.837 0-16-7.163-16-16V38.627c0-14.254 17.234-21.393 27.314-11.314l49.372 49.372C129.209 34.136 189.552 8 256 8c136.81 0 247.747 110.78 248 247.531zm-180.912 78.784l9.823-12.63c8.138-10.463 6.253-25.542-4.21-33.679L288 256.349V152c0-13.255-10.745-24-24-24h-16c-13.255 0-24 10.745-24 24v135.651l65.409 50.874c10.463 8.137 25.541 6.253 33.679-4.21z"},child:[]}]})(s)}function Hp(s){return Ft({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M640 264v-16c0-8.84-7.16-16-16-16H344v-40h72c17.67 0 32-14.33 32-32V32c0-17.67-14.33-32-32-32H224c-17.67 0-32 14.33-32 32v128c0 17.67 14.33 32 32 32h72v40H16c-8.84 0-16 7.16-16 16v16c0 8.84 7.16 16 16 16h104v40H64c-17.67 0-32 14.33-32 32v128c0 17.67 14.33 32 32 32h160c17.67 0 32-14.33 32-32V352c0-17.67-14.33-32-32-32h-56v-40h304v40h-56c-17.67 0-32 14.33-32 32v128c0 17.67 14.33 32 32 32h160c17.67 0 32-14.33 32-32V352c0-17.67-14.33-32-32-32h-56v-40h104c8.84 0 16-7.16 16-16zM256 128V64h128v64H256zm-64 320H96v-64h96v64zm352 0h-96v-64h96v64z"},child:[]}]})(s)}function kb(s){return Ft({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z"},child:[]}]})(s)}function Ip(s){return Ft({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"},child:[]}]})(s)}function Bu(s){return Ft({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"},child:[]}]})(s)}function Tb(s){return Ft({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M440.65 12.57l4 82.77A247.16 247.16 0 0 0 255.83 8C134.73 8 33.91 94.92 12.29 209.82A12 12 0 0 0 24.09 224h49.05a12 12 0 0 0 11.67-9.26 175.91 175.91 0 0 1 317-56.94l-101.46-4.86a12 12 0 0 0-12.57 12v47.41a12 12 0 0 0 12 12H500a12 12 0 0 0 12-12V12a12 12 0 0 0-12-12h-47.37a12 12 0 0 0-11.98 12.57zM255.83 432a175.61 175.61 0 0 1-146-77.8l101.8 4.87a12 12 0 0 0 12.57-12v-47.4a12 12 0 0 0-12-12H12a12 12 0 0 0-12 12V500a12 12 0 0 0 12 12h47.35a12 12 0 0 0 12-12.6l-4.15-82.57A247.17 247.17 0 0 0 255.83 504c121.11 0 221.93-86.92 243.55-201.82a12 12 0 0 0-11.8-14.18h-49.05a12 12 0 0 0-11.67 9.26A175.86 175.86 0 0 1 255.83 432z"},child:[]}]})(s)}function $p(s){return Ft({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M461.2 128H80c-8.84 0-16-7.16-16-16s7.16-16 16-16h384c8.84 0 16-7.16 16-16 0-26.51-21.49-48-48-48H64C28.65 32 0 60.65 0 96v320c0 35.35 28.65 64 64 64h397.2c28.02 0 50.8-21.53 50.8-48V176c0-26.47-22.78-48-50.8-48zM416 336c-17.67 0-32-14.33-32-32s14.33-32 32-32 32 14.33 32 32-14.33 32-32 32z"},child:[]}]})(s)}var Cb=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function Vg({packageName:s,customMessages:a}){let r=s;const l=se(se({},Cb),a);function o(u,f){if(!f)return`${r}: ${u}`;let m=u;const g=u.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);for(const v of g){const y=(f[v[1]]||"").toString();m=m.replace(`{{${v[1]}}}`,y)}return`${r}: ${m}`}return{setPackageName({packageName:u}){return typeof u=="string"&&(r=u),this},setMessages({customMessages:u}){return Object.assign(l,u||{}),this},throwInvalidPublishableKeyError(u){throw new Error(o(l.InvalidPublishableKeyErrorMessage,u))},throwInvalidProxyUrl(u){throw new Error(o(l.InvalidProxyUrlErrorMessage,u))},throwMissingPublishableKeyError(){throw new Error(o(l.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw new Error(o(l.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(u){throw new Error(o(l.MissingClerkProvider,u))},throw(u){throw new Error(o(u))}}}var Yg=Object.defineProperty,Ab=Object.getOwnPropertyDescriptor,Ob=Object.getOwnPropertyNames,Nb=Object.prototype.hasOwnProperty,Rb=(s,a)=>{for(var r in a)Yg(s,r,{get:a[r],enumerable:!0})},Mb=(s,a,r,l)=>{if(a&&typeof a=="object"||typeof a=="function")for(let o of Ob(a))!Nb.call(s,o)&&o!==r&&Yg(s,o,{get:()=>a[o],enumerable:!(l=Ab(a,o))||l.enumerable});return s},Ub=(s,a,r)=>(Mb(s,a,"default"),r),Db={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},zb=new Set(["first_factor","second_factor","multi_factor"]),Lb=new Set(["strict_mfa","strict","moderate","lax"]),Pb=s=>typeof s=="number"&&s>0,Bb=s=>zb.has(s),qb=s=>Lb.has(s),qu=s=>s.replace(/^(org:)*/,"org:"),Hb=(s,a)=>{const{orgId:r,orgRole:l,orgPermissions:o}=a;return!s.role&&!s.permission||!r||!l||!o?null:s.permission?o.includes(qu(s.permission)):s.role?qu(l)===qu(s.role):null},Gp=(s,a)=>{const{org:r,user:l}=$b(s),[o,u]=a.split(":"),f=u||o;return o==="org"?r.includes(f):o==="user"?l.includes(f):[...r,...l].includes(f)},Ib=(s,a)=>{const{features:r,plans:l}=a;return s.feature&&r?Gp(r,s.feature):s.plan&&l?Gp(l,s.plan):null},$b=s=>{const a=s?s.split(",").map(r=>r.trim()):[];return{org:a.filter(r=>r.split(":")[0].includes("o")).map(r=>r.split(":")[1]),user:a.filter(r=>r.split(":")[0].includes("u")).map(r=>r.split(":")[1])}},Gb=s=>{if(!s)return!1;const a=o=>typeof o=="string"?Db[o]:o,r=typeof s=="string"&&qb(s),l=typeof s=="object"&&Bb(s.level)&&Pb(s.afterMinutes);return r||l?a.bind(null,s):!1},Vb=(s,{factorVerificationAge:a})=>{if(!s.reverification||!a)return null;const r=Gb(s.reverification);if(!r)return null;const{level:l,afterMinutes:o}=r(),[u,f]=a,m=u!==-1?o>u:null,g=f!==-1?o>f:null;switch(l){case"first_factor":return m;case"second_factor":return f!==-1?g:m;case"multi_factor":return f===-1?m:m&&g}},Yb=s=>a=>{if(!s.userId)return!1;const r=Ib(a,s),l=Hb(a,s),o=Vb(a,s);return[r||l,o].some(u=>u===null)?[r||l,o].some(u=>u===!0):[r||l,o].every(u=>u===!0)},Fb=({authObject:{sessionId:s,sessionStatus:a,userId:r,actor:l,orgId:o,orgRole:u,orgSlug:f,signOut:m,getToken:g,has:v,sessionClaims:y},options:{treatPendingAsSignedOut:S=!0}})=>{if(s===void 0&&r===void 0)return{isLoaded:!1,isSignedIn:void 0,sessionId:s,sessionClaims:void 0,userId:r,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:m,getToken:g};if(s===null&&r===null)return{isLoaded:!0,isSignedIn:!1,sessionId:s,userId:r,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:m,getToken:g};if(S&&a==="pending")return{isLoaded:!0,isSignedIn:!1,sessionId:null,userId:null,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:m,getToken:g};if(s&&y&&r&&o&&u)return{isLoaded:!0,isSignedIn:!0,sessionId:s,sessionClaims:y,userId:r,actor:l||null,orgId:o,orgRole:u,orgSlug:f||null,has:v,signOut:m,getToken:g};if(s&&y&&r&&!o)return{isLoaded:!0,isSignedIn:!0,sessionId:s,sessionClaims:y,userId:r,actor:l||null,orgId:null,orgRole:null,orgSlug:null,has:v,signOut:m,getToken:g}},Fg=s=>typeof atob!="undefined"&&typeof atob=="function"?atob(s):typeof global!="undefined"&&global.Buffer?new global.Buffer(s,"base64").toString():s,Kb=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],Kg="pk_live_",Xb="pk_test_";function Vp(s,a={}){if(s=s||"",!s||!ud(s)){if(a.fatal&&!s)throw new Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(a.fatal&&!ud(s))throw new Error("Publishable key not valid.");return null}const r=s.startsWith(Kg)?"production":"development";let l=Fg(s.split("_")[2]);return l=l.slice(0,-1),a.proxyUrl?l=a.proxyUrl:r!=="development"&&a.domain&&a.isSatellite&&(l=`clerk.${a.domain}`),{instanceType:r,frontendApi:l}}function ud(s=""){try{const a=s.startsWith(Kg)||s.startsWith(Xb),r=Fg(s.split("_")[2]||"").endsWith("$");return a&&r}catch(a){return!1}}function Qb(){const s=new Map;return{isDevOrStagingUrl:a=>{if(!a)return!1;const r=typeof a=="string"?a:a.hostname;let l=s.get(r);return l===void 0&&(l=Kb.some(o=>r.endsWith(o)),s.set(r,l)),l}}}var Zb="METHOD_CALLED";function Jb(s,a){return{event:Zb,payload:se({method:s},a)}}var Hu={exports:{}},Iu={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yp;function Wb(){if(Yp)return Iu;Yp=1;var s=fo();function a(S,x){return S===x&&(S!==0||1/S===1/x)||S!==S&&x!==x}var r=typeof Object.is=="function"?Object.is:a,l=s.useState,o=s.useEffect,u=s.useLayoutEffect,f=s.useDebugValue;function m(S,x){var C=x(),U=l({inst:{value:C,getSnapshot:x}}),Y=U[0].inst,V=U[1];return u(function(){Y.value=C,Y.getSnapshot=x,g(Y)&&V({inst:Y})},[S,C,x]),o(function(){return g(Y)&&V({inst:Y}),S(function(){g(Y)&&V({inst:Y})})},[S]),f(C),C}function g(S){var x=S.getSnapshot;S=S.value;try{var C=x();return!r(S,C)}catch(U){return!0}}function v(S,x){return x()}var y=typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"?v:m;return Iu.useSyncExternalStore=s.useSyncExternalStore!==void 0?s.useSyncExternalStore:y,Iu}var Fp;function e_(){return Fp||(Fp=1,Hu.exports=Wb()),Hu.exports}var Xg=e_();const Qg=0,Zg=1,Jg=2,Kp=3;var Xp=Object.prototype.hasOwnProperty;function dd(s,a){var r,l;if(s===a)return!0;if(s&&a&&(r=s.constructor)===a.constructor){if(r===Date)return s.getTime()===a.getTime();if(r===RegExp)return s.toString()===a.toString();if(r===Array){if((l=s.length)===a.length)for(;l--&&dd(s[l],a[l]););return l===-1}if(!r||typeof s=="object"){l=0;for(r in s)if(Xp.call(s,r)&&++l&&!Xp.call(a,r)||!(r in a)||!dd(s[r],a[r]))return!1;return Object.keys(a).length===l}}return s!==s&&a!==a}const xn=new WeakMap,Ma=()=>{},yt=Ma(),oo=Object,Se=s=>s===yt,pn=s=>typeof s=="function",Wn=(s,a)=>se(se({},s),a),Wg=s=>pn(s.then),$u={},$l={},Ud="undefined",Sr=typeof window!=Ud,fd=typeof document!=Ud,t_=Sr&&"Deno"in window,n_=()=>Sr&&typeof window.requestAnimationFrame!=Ud,Aa=(s,a)=>{const r=xn.get(s);return[()=>!Se(a)&&s.get(a)||$u,l=>{if(!Se(a)){const o=s.get(a);a in $l||($l[a]=o),r[5](a,Wn(o,l),o||$u)}},r[6],()=>!Se(a)&&a in $l?$l[a]:!Se(a)&&s.get(a)||$u]};let hd=!0;const a_=()=>hd,[md,pd]=Sr&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[Ma,Ma],i_=()=>{const s=fd&&document.visibilityState;return Se(s)||s!=="hidden"},s_=s=>(fd&&document.addEventListener("visibilitychange",s),md("focus",s),()=>{fd&&document.removeEventListener("visibilitychange",s),pd("focus",s)}),r_=s=>{const a=()=>{hd=!0,s()},r=()=>{hd=!1};return md("online",a),md("offline",r),()=>{pd("online",a),pd("offline",r)}},l_={isOnline:a_,isVisible:i_},o_={initFocus:s_,initReconnect:r_},Qp=!P.useId,br=!Sr||t_,c_=s=>n_()?window.requestAnimationFrame(s):setTimeout(s,1),fr=br?ee.useEffect:ee.useLayoutEffect,Gu=typeof navigator!="undefined"&&navigator.connection,Zp=!br&&Gu&&(["slow-2g","2g"].includes(Gu.effectiveType)||Gu.saveData),Gl=new WeakMap,Vu=(s,a)=>oo.prototype.toString.call(s)===`[object ${a}]`;let u_=0;const gd=s=>{const a=typeof s,r=Vu(s,"Date"),l=Vu(s,"RegExp"),o=Vu(s,"Object");let u,f;if(oo(s)===s&&!r&&!l){if(u=Gl.get(s),u)return u;if(u=++u_+"~",Gl.set(s,u),Array.isArray(s)){for(u="@",f=0;f<s.length;f++)u+=gd(s[f])+",";Gl.set(s,u)}if(o){u="#";const m=oo.keys(s).sort();for(;!Se(f=m.pop());)Se(s[f])||(u+=f+":"+gd(s[f])+",");Gl.set(s,u)}}else u=r?s.toJSON():a=="symbol"?s.toString():a=="string"?JSON.stringify(s):""+s;return u},rs=s=>{if(pn(s))try{s=s()}catch(r){s=""}const a=s;return s=typeof s=="string"?s:(Array.isArray(s)?s.length:s)?gd(s):"",[s,a]};let d_=0;const vd=()=>++d_;function ev(...s){return E(this,null,function*(){const[a,r,l,o]=s,u=Wn({populateCache:!0,throwOnError:!0},typeof o=="boolean"?{revalidate:o}:o||{});let f=u.populateCache;const m=u.rollbackOnError;let g=u.optimisticData;const v=x=>typeof m=="function"?m(x):m!==!1,y=u.throwOnError;if(pn(r)){const x=r,C=[],U=a.keys();for(const Y of U)!/^\$(inf|sub)\$/.test(Y)&&x(a.get(Y)._k)&&C.push(Y);return Promise.all(C.map(S))}return S(r);function S(x){return E(this,null,function*(){const[C]=rs(x);if(!C)return;const[U,Y]=Aa(a,C),[V,F,J,q]=xn.get(a),te=()=>{const Le=V[C];return(pn(u.revalidate)?u.revalidate(U().data,x):u.revalidate!==!1)&&(delete J[C],delete q[C],Le&&Le[0])?Le[0](Jg).then(()=>U().data):U().data};if(s.length<3)return te();let K=l,W;const le=vd();F[C]=[le,0];const ne=!Se(g),ye=U(),ge=ye.data,ue=ye._c,Ae=Se(ue)?ge:ue;if(ne&&(g=pn(g)?g(Ae,ge):g,Y({data:g,_c:Ae})),pn(K))try{K=K(Ae)}catch(Le){W=Le}if(K&&Wg(K))if(K=yield K.catch(Le=>{W=Le}),le!==F[C][0]){if(W)throw W;return K}else W&&ne&&v(W)&&(f=!0,Y({data:Ae,_c:yt}));if(f&&!W)if(pn(f)){const Le=f(K,Ae);Y({data:Le,error:yt,_c:yt})}else Y({data:K,error:yt,_c:yt});if(F[C][1]=vd(),Promise.resolve(te()).then(()=>{Y({_c:yt})}),W){if(y)throw W;return}return K})}})}const Jp=(s,a)=>{for(const r in s)s[r][0]&&s[r][0](a)},tv=(s,a)=>{if(!xn.has(s)){const r=Wn(o_,a),l=Object.create(null),o=ev.bind(yt,s);let u=Ma;const f=Object.create(null),m=(y,S)=>{const x=f[y]||[];return f[y]=x,x.push(S),()=>x.splice(x.indexOf(S),1)},g=(y,S,x)=>{s.set(y,S);const C=f[y];if(C)for(const U of C)U(S,x)},v=()=>{if(!xn.has(s)&&(xn.set(s,[l,Object.create(null),Object.create(null),Object.create(null),o,g,m]),!br)){const y=r.initFocus(setTimeout.bind(yt,Jp.bind(yt,l,Qg))),S=r.initReconnect(setTimeout.bind(yt,Jp.bind(yt,l,Zg)));u=()=>{y&&y(),S&&S(),xn.delete(s)}}};return v(),[s,o,v,u]}return[s,xn.get(s)[4]]},f_=(s,a,r,l,o)=>{const u=r.errorRetryCount,f=o.retryCount,m=~~((Math.random()+.5)*(1<<(f<8?f:8)))*r.errorRetryInterval;!Se(u)&&f>u||setTimeout(l,m,o)},h_=dd,[wr,nv]=tv(new Map),av=Wn({onLoadingSlow:Ma,onSuccess:Ma,onError:Ma,onErrorRetry:f_,onDiscarded:Ma,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:Zp?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:Zp?5e3:3e3,compare:h_,isPaused:()=>!1,cache:wr,mutate:nv,fallback:{}},l_),iv=(s,a)=>{const r=Wn(s,a);if(a){const{use:l,fallback:o}=s,{use:u,fallback:f}=a;l&&u&&(r.use=l.concat(u)),o&&f&&(r.fallback=Wn(o,f))}return r},yd=ee.createContext({}),m_=s=>{const{value:a}=s,r=ee.useContext(yd),l=pn(a),o=ee.useMemo(()=>l?a(r):a,[l,r,a]),u=ee.useMemo(()=>l?o:iv(r,o),[l,r,o]),f=o&&o.provider,m=ee.useRef(yt);f&&!m.current&&(m.current=tv(f(u.cache||wr),o));const g=m.current;return g&&(u.cache=g[0],u.mutate=g[1]),fr(()=>{if(g)return g[2]&&g[2](),g[3]},[]),ee.createElement(yd.Provider,Wn(s,{value:u}))},sv="$inf$",rv=Sr&&window.__SWR_DEVTOOLS_USE__,p_=rv?window.__SWR_DEVTOOLS_USE__:[],g_=()=>{rv&&(window.__SWR_DEVTOOLS_REACT__=P)},lv=s=>pn(s[1])?[s[0],s[1],s[2]||{}]:[s[0],null,(s[1]===null?s[2]:s[1])||{}],ov=()=>Wn(av,ee.useContext(yd)),v_=(s,a)=>{const[r,l]=rs(s),[,,,o]=xn.get(wr);if(o[r])return o[r];const u=a(l);return o[r]=u,u},y_=s=>(a,r,l)=>s(a,r&&((...u)=>{const[f]=rs(a),[,,,m]=xn.get(wr);if(f.startsWith(sv))return r(...u);const g=m[f];return Se(g)?r(...u):(delete m[f],g)}),l),b_=p_.concat(y_),__=s=>function(...r){const l=ov(),[o,u,f]=lv(r),m=iv(l,f);let g=s;const{use:v}=m,y=(v||[]).concat(b_);for(let S=y.length;S--;)g=y[S](g);return g(o,u||m.fetcher||null,m)},S_=(s,a,r)=>{const l=a[s]||(a[s]=[]);return l.push(r),()=>{const o=l.indexOf(r);o>=0&&(l[o]=l[l.length-1],l.pop())}},w_=(s,a)=>(...r)=>{const[l,o,u]=lv(r),f=(u.use||[]).concat(a);return s(l,o,Ve(se({},u),{use:f}))};g_();const j_=()=>{},E_=j_(),bd=Object,Wp=s=>s===E_,x_=s=>typeof s=="function",Vl=new WeakMap,Yu=(s,a)=>bd.prototype.toString.call(s)===`[object ${a}]`;let k_=0;const _d=s=>{const a=typeof s,r=Yu(s,"Date"),l=Yu(s,"RegExp"),o=Yu(s,"Object");let u,f;if(bd(s)===s&&!r&&!l){if(u=Vl.get(s),u)return u;if(u=++k_+"~",Vl.set(s,u),Array.isArray(s)){for(u="@",f=0;f<s.length;f++)u+=_d(s[f])+",";Vl.set(s,u)}if(o){u="#";const m=bd.keys(s).sort();for(;!Wp(f=m.pop());)Wp(s[f])||(u+=f+":"+_d(s[f])+",");Vl.set(s,u)}}else u=r?s.toJSON():a=="symbol"?s.toString():a=="string"?JSON.stringify(s):""+s;return u},T_=s=>{if(x_(s))try{s=s()}catch(r){s=""}const a=s;return s=typeof s=="string"?s:(Array.isArray(s)?s.length:s)?_d(s):"",[s,a]},C_=s=>T_(s)[0],Fu=P.use||(s=>{switch(s.status){case"pending":throw s;case"fulfilled":return s.value;case"rejected":throw s.reason;default:throw s.status="pending",s.then(a=>{s.status="fulfilled",s.value=a},a=>{s.status="rejected",s.reason=a}),s}}),Ku={dedupe:!0},A_=(s,a,r)=>{const{cache:l,compare:o,suspense:u,fallbackData:f,revalidateOnMount:m,revalidateIfStale:g,refreshInterval:v,refreshWhenHidden:y,refreshWhenOffline:S,keepPreviousData:x}=r,[C,U,Y,V]=xn.get(l),[F,J]=rs(s),q=ee.useRef(!1),te=ee.useRef(!1),K=ee.useRef(F),W=ee.useRef(a),le=ee.useRef(r),ne=()=>le.current,ye=()=>ne().isVisible()&&ne().isOnline(),[ge,ue,Ae,Le]=Aa(l,F),Qe=ee.useRef({}).current,Me=Se(f)?Se(r.fallback)?yt:r.fallback[F]:f,z=(Ne,Oe)=>{for(const rt in Qe){const ze=rt;if(ze==="data"){if(!o(Ne[ze],Oe[ze])&&(!Se(Ne[ze])||!o(ve,Oe[ze])))return!1}else if(Oe[ze]!==Ne[ze])return!1}return!0},G=ee.useMemo(()=>{const Ne=!F||!a?!1:Se(m)?ne().isPaused()||u?!1:g!==!1:m,Oe=ut=>{const Rt=Wn(ut);return delete Rt._k,Ne?se({isValidating:!0,isLoading:!0},Rt):Rt},rt=ge(),ze=Le(),Ze=Oe(rt),Xt=rt===ze?Ze:Oe(ze);let nt=Ze;return[()=>{const ut=Oe(ge());return z(ut,nt)?(nt.data=ut.data,nt.isLoading=ut.isLoading,nt.isValidating=ut.isValidating,nt.error=ut.error,nt):(nt=ut,ut)},()=>Xt]},[l,F]),ae=Xg.useSyncExternalStore(ee.useCallback(Ne=>Ae(F,(Oe,rt)=>{z(rt,Oe)||Ne()}),[l,F]),G[0],G[1]),xe=!q.current,j=C[F]&&C[F].length>0,I=ae.data,X=Se(I)?Me&&Wg(Me)?Fu(Me):Me:I,Z=ae.error,re=ee.useRef(X),ve=x?Se(I)?Se(re.current)?X:re.current:I:X,de=j&&!Se(Z)?!1:xe&&!Se(m)?m:ne().isPaused()?!1:u?Se(X)?!1:g:Se(X)||g,oe=!!(F&&a&&xe&&de),Pe=Se(ae.isValidating)?oe:ae.isValidating,Bt=Se(ae.isLoading)?oe:ae.isLoading,an=ee.useCallback(Ne=>E(null,null,function*(){const Oe=W.current;if(!F||!Oe||te.current||ne().isPaused())return!1;let rt,ze,Ze=!0;const Xt=Ne||{},nt=!Y[F]||!Xt.dedupe,ut=()=>Qp?!te.current&&F===K.current&&q.current:F===K.current,Rt={isValidating:!1,isLoading:!1},Da=()=>{ue(Rt)},ta=()=>{const Ct=Y[F];Ct&&Ct[1]===ze&&delete Y[F]},ci={isValidating:!0};Se(ge().data)&&(ci.isLoading=!0);try{if(nt&&(ue(ci),r.loadingTimeout&&Se(ge().data)&&setTimeout(()=>{Ze&&ut()&&ne().onLoadingSlow(F,r)},r.loadingTimeout),Y[F]=[Oe(J),vd()]),[rt,ze]=Y[F],rt=yield rt,nt&&setTimeout(ta,r.dedupingInterval),!Y[F]||Y[F][1]!==ze)return nt&&ut()&&ne().onDiscarded(F),!1;Rt.error=yt;const Ct=U[F];if(!Se(Ct)&&(ze<=Ct[0]||ze<=Ct[1]||Ct[1]===0))return Da(),nt&&ut()&&ne().onDiscarded(F),!1;const bt=ge().data;Rt.data=o(bt,rt)?bt:rt,nt&&ut()&&ne().onSuccess(rt,F,r)}catch(Ct){ta();const bt=ne(),{shouldRetryOnError:ht}=bt;bt.isPaused()||(Rt.error=Ct,nt&&ut()&&(bt.onError(Ct,F,bt),(ht===!0||pn(ht)&&ht(Ct))&&(!ne().revalidateOnFocus||!ne().revalidateOnReconnect||ye())&&bt.onErrorRetry(Ct,F,bt,sn=>{const mt=C[F];mt&&mt[0]&&mt[0](Kp,sn)},{retryCount:(Xt.retryCount||0)+1,dedupe:!0})))}return Ze=!1,Da(),!0}),[F,l]),vn=ee.useCallback((...Ne)=>ev(l,K.current,...Ne),[]);if(fr(()=>{W.current=a,le.current=r,Se(I)||(re.current=I)}),fr(()=>{if(!F)return;const Ne=an.bind(yt,Ku);let Oe=0;ne().revalidateOnFocus&&(Oe=Date.now()+ne().focusThrottleInterval);const ze=S_(F,C,(Ze,Xt={})=>{if(Ze==Qg){const nt=Date.now();ne().revalidateOnFocus&&nt>Oe&&ye()&&(Oe=nt+ne().focusThrottleInterval,Ne())}else if(Ze==Zg)ne().revalidateOnReconnect&&ye()&&Ne();else{if(Ze==Jg)return an();if(Ze==Kp)return an(Xt)}});return te.current=!1,K.current=F,q.current=!0,ue({_k:J}),de&&(Se(X)||br?Ne():c_(Ne)),()=>{te.current=!0,ze()}},[F]),fr(()=>{let Ne;function Oe(){const ze=pn(v)?v(ge().data):v;ze&&Ne!==-1&&(Ne=setTimeout(rt,ze))}function rt(){!ge().error&&(y||ne().isVisible())&&(S||ne().isOnline())?an(Ku).then(Oe):Oe()}return Oe(),()=>{Ne&&(clearTimeout(Ne),Ne=-1)}},[v,y,S,F]),ee.useDebugValue(ve),u&&Se(X)&&F){if(!Qp&&br)throw new Error("Fallback data is required when using Suspense in SSR.");W.current=a,le.current=r,te.current=!1;const Ne=V[F];if(!Se(Ne)){const Oe=vn(Ne);Fu(Oe)}if(Se(Z)){const Oe=an(Ku);Se(ve)||(Oe.status="fulfilled",Oe.value=!0),Fu(Oe)}else throw Z}return{mutate:vn,get data(){return Qe.data=!0,ve},get error(){return Qe.error=!0,Z},get isValidating(){return Qe.isValidating=!0,Pe},get isLoading(){return Qe.isLoading=!0,Bt}}},O_=oo.defineProperty(m_,"defaultValue",{value:av}),Dd=__(A_),N_=Object.freeze(Object.defineProperty({__proto__:null,SWRConfig:O_,default:Dd,mutate:nv,preload:v_,unstable_serialize:C_,useSWRConfig:ov},Symbol.toStringTag,{value:"Module"})),R_=()=>{},M_=R_(),Sd=Object,eg=s=>s===M_,U_=s=>typeof s=="function",Yl=new WeakMap,Xu=(s,a)=>Sd.prototype.toString.call(s)===`[object ${a}]`;let D_=0;const wd=s=>{const a=typeof s,r=Xu(s,"Date"),l=Xu(s,"RegExp"),o=Xu(s,"Object");let u,f;if(Sd(s)===s&&!r&&!l){if(u=Yl.get(s),u)return u;if(u=++D_+"~",Yl.set(s,u),Array.isArray(s)){for(u="@",f=0;f<s.length;f++)u+=wd(s[f])+",";Yl.set(s,u)}if(o){u="#";const m=Sd.keys(s).sort();for(;!eg(f=m.pop());)eg(s[f])||(u+=f+":"+wd(s[f])+",");Yl.set(s,u)}}else u=r?s.toJSON():a=="symbol"?s.toString():a=="string"?JSON.stringify(s):""+s;return u},z_=s=>{if(U_(s))try{s=s()}catch(r){s=""}const a=s;return s=typeof s=="string"?s:(Array.isArray(s)?s.length:s)?wd(s):"",[s,a]},L_=s=>z_(s?s(0,null):null)[0],Qu=Promise.resolve(),P_=s=>(a,r,l)=>{const o=ee.useRef(!1),{cache:u,initialSize:f=1,revalidateAll:m=!1,persistSize:g=!1,revalidateFirstPage:v=!0,revalidateOnMount:y=!1,parallel:S=!1}=l,[,,,x]=xn.get(wr);let C;try{C=L_(a),C&&(C=sv+C)}catch(ne){}const[U,Y,V]=Aa(u,C),F=ee.useCallback(()=>Se(U()._l)?f:U()._l,[u,C,f]);Xg.useSyncExternalStore(ee.useCallback(ne=>C?V(C,()=>{ne()}):()=>{},[u,C]),F,F);const J=ee.useCallback(()=>{const ne=U()._l;return Se(ne)?f:ne},[C,f]),q=ee.useRef(J());fr(()=>{if(!o.current){o.current=!0;return}C&&Y({_l:g?q.current:J()})},[C,u]);const te=y&&!o.current,K=s(C,ne=>E(null,null,function*(){const ye=U()._i,ge=U()._r;Y({_r:yt});const ue=[],Ae=J(),[Le]=Aa(u,ne),Qe=Le().data,Me=[];let z=null;for(let G=0;G<Ae;++G){const[ae,xe]=rs(a(G,S?null:z));if(!ae)break;const[j,I]=Aa(u,ae);let X=j().data;const Z=m||ye||Se(X)||v&&!G&&!Se(Qe)||te||Qe&&!Se(Qe[G])&&!l.compare(Qe[G],X);if(r&&(typeof ge=="function"?ge(X,xe):Z)){const re=()=>E(null,null,function*(){if(!(ae in x))X=yield r(xe);else{const de=x[ae];delete x[ae],X=yield de}I({data:X,_k:xe}),ue[G]=X});S?Me.push(re):yield re()}else ue[G]=X;S||(z=X)}return S&&(yield Promise.all(Me.map(G=>G()))),Y({_i:yt}),ue}),l),W=ee.useCallback(function(ne,ye){const ge=typeof ye=="boolean"?{revalidate:ye}:ye||{},ue=ge.revalidate!==!1;return C?(ue&&(Se(ne)?Y({_i:!0,_r:ge.revalidate}):Y({_i:!1,_r:ge.revalidate})),arguments.length?K.mutate(ne,Ve(se({},ge),{revalidate:ue})):K.mutate()):Qu},[C,u]),le=ee.useCallback(ne=>{if(!C)return Qu;const[,ye]=Aa(u,C);let ge;if(pn(ne)?ge=ne(J()):typeof ne=="number"&&(ge=ne),typeof ge!="number")return Qu;ye({_l:ge}),q.current=ge;const ue=[],[Ae]=Aa(u,C);let Le=null;for(let Qe=0;Qe<ge;++Qe){const[Me]=rs(a(Qe,Le)),[z]=Aa(u,Me),G=Me?z().data:yt;if(Se(G))return W(Ae().data);ue.push(G),Le=G}return W(ue)},[C,u,W,J]);return{size:J(),setSize:le,mutate:W,get data(){return K.data},get error(){return K.error},get isValidating(){return K.isValidating},get isLoading(){return K.isLoading}}},B_=w_(Dd,P_);var tg=Object.prototype.hasOwnProperty;function ng(s,a,r){for(r of s.keys())if(hr(r,a))return r}function hr(s,a){var r,l,o;if(s===a)return!0;if(s&&a&&(r=s.constructor)===a.constructor){if(r===Date)return s.getTime()===a.getTime();if(r===RegExp)return s.toString()===a.toString();if(r===Array){if((l=s.length)===a.length)for(;l--&&hr(s[l],a[l]););return l===-1}if(r===Set){if(s.size!==a.size)return!1;for(l of s)if(o=l,o&&typeof o=="object"&&(o=ng(a,o),!o)||!a.has(o))return!1;return!0}if(r===Map){if(s.size!==a.size)return!1;for(l of s)if(o=l[0],o&&typeof o=="object"&&(o=ng(a,o),!o)||!hr(l[1],a.get(o)))return!1;return!0}if(r===ArrayBuffer)s=new Uint8Array(s),a=new Uint8Array(a);else if(r===DataView){if((l=s.byteLength)===a.byteLength)for(;l--&&s.getInt8(l)===a.getInt8(l););return l===-1}if(ArrayBuffer.isView(s)){if((l=s.byteLength)===a.byteLength)for(;l--&&s[l]===a[l];);return l===-1}if(!r||typeof s=="object"){l=0;for(r in s)if(tg.call(s,r)&&++l&&!tg.call(a,r)||!(r in a)||!hr(s[r],a[r]))return!1;return Object.keys(a).length===l}}return s!==s&&a!==a}function q_(s,a){if(!s)throw typeof a=="string"?new Error(a):new Error(`${a.displayName} not found`)}var os=(s,a)=>{const{assertCtxFn:r=q_}={},l=P.createContext(void 0);return l.displayName=s,[l,()=>{const f=P.useContext(l);return r(f,`${s} not found`),f.value},()=>{const f=P.useContext(l);return f?f.value:{}}]},zd={};Rb(zd,{useSWR:()=>Dd,useSWRInfinite:()=>B_});Ub(zd,N_);var[cv,H_]=os("ClerkInstanceContext"),[I_,b2]=os("UserContext"),[$_,_2]=os("ClientContext"),[G_,S2]=os("SessionContext");P.createContext({});var[V_,w2]=os("OrganizationContext"),Y_=({children:s,organization:a,swrConfig:r})=>P.createElement(zd.SWRConfig,{value:r},P.createElement(V_.Provider,{value:{value:{organization:a}}},s));function F_(s){if(!P.useContext(cv)){if(typeof s=="function"){s();return}throw new Error(`${s} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}typeof window!="undefined"?P.useLayoutEffect:P.useEffect;var ag=hr,K_=()=>{try{return!1}catch(s){}return!1},X_=()=>{try{return!1}catch(s){}return!1},Q_=()=>{try{return!0}catch(s){}return!1},ig=new Set,Ld=(s,a,r)=>{const l=X_()||Q_(),o=s;ig.has(o)||l||(ig.add(o),console.warn(`Clerk - DEPRECATION WARNING: "${s}" is deprecated and will be removed in the next major release.
${a}`))},Mn=Vg({packageName:"@clerk/clerk-react"});function Z_(s){Mn.setMessages(s).setPackageName(s)}var[J_,W_]=os("AuthContext"),e1=cv,uv=H_,t1="You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.",n1=s=>`You've passed multiple children components to <${s}/>. You can only pass a single child component or text.`,a1="Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support",Zu="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",i1="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",s1="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",r1="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",l1="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",o1=s=>`<${s} /> can only accept <${s}.Page /> and <${s}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,c1=s=>`Missing props. <${s}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,u1=s=>`Missing props. <${s}.Link /> component requires the following props: url, label and labelIcon.`,d1="<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",f1="<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",h1="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",m1="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",p1="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",g1="Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.",v1="Missing props. <UserButton.Action /> component requires the following props: label.",dv=s=>{F_(()=>{Mn.throwMissingClerkProviderError({source:s})})},fv=s=>new Promise(a=>{const r=l=>{["ready","degraded"].includes(l)&&(a(),s.off("status",r))};s.on("status",r,{notify:!0})}),y1=s=>a=>E(null,null,function*(){return yield fv(s),s.session?s.session.getToken(a):null}),b1=s=>(...a)=>E(null,null,function*(){return yield fv(s),s.signOut(...a)}),hv=(s={})=>{var a,r;dv("useAuth");const S=s!=null?s:{},{treatPendingAsSignedOut:l}=S,u=Fe(S,["treatPendingAsSignedOut"]);let m=W_();m.sessionId===void 0&&m.userId===void 0&&(m=u!=null?u:{});const g=uv(),v=ee.useCallback(y1(g),[g]),y=ee.useCallback(b1(g),[g]);return(a=g.telemetry)==null||a.record(Jb("useAuth",{treatPendingAsSignedOut:l})),_1(Ve(se({},m),{getToken:v,signOut:y}),{treatPendingAsSignedOut:l!=null?l:(r=g.__internal_getOption)==null?void 0:r.call(g,"treatPendingAsSignedOut")})};function _1(s,{treatPendingAsSignedOut:a=!0}={}){const{userId:r,orgId:l,orgRole:o,has:u,signOut:f,getToken:m,orgPermissions:g,factorVerificationAge:v,sessionClaims:y}=s!=null?s:{},S=ee.useCallback(C=>u?u(C):Yb({userId:r,orgId:l,orgRole:o,orgPermissions:g,factorVerificationAge:v,features:(y==null?void 0:y.fea)||"",plans:(y==null?void 0:y.pla)||""})(C),[u,r,l,o,g,v]),x=Fb({authObject:Ve(se({},s),{getToken:m,signOut:f,has:S}),options:{treatPendingAsSignedOut:a}});return x||Mn.throw(a1)}var st=(s,a)=>{const l=(typeof a=="string"?a:a==null?void 0:a.component)||s.displayName||s.name||"Component";s.displayName=l;const o=typeof a=="string"?void 0:a,u=f=>{dv(l||"withClerk");const m=uv();return!m.loaded&&!(o!=null&&o.renderWhileLoading)?null:P.createElement(s,Ve(se({},f),{component:l,clerk:m}))};return u.displayName=`withClerk(${l})`,u};st(r=>{var l=r,{clerk:s}=l,a=Fe(l,["clerk"]);const{client:o,session:u}=s,f=o.signedInSessions?o.signedInSessions.length>0:o.activeSessions&&o.activeSessions.length>0;return P.useEffect(()=>{u===null&&f?s.redirectToAfterSignOut():s.redirectToSignIn(a)},[]),null},"RedirectToSignIn");st(r=>{var l=r,{clerk:s}=l,a=Fe(l,["clerk"]);return P.useEffect(()=>{s.redirectToSignUp(a)},[]),null},"RedirectToSignUp");st(({clerk:s})=>(P.useEffect(()=>{Ld("RedirectToUserProfile","Use the `redirectToUserProfile()` method instead."),s.redirectToUserProfile()},[]),null),"RedirectToUserProfile");st(({clerk:s})=>(P.useEffect(()=>{Ld("RedirectToOrganizationProfile","Use the `redirectToOrganizationProfile()` method instead."),s.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile");st(({clerk:s})=>(P.useEffect(()=>{Ld("RedirectToCreateOrganization","Use the `redirectToCreateOrganization()` method instead."),s.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization");st(r=>{var l=r,{clerk:s}=l,a=Fe(l,["clerk"]);return P.useEffect(()=>{s.handleRedirectCallback(a)},[]),null},"AuthenticateWithRedirectCallback");var mv=s=>{throw TypeError(s)},Pd=(s,a,r)=>a.has(s)||mv("Cannot "+r),tt=(s,a,r)=>(Pd(s,a,"read from private field"),r?r.call(s):a.get(s)),ii=(s,a,r)=>a.has(s)?mv("Cannot add the same private member more than once"):a instanceof WeakSet?a.add(s):a.set(s,r),Fi=(s,a,r,l)=>(Pd(s,a,"write to private field"),a.set(s,r),r),sg=(s,a,r)=>(Pd(s,a,"access private method"),r),S1=(s,a="5.69.2")=>{if(s)return s;const r=w1(a);return r?r==="snapshot"?"5.69.2":r:j1(a)},w1=s=>{var a;return(a=s.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/))==null?void 0:a[1]},j1=s=>s.trim().replace(/^v/,"").split(".")[0];function E1(s){return s?x1(s)||pv(s):!0}function x1(s){return/^http(s)?:\/\//.test(s||"")}function pv(s){return s.startsWith("/")}function k1(s){return s?pv(s)?new URL(s,window.location.origin).toString():s:""}function T1(s){if(!s)return"";let a;if(s.match(/^(clerk\.)+\w*$/))a=/(clerk\.)*(?=clerk\.)/;else{if(s.match(/\.clerk.accounts/))return s;a=/^(clerk\.)*/gi}return`clerk.${s.replace(a,"")}`}var C1={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(s,a)=>a<5,retryImmediately:!1,jitter:!0},A1=100,gv=s=>E(null,null,function*(){return new Promise(a=>setTimeout(a,s))}),vv=(s,a)=>a?s*(1+Math.random()):s,O1=s=>{let a=0;const r=()=>{const l=s.initialDelay,o=s.factor;let u=l*Math.pow(o,a);return u=vv(u,s.jitter),Math.min(s.maxDelayBetweenRetries||u,u)};return()=>E(null,null,function*(){yield gv(r()),a++})},N1=(r,...l)=>E(null,[r,...l],function*(s,a={}){let o=0;const{shouldRetry:u,initialDelay:f,maxDelayBetweenRetries:m,factor:g,retryImmediately:v,jitter:y}=se(se({},C1),a),S=O1({initialDelay:f,maxDelayBetweenRetries:m,factor:g,jitter:y});for(;;)try{return yield s()}catch(x){if(o++,!u(x,o))throw x;v&&o===1?yield gv(vv(A1,y)):yield S()}}),R1="loadScript cannot be called when document does not exist",M1="loadScript cannot be called without a src";function U1(s="",a){return E(this,null,function*(){const{async:r,defer:l,beforeLoad:o,crossOrigin:u,nonce:f}=a||{};return N1(()=>new Promise((g,v)=>{s||v(new Error(M1)),(!document||!document.body)&&v(R1);const y=document.createElement("script");u&&y.setAttribute("crossorigin",u),y.async=r||!1,y.defer=l||!1,y.addEventListener("load",()=>{y.remove(),g(y)}),y.addEventListener("error",()=>{y.remove(),v()}),y.src=s,y.nonce=f,o==null||o(y),document.body.appendChild(y)}),{shouldRetry:(g,v)=>v<=5})})}var rg="Clerk: Failed to load Clerk",{isDevOrStagingUrl:D1}=Qb(),yv=Vg({packageName:"@clerk/shared"});function z1(s){yv.setPackageName({packageName:s})}var L1=s=>E(null,null,function*(){const a=document.querySelector("script[data-clerk-js-script]");if(a)return new Promise((r,l)=>{a.addEventListener("load",()=>{r(a)}),a.addEventListener("error",()=>{l(rg)})});if(!(s!=null&&s.publishableKey)){yv.throwMissingPublishableKeyError();return}return U1(P1(s),{async:!0,crossOrigin:"anonymous",nonce:s.nonce,beforeLoad:q1(s)}).catch(()=>{throw new Error(rg)})}),P1=s=>{var y,S;const{clerkJSUrl:a,clerkJSVariant:r,clerkJSVersion:l,proxyUrl:o,domain:u,publishableKey:f}=s;if(a)return a;let m="";o&&E1(o)?m=k1(o).replace(/http(s)?:\/\//,""):u&&!D1(((y=Vp(f))==null?void 0:y.frontendApi)||"")?m=T1(u):m=((S=Vp(f))==null?void 0:S.frontendApi)||"";const g=r?`${r.replace(/\.+$/,"")}.`:"",v=S1(l);return`https://${m}/npm/@clerk/clerk-js@${v}/dist/clerk.${g}browser.js`},B1=s=>{const a={};return s.publishableKey&&(a["data-clerk-publishable-key"]=s.publishableKey),s.proxyUrl&&(a["data-clerk-proxy-url"]=s.proxyUrl),s.domain&&(a["data-clerk-domain"]=s.domain),s.nonce&&(a.nonce=s.nonce),a},q1=s=>a=>{const r=B1(s);for(const l in r)a.setAttribute(l,r[l])},Pt=s=>{K_()&&console.error(`Clerk: ${s}`)};function Ju(s,a,r){if(typeof s=="function")return s(a);if(typeof s!="undefined")return s;if(typeof r!="undefined")return r}var H1=Ig(),lg=(s,...a)=>{const r=se({},s);for(const l of a)delete r[l];return r},I1=(s,a,r)=>!s&&r?$1(r):G1(a),$1=s=>{const a=s.userId,r=s.user,l=s.sessionId,o=s.sessionStatus,u=s.sessionClaims,f=s.session,m=s.organization,g=s.orgId,v=s.orgRole,y=s.orgPermissions,S=s.orgSlug,x=s.actor,C=s.factorVerificationAge;return{userId:a,user:r,sessionId:l,session:f,sessionStatus:o,sessionClaims:u,organization:m,orgId:g,orgRole:v,orgPermissions:y,orgSlug:S,actor:x,factorVerificationAge:C}},G1=s=>{var Y,V,F,J;const a=s.user?s.user.id:s.user,r=s.user,l=s.session?s.session.id:s.session,o=s.session,u=(Y=s.session)==null?void 0:Y.status,f=s.session?(F=(V=s.session.lastActiveToken)==null?void 0:V.jwt)==null?void 0:F.claims:null,m=s.session?s.session.factorVerificationAge:null,g=o==null?void 0:o.actor,v=s.organization,y=s.organization?s.organization.id:s.organization,S=v==null?void 0:v.slug,x=v&&((J=r==null?void 0:r.organizationMemberships)==null?void 0:J.find(q=>q.organization.id===y)),C=x&&x.permissions,U=x&&x.role;return{userId:a,user:r,sessionId:l,session:o,sessionStatus:u,sessionClaims:f,organization:v,orgId:y,orgRole:U,orgSlug:S,orgPermissions:C,actor:g,factorVerificationAge:m}};function og(){return typeof window!="undefined"}var cg=(s,a,r,l,o)=>{const{notify:u}=o||{};let f=s.get(r);f||(f=[],s.set(r,f)),f.push(l),u&&a.has(r)&&l(a.get(r))},ug=(s,a,r)=>(s.get(a)||[]).map(l=>l(r)),dg=(s,a,r)=>{const l=s.get(a);l&&(r?l.splice(l.indexOf(r)>>>0,1):s.set(a,[]))},V1=()=>{const s=new Map,a=new Map,r=new Map;return{on:(...o)=>cg(s,a,...o),prioritizedOn:(...o)=>cg(r,a,...o),emit:(o,u)=>{a.set(o,u),ug(r,o,u),ug(s,o,u)},off:(...o)=>dg(s,...o),prioritizedOff:(...o)=>dg(r,...o),internal:{retrieveListeners:o=>s.get(o)||[]}}},Fl={Status:"status"},Y1=()=>V1();typeof window!="undefined"&&!window.global&&(window.global=typeof global=="undefined"?window:global);var ho=s=>a=>{try{return P.Children.only(s)}catch(r){return Mn.throw(n1(a))}},mo=(s,a)=>(s||(s=a),typeof s=="string"&&(s=P.createElement("button",null,s)),s),po=s=>(...a)=>{if(s&&typeof s=="function")return s(...a)};function F1(s){return typeof s=="function"}var Kl=new Map;function K1(s,a,r=1){P.useEffect(()=>{const l=Kl.get(s)||0;return l==r?Mn.throw(a):(Kl.set(s,l+1),()=>{Kl.set(s,(Kl.get(s)||1)-1)})},[])}function X1(s,a,r){const l=s.displayName||s.name||a||"Component",o=u=>(K1(a,r),P.createElement(s,se({},u)));return o.displayName=`withMaxAllowedInstancesGuard(${l})`,o}var mr=s=>{const a=Array(s.length).fill(null),[r,l]=ee.useState(a);return s.map((o,u)=>({id:o.id,mount:f=>l(m=>m.map((g,v)=>v===u?f:g)),unmount:()=>l(f=>f.map((m,g)=>g===u?null:m)),portal:()=>P.createElement(P.Fragment,null,r[u]?H1.createPortal(o.component,r[u]):null)}))},Lt=(s,a)=>!!s&&P.isValidElement(s)&&(s==null?void 0:s.type)===a,bv=(s,a)=>wv({children:s,reorderItemsLabels:["account","security"],LinkComponent:Er,PageComponent:jr,MenuItemsComponent:vo,componentName:"UserProfile"},a),_v=(s,a)=>wv({children:s,reorderItemsLabels:["general","members"],LinkComponent:bo,PageComponent:yo,componentName:"OrganizationProfile"},a),Sv=s=>{const a=[],r=[bo,yo,vo,jr,Er];return P.Children.forEach(s,l=>{r.some(o=>Lt(l,o))||a.push(l)}),a},wv=(s,a)=>{const{children:r,LinkComponent:l,PageComponent:o,MenuItemsComponent:u,reorderItemsLabels:f,componentName:m}=s,{allowForAnyChildren:g=!1}=a||{},v=[];P.Children.forEach(r,J=>{if(!Lt(J,o)&&!Lt(J,l)&&!Lt(J,u)){J&&!g&&Pt(o1(m));return}const{props:q}=J,{children:te,label:K,url:W,labelIcon:le}=q;if(Lt(J,o))if(fg(q,f))v.push({label:K});else if(Wu(q))v.push({label:K,labelIcon:le,children:te,url:W});else{Pt(c1(m));return}if(Lt(J,l))if(ed(q))v.push({label:K,labelIcon:le,url:W});else{Pt(u1(m));return}});const y=[],S=[],x=[];v.forEach((J,q)=>{if(Wu(J)){y.push({component:J.children,id:q}),S.push({component:J.labelIcon,id:q});return}ed(J)&&x.push({component:J.labelIcon,id:q})});const C=mr(y),U=mr(S),Y=mr(x),V=[],F=[];return v.forEach((J,q)=>{if(fg(J,f)){V.push({label:J.label});return}if(Wu(J)){const{portal:te,mount:K,unmount:W}=C.find(ge=>ge.id===q),{portal:le,mount:ne,unmount:ye}=U.find(ge=>ge.id===q);V.push({label:J.label,url:J.url,mount:K,unmount:W,mountIcon:ne,unmountIcon:ye}),F.push(te),F.push(le);return}if(ed(J)){const{portal:te,mount:K,unmount:W}=Y.find(le=>le.id===q);V.push({label:J.label,url:J.url,mountIcon:K,unmountIcon:W}),F.push(te);return}}),{customPages:V,customPagesPortals:F}},fg=(s,a)=>{const{children:r,label:l,url:o,labelIcon:u}=s;return!r&&!o&&!u&&a.some(f=>f===l)},Wu=s=>{const{children:a,label:r,url:l,labelIcon:o}=s;return!!a&&!!l&&!!o&&!!r},ed=s=>{const{children:a,label:r,url:l,labelIcon:o}=s;return!a&&!!l&&!!o&&!!r},Q1=s=>Z1({children:s,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:vo,MenuActionComponent:Ev,MenuLinkComponent:xv,UserProfileLinkComponent:Er,UserProfilePageComponent:jr}),Z1=({children:s,MenuItemsComponent:a,MenuActionComponent:r,MenuLinkComponent:l,UserProfileLinkComponent:o,UserProfilePageComponent:u,reorderItemsLabels:f})=>{const m=[],g=[],v=[];P.Children.forEach(s,U=>{if(!Lt(U,a)&&!Lt(U,o)&&!Lt(U,u)){U&&Pt(d1);return}if(Lt(U,o)||Lt(U,u))return;const{props:Y}=U;P.Children.forEach(Y.children,V=>{if(!Lt(V,r)&&!Lt(V,l)){V&&Pt(f1);return}const{props:F}=V,{label:J,labelIcon:q,href:te,onClick:K,open:W}=F;if(Lt(V,r))if(hg(F,f))m.push({label:J});else if(td(F)){const le={label:J,labelIcon:q};if(K!==void 0)m.push(Ve(se({},le),{onClick:K}));else if(W!==void 0)m.push(Ve(se({},le),{open:W.startsWith("/")?W:`/${W}`}));else{Pt("Custom menu item must have either onClick or open property");return}}else{Pt(v1);return}if(Lt(V,l))if(nd(F))m.push({label:J,labelIcon:q,href:te});else{Pt(g1);return}})});const y=[],S=[];m.forEach((U,Y)=>{td(U)&&y.push({component:U.labelIcon,id:Y}),nd(U)&&S.push({component:U.labelIcon,id:Y})});const x=mr(y),C=mr(S);return m.forEach((U,Y)=>{if(hg(U,f)&&g.push({label:U.label}),td(U)){const{portal:V,mount:F,unmount:J}=x.find(te=>te.id===Y),q={label:U.label,mountIcon:F,unmountIcon:J};"onClick"in U?q.onClick=U.onClick:"open"in U&&(q.open=U.open),g.push(q),v.push(V)}if(nd(U)){const{portal:V,mount:F,unmount:J}=C.find(q=>q.id===Y);g.push({label:U.label,href:U.href,mountIcon:F,unmountIcon:J}),v.push(V)}}),{customMenuItems:g,customMenuItemsPortals:v}},hg=(s,a)=>{const{children:r,label:l,onClick:o,labelIcon:u}=s;return!r&&!o&&!u&&a.some(f=>f===l)},td=s=>{const{label:a,labelIcon:r,onClick:l,open:o}=s;return!!r&&!!a&&(typeof l=="function"||typeof o=="string")},nd=s=>{const{label:a,href:r,labelIcon:l}=s;return!!r&&!!l&&!!a};function J1(s){const{root:a=document==null?void 0:document.body,selector:r,timeout:l=0}=s;return new Promise((o,u)=>{if(!a){u(new Error("No root element provided"));return}let f=a;if(r&&(f=a==null?void 0:a.querySelector(r)),(f==null?void 0:f.childElementCount)&&f.childElementCount>0){o();return}const g=new MutationObserver(v=>{for(const y of v)if(y.type==="childList"&&(!f&&r&&(f=a==null?void 0:a.querySelector(r)),f!=null&&f.childElementCount&&f.childElementCount>0)){g.disconnect(),o();return}});g.observe(a,{childList:!0,subtree:!0}),l>0&&setTimeout(()=>{g.disconnect(),u(new Error("Timeout waiting for element children"))},l)})}function gn(s){const a=ee.useRef(),[r,l]=ee.useState("rendering");return ee.useEffect(()=>{if(!s)throw new Error("Clerk: no component name provided, unable to detect mount.");typeof window!="undefined"&&!a.current&&(a.current=J1({selector:`[data-clerk-component="${s}"]`}).then(()=>{l("rendered")}).catch(()=>{l("error")}))},[s]),r}var Xl=s=>"mount"in s,mg=s=>"open"in s,pg=s=>s==null?void 0:s.map(o=>{var u=o,{mountIcon:a,unmountIcon:r}=u,l=Fe(u,["mountIcon","unmountIcon"]);return l}),Kt=class extends P.PureComponent{constructor(){super(...arguments),this.rootRef=P.createRef()}componentDidUpdate(s){var a,r,l,o;if(!Xl(s)||!Xl(this.props))return;const u=lg(s.props,"customPages","customMenuItems","children"),f=lg(this.props.props,"customPages","customMenuItems","children"),m=((a=u.customPages)==null?void 0:a.length)!==((r=f.customPages)==null?void 0:r.length),g=((l=u.customMenuItems)==null?void 0:l.length)!==((o=f.customMenuItems)==null?void 0:o.length),v=pg(s.props.customMenuItems),y=pg(this.props.props.customMenuItems);(!ag(u,f)||!ag(v,y)||m||g)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(Xl(this.props)&&this.props.mount(this.rootRef.current,this.props.props),mg(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(Xl(this.props)&&this.props.unmount(this.rootRef.current),mg(this.props)&&this.props.close())}render(){const{hideRootHtmlElement:s=!1}=this.props,a=se(se({ref:this.rootRef},this.props.rootProps),this.props.component&&{"data-clerk-component":this.props.component});return P.createElement(P.Fragment,null,!s&&P.createElement("div",se({},a)),this.props.children)}},go=s=>{var a,r;return P.createElement(P.Fragment,null,(a=s==null?void 0:s.customPagesPortals)==null?void 0:a.map((l,o)=>ee.createElement(l,{key:o})),(r=s==null?void 0:s.customMenuItemsPortals)==null?void 0:r.map((l,o)=>ee.createElement(l,{key:o})))};st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}});return P.createElement(P.Fragment,null,m&&r,s.loaded&&P.createElement(Kt,{component:a,mount:s.mountSignIn,unmount:s.unmountSignIn,updateProps:s.__unstable__updateProps,props:l,rootProps:g}))},{component:"SignIn",renderWhileLoading:!0});st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}});return P.createElement(P.Fragment,null,m&&r,s.loaded&&P.createElement(Kt,{component:a,mount:s.mountSignUp,unmount:s.unmountSignUp,updateProps:s.__unstable__updateProps,props:l,rootProps:g}))},{component:"SignUp",renderWhileLoading:!0});function jr({children:s}){return Pt(i1),P.createElement(P.Fragment,null,s)}function Er({children:s}){return Pt(s1),P.createElement(P.Fragment,null,s)}var W1=st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}}),{customPages:v,customPagesPortals:y}=bv(l.children);return P.createElement(P.Fragment,null,m&&r,P.createElement(Kt,{component:a,mount:s.mountUserProfile,unmount:s.unmountUserProfile,updateProps:s.__unstable__updateProps,props:Ve(se({},l),{customPages:v}),rootProps:g},P.createElement(go,{customPagesPortals:y})))},{component:"UserProfile",renderWhileLoading:!0});Object.assign(W1,{Page:jr,Link:Er});var jv=ee.createContext({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),eS=st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}}),{customPages:v,customPagesPortals:y}=bv(l.children,{allowForAnyChildren:!!l.__experimental_asProvider}),S=Object.assign(l.userProfileProps||{},{customPages:v}),{customMenuItems:x,customMenuItemsPortals:C}=Q1(l.children),U=Sv(l.children),Y={mount:s.mountUserButton,unmount:s.unmountUserButton,updateProps:s.__unstable__updateProps,props:Ve(se({},l),{userProfileProps:S,customMenuItems:x})},V={customPagesPortals:y,customMenuItemsPortals:C};return P.createElement(jv.Provider,{value:Y},m&&r,s.loaded&&P.createElement(Kt,Ve(se({component:a},Y),{hideRootHtmlElement:!!l.__experimental_asProvider,rootProps:g}),l.__experimental_asProvider?U:null,P.createElement(go,se({},V))))},{component:"UserButton",renderWhileLoading:!0});function vo({children:s}){return Pt(h1),P.createElement(P.Fragment,null,s)}function Ev({children:s}){return Pt(m1),P.createElement(P.Fragment,null,s)}function xv({children:s}){return Pt(p1),P.createElement(P.Fragment,null,s)}function tS(s){const a=ee.useContext(jv),r=Ve(se({},a),{props:se(se({},a.props),s)});return P.createElement(Kt,se({},r))}var Ql=Object.assign(eS,{UserProfilePage:jr,UserProfileLink:Er,MenuItems:vo,Action:Ev,Link:xv,__experimental_Outlet:tS});function yo({children:s}){return Pt(r1),P.createElement(P.Fragment,null,s)}function bo({children:s}){return Pt(l1),P.createElement(P.Fragment,null,s)}var nS=st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}}),{customPages:v,customPagesPortals:y}=_v(l.children);return P.createElement(P.Fragment,null,m&&r,s.loaded&&P.createElement(Kt,{component:a,mount:s.mountOrganizationProfile,unmount:s.unmountOrganizationProfile,updateProps:s.__unstable__updateProps,props:Ve(se({},l),{customPages:v}),rootProps:g},P.createElement(go,{customPagesPortals:y})))},{component:"OrganizationProfile",renderWhileLoading:!0});Object.assign(nS,{Page:yo,Link:bo});st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}});return P.createElement(P.Fragment,null,m&&r,s.loaded&&P.createElement(Kt,{component:a,mount:s.mountCreateOrganization,unmount:s.unmountCreateOrganization,updateProps:s.__unstable__updateProps,props:l,rootProps:g}))},{component:"CreateOrganization",renderWhileLoading:!0});var kv=ee.createContext({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),aS=st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}}),{customPages:v,customPagesPortals:y}=_v(l.children,{allowForAnyChildren:!!l.__experimental_asProvider}),S=Object.assign(l.organizationProfileProps||{},{customPages:v}),x=Sv(l.children),C={mount:s.mountOrganizationSwitcher,unmount:s.unmountOrganizationSwitcher,updateProps:s.__unstable__updateProps,props:Ve(se({},l),{organizationProfileProps:S}),rootProps:g,component:a};return s.__experimental_prefetchOrganizationSwitcher(),P.createElement(kv.Provider,{value:C},P.createElement(P.Fragment,null,m&&r,s.loaded&&P.createElement(Kt,Ve(se({},C),{hideRootHtmlElement:!!l.__experimental_asProvider}),l.__experimental_asProvider?x:null,P.createElement(go,{customPagesPortals:y}))))},{component:"OrganizationSwitcher",renderWhileLoading:!0});function iS(s){const a=ee.useContext(kv),r=Ve(se({},a),{props:se(se({},a.props),s)});return P.createElement(Kt,se({},r))}Object.assign(aS,{OrganizationProfilePage:yo,OrganizationProfileLink:bo,__experimental_Outlet:iS});st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}});return P.createElement(P.Fragment,null,m&&r,s.loaded&&P.createElement(Kt,{component:a,mount:s.mountOrganizationList,unmount:s.unmountOrganizationList,updateProps:s.__unstable__updateProps,props:l,rootProps:g}))},{component:"OrganizationList",renderWhileLoading:!0});st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}});return P.createElement(P.Fragment,null,m&&r,s.loaded&&P.createElement(Kt,{component:a,open:s.openGoogleOneTap,close:s.closeGoogleOneTap,updateProps:s.__unstable__updateProps,props:l,rootProps:g}))},{component:"GoogleOneTap",renderWhileLoading:!0});st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}});return P.createElement(P.Fragment,null,m&&r,s.loaded&&P.createElement(Kt,{component:a,mount:s.mountWaitlist,unmount:s.unmountWaitlist,updateProps:s.__unstable__updateProps,props:l,rootProps:g}))},{component:"Waitlist",renderWhileLoading:!0});st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}});return P.createElement(P.Fragment,null,m&&r,s.loaded&&P.createElement(Kt,{component:a,mount:s.mountPricingTable,unmount:s.unmountPricingTable,updateProps:s.__unstable__updateProps,props:l,rootProps:g}))},{component:"PricingTable",renderWhileLoading:!0});st(o=>{var u=o,{clerk:s,component:a,fallback:r}=u,l=Fe(u,["clerk","component","fallback"]);const m=gn(a)==="rendering"||!s.loaded,g=se({},m&&r&&{style:{display:"none"}});return P.createElement(P.Fragment,null,m&&r,s.loaded&&P.createElement(Kt,{component:a,mount:s.mountApiKeys,unmount:s.unmountApiKeys,updateProps:s.__unstable__updateProps,props:l,rootProps:g}))},{component:"ApiKeys",renderWhileLoading:!0});st(l=>{var o=l,{clerk:s,children:a}=o,r=Fe(o,["clerk","children"]);const J=r,{signUpFallbackRedirectUrl:u,forceRedirectUrl:f,fallbackRedirectUrl:m,signUpForceRedirectUrl:g,mode:v,initialValues:y,withSignUp:S,oauthFlow:x}=J,C=Fe(J,["signUpFallbackRedirectUrl","forceRedirectUrl","fallbackRedirectUrl","signUpForceRedirectUrl","mode","initialValues","withSignUp","oauthFlow"]);a=mo(a,"Sign in");const U=ho(a)("SignInButton"),Y=()=>{const q={forceRedirectUrl:f,fallbackRedirectUrl:m,signUpFallbackRedirectUrl:u,signUpForceRedirectUrl:g,initialValues:y,withSignUp:S,oauthFlow:x};return v==="modal"?s.openSignIn(Ve(se({},q),{appearance:r.appearance})):s.redirectToSignIn(Ve(se({},q),{signInFallbackRedirectUrl:m,signInForceRedirectUrl:f}))},V=q=>E(null,null,function*(){return U&&typeof U=="object"&&"props"in U&&(yield po(U.props.onClick)(q)),Y()}),F=Ve(se({},C),{onClick:V});return P.cloneElement(U,F)},{component:"SignInButton",renderWhileLoading:!0});st(l=>{var o=l,{clerk:s,children:a}=o,r=Fe(o,["clerk","children"]);const J=r,{fallbackRedirectUrl:u,forceRedirectUrl:f,signInFallbackRedirectUrl:m,signInForceRedirectUrl:g,mode:v,unsafeMetadata:y,initialValues:S,oauthFlow:x}=J,C=Fe(J,["fallbackRedirectUrl","forceRedirectUrl","signInFallbackRedirectUrl","signInForceRedirectUrl","mode","unsafeMetadata","initialValues","oauthFlow"]);a=mo(a,"Sign up");const U=ho(a)("SignUpButton"),Y=()=>{const q={fallbackRedirectUrl:u,forceRedirectUrl:f,signInFallbackRedirectUrl:m,signInForceRedirectUrl:g,unsafeMetadata:y,initialValues:S,oauthFlow:x};return v==="modal"?s.openSignUp(Ve(se({},q),{appearance:r.appearance})):s.redirectToSignUp(Ve(se({},q),{signUpFallbackRedirectUrl:u,signUpForceRedirectUrl:f}))},V=q=>E(null,null,function*(){return U&&typeof U=="object"&&"props"in U&&(yield po(U.props.onClick)(q)),Y()}),F=Ve(se({},C),{onClick:V});return P.cloneElement(U,F)},{component:"SignUpButton",renderWhileLoading:!0});st(l=>{var o=l,{clerk:s,children:a}=o,r=Fe(o,["clerk","children"]);const x=r,{redirectUrl:u="/",signOutOptions:f}=x,m=Fe(x,["redirectUrl","signOutOptions"]);a=mo(a,"Sign out");const g=ho(a)("SignOutButton"),v=()=>s.signOut(se({redirectUrl:u},f)),y=C=>E(null,null,function*(){return yield po(g.props.onClick)(C),v()}),S=Ve(se({},m),{onClick:y});return P.cloneElement(g,S)},{component:"SignOutButton",renderWhileLoading:!0});st(l=>{var o=l,{clerk:s,children:a}=o,r=Fe(o,["clerk","children"]);const S=r,{redirectUrl:u}=S,f=Fe(S,["redirectUrl"]);a=mo(a,"Sign in with Metamask");const m=ho(a)("SignInWithMetamaskButton"),g=()=>E(null,null,function*(){function x(){return E(this,null,function*(){yield s.authenticateWithMetamask({redirectUrl:u||void 0})})}x()}),v=x=>E(null,null,function*(){return yield po(m.props.onClick)(x),g()}),y=Ve(se({},f),{onClick:v});return P.cloneElement(m,y)},{component:"SignInWithMetamask",renderWhileLoading:!0});typeof globalThis.__BUILD_DISABLE_RHC__=="undefined"&&(globalThis.__BUILD_DISABLE_RHC__=!1);var sS={name:"@clerk/clerk-react",version:"5.32.2",environment:"production"},ao,as,is,Ta,Qn,Oa,io,jd,Tv=class Cv{constructor(a){ii(this,io),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenCheckout=null,this.preopenPlanDetails=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountApiKeysNodes=new Map,this.premountOAuthConsentNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],ii(this,ao,"loading"),ii(this,as),ii(this,is),ii(this,Ta),ii(this,Qn,Y1()),this.buildSignInUrl=o=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildSignInUrl(o))||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildSignInUrl",u)},this.buildSignUpUrl=o=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildSignUpUrl(o))||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildSignUpUrl",u)},this.buildAfterSignInUrl=(...o)=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildAfterSignInUrl(...o))||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildAfterSignInUrl",u)},this.buildAfterSignUpUrl=(...o)=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildAfterSignUpUrl(...o))||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildAfterSignUpUrl",u)},this.buildAfterSignOutUrl=()=>{const o=()=>{var u;return((u=this.clerkjs)==null?void 0:u.buildAfterSignOutUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildAfterSignOutUrl",o)},this.buildNewSubscriptionRedirectUrl=()=>{const o=()=>{var u;return((u=this.clerkjs)==null?void 0:u.buildNewSubscriptionRedirectUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildNewSubscriptionRedirectUrl",o)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{const o=()=>{var u;return((u=this.clerkjs)==null?void 0:u.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",o)},this.buildUserProfileUrl=()=>{const o=()=>{var u;return((u=this.clerkjs)==null?void 0:u.buildUserProfileUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildUserProfileUrl",o)},this.buildCreateOrganizationUrl=()=>{const o=()=>{var u;return((u=this.clerkjs)==null?void 0:u.buildCreateOrganizationUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildCreateOrganizationUrl",o)},this.buildOrganizationProfileUrl=()=>{const o=()=>{var u;return((u=this.clerkjs)==null?void 0:u.buildOrganizationProfileUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildOrganizationProfileUrl",o)},this.buildWaitlistUrl=()=>{const o=()=>{var u;return((u=this.clerkjs)==null?void 0:u.buildWaitlistUrl())||""};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("buildWaitlistUrl",o)},this.buildUrlWithAuth=o=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildUrlWithAuth(o))||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildUrlWithAuth",u)},this.handleUnauthenticated=()=>E(this,null,function*(){const o=()=>{var u;return(u=this.clerkjs)==null?void 0:u.handleUnauthenticated()};this.clerkjs&&this.loaded?o():this.premountMethodCalls.set("handleUnauthenticated",o)}),this.on=(...o)=>{var u;if((u=this.clerkjs)!=null&&u.on)return this.clerkjs.on(...o);tt(this,Qn).on(...o)},this.off=(...o)=>{var u;if((u=this.clerkjs)!=null&&u.off)return this.clerkjs.off(...o);tt(this,Qn).off(...o)},this.addOnLoaded=o=>{this.loadedListeners.push(o),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(o=>o()),this.loadedListeners=[]},this.beforeLoad=o=>{if(!o)throw new Error("Failed to hydrate latest Clerk JS")},this.hydrateClerkJS=o=>{var u;if(!o)throw new Error("Failed to hydrate latest Clerk JS");return this.clerkjs=o,this.premountMethodCalls.forEach(f=>f()),this.premountAddListenerCalls.forEach((f,m)=>{f.nativeUnsubscribe=o.addListener(m)}),(u=tt(this,Qn).internal.retrieveListeners("status"))==null||u.forEach(f=>{this.on("status",f,{notify:!0})}),this.preopenSignIn!==null&&o.openSignIn(this.preopenSignIn),this.preopenCheckout!==null&&o.__internal_openCheckout(this.preopenCheckout),this.preopenPlanDetails!==null&&o.__internal_openPlanDetails(this.preopenPlanDetails),this.preopenSignUp!==null&&o.openSignUp(this.preopenSignUp),this.preopenUserProfile!==null&&o.openUserProfile(this.preopenUserProfile),this.preopenUserVerification!==null&&o.__internal_openReverification(this.preopenUserVerification),this.preopenOneTap!==null&&o.openGoogleOneTap(this.preopenOneTap),this.preopenOrganizationProfile!==null&&o.openOrganizationProfile(this.preopenOrganizationProfile),this.preopenCreateOrganization!==null&&o.openCreateOrganization(this.preopenCreateOrganization),this.preOpenWaitlist!==null&&o.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((f,m)=>{o.mountSignIn(m,f)}),this.premountSignUpNodes.forEach((f,m)=>{o.mountSignUp(m,f)}),this.premountUserProfileNodes.forEach((f,m)=>{o.mountUserProfile(m,f)}),this.premountUserButtonNodes.forEach((f,m)=>{o.mountUserButton(m,f)}),this.premountOrganizationListNodes.forEach((f,m)=>{o.mountOrganizationList(m,f)}),this.premountWaitlistNodes.forEach((f,m)=>{o.mountWaitlist(m,f)}),this.premountPricingTableNodes.forEach((f,m)=>{o.mountPricingTable(m,f)}),this.premountApiKeysNodes.forEach((f,m)=>{o.mountApiKeys(m,f)}),this.premountOAuthConsentNodes.forEach((f,m)=>{o.__internal_mountOAuthConsent(m,f)}),typeof this.clerkjs.status=="undefined"&&tt(this,Qn).emit(Fl.Status,"ready"),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=o=>E(this,null,function*(){const u=yield sg(this,io,jd).call(this);if(u&&"__unstable__updateProps"in u)return u.__unstable__updateProps(o)}),this.__experimental_navigateToTask=o=>E(this,null,function*(){return this.clerkjs?this.clerkjs.__experimental_navigateToTask(o):Promise.reject()}),this.setActive=o=>this.clerkjs?this.clerkjs.setActive(o):Promise.reject(),this.openSignIn=o=>{this.clerkjs&&this.loaded?this.clerkjs.openSignIn(o):this.preopenSignIn=o},this.closeSignIn=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openCheckout=o=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openCheckout(o):this.preopenCheckout=o},this.__internal_closeCheckout=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeCheckout():this.preopenCheckout=null},this.__internal_openPlanDetails=o=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openPlanDetails(o):this.preopenPlanDetails=o},this.__internal_closePlanDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closePlanDetails():this.preopenPlanDetails=null},this.__internal_openReverification=o=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openReverification(o):this.preopenUserVerification=o},this.__internal_closeReverification=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=o=>{this.clerkjs&&this.loaded?this.clerkjs.openGoogleOneTap(o):this.preopenOneTap=o},this.closeGoogleOneTap=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=o=>{this.clerkjs&&this.loaded?this.clerkjs.openUserProfile(o):this.preopenUserProfile=o},this.closeUserProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=o=>{this.clerkjs&&this.loaded?this.clerkjs.openOrganizationProfile(o):this.preopenOrganizationProfile=o},this.closeOrganizationProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=o=>{this.clerkjs&&this.loaded?this.clerkjs.openCreateOrganization(o):this.preopenCreateOrganization=o},this.closeCreateOrganization=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=o=>{this.clerkjs&&this.loaded?this.clerkjs.openWaitlist(o):this.preOpenWaitlist=o},this.closeWaitlist=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=o=>{this.clerkjs&&this.loaded?this.clerkjs.openSignUp(o):this.preopenSignUp=o},this.closeSignUp=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignIn(o,u):this.premountSignInNodes.set(o,u)},this.unmountSignIn=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignIn(o):this.premountSignInNodes.delete(o)},this.mountSignUp=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignUp(o,u):this.premountSignUpNodes.set(o,u)},this.unmountSignUp=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignUp(o):this.premountSignUpNodes.delete(o)},this.mountUserProfile=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserProfile(o,u):this.premountUserProfileNodes.set(o,u)},this.unmountUserProfile=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserProfile(o):this.premountUserProfileNodes.delete(o)},this.mountOrganizationProfile=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationProfile(o,u):this.premountOrganizationProfileNodes.set(o,u)},this.unmountOrganizationProfile=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationProfile(o):this.premountOrganizationProfileNodes.delete(o)},this.mountCreateOrganization=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.mountCreateOrganization(o,u):this.premountCreateOrganizationNodes.set(o,u)},this.unmountCreateOrganization=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountCreateOrganization(o):this.premountCreateOrganizationNodes.delete(o)},this.mountOrganizationSwitcher=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationSwitcher(o,u):this.premountOrganizationSwitcherNodes.set(o,u)},this.unmountOrganizationSwitcher=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationSwitcher(o):this.premountOrganizationSwitcherNodes.delete(o)},this.__experimental_prefetchOrganizationSwitcher=()=>{const o=()=>{var u;return(u=this.clerkjs)==null?void 0:u.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&this.loaded?o():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",o)},this.mountOrganizationList=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationList(o,u):this.premountOrganizationListNodes.set(o,u)},this.unmountOrganizationList=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationList(o):this.premountOrganizationListNodes.delete(o)},this.mountUserButton=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserButton(o,u):this.premountUserButtonNodes.set(o,u)},this.unmountUserButton=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserButton(o):this.premountUserButtonNodes.delete(o)},this.mountWaitlist=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.mountWaitlist(o,u):this.premountWaitlistNodes.set(o,u)},this.unmountWaitlist=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountWaitlist(o):this.premountWaitlistNodes.delete(o)},this.mountPricingTable=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.mountPricingTable(o,u):this.premountPricingTableNodes.set(o,u)},this.unmountPricingTable=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountPricingTable(o):this.premountPricingTableNodes.delete(o)},this.mountApiKeys=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.mountApiKeys(o,u):this.premountApiKeysNodes.set(o,u)},this.unmountApiKeys=o=>{this.clerkjs&&this.loaded?this.clerkjs.unmountApiKeys(o):this.premountApiKeysNodes.delete(o)},this.__internal_mountOAuthConsent=(o,u)=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_mountOAuthConsent(o,u):this.premountOAuthConsentNodes.set(o,u)},this.__internal_unmountOAuthConsent=o=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_unmountOAuthConsent(o):this.premountOAuthConsentNodes.delete(o)},this.addListener=o=>{if(this.clerkjs)return this.clerkjs.addListener(o);{const u=()=>{var f;const m=this.premountAddListenerCalls.get(o);m&&((f=m.nativeUnsubscribe)==null||f.call(m),this.premountAddListenerCalls.delete(o))};return this.premountAddListenerCalls.set(o,{unsubscribe:u,nativeUnsubscribe:void 0}),u}},this.navigate=o=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.navigate(o)};this.clerkjs&&this.loaded?u():this.premountMethodCalls.set("navigate",u)},this.redirectWithAuth=(...o)=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectWithAuth(...o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectWithAuth",u)}),this.redirectToSignIn=o=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToSignIn(o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToSignIn",u)}),this.redirectToSignUp=o=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToSignUp(o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToSignUp",u)}),this.redirectToUserProfile=()=>E(this,null,function*(){const o=()=>{var u;return(u=this.clerkjs)==null?void 0:u.redirectToUserProfile()};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("redirectToUserProfile",o)}),this.redirectToAfterSignUp=()=>{const o=()=>{var u;return(u=this.clerkjs)==null?void 0:u.redirectToAfterSignUp()};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("redirectToAfterSignUp",o)},this.redirectToAfterSignIn=()=>{const o=()=>{var u;return(u=this.clerkjs)==null?void 0:u.redirectToAfterSignIn()};this.clerkjs&&this.loaded?o():this.premountMethodCalls.set("redirectToAfterSignIn",o)},this.redirectToAfterSignOut=()=>{const o=()=>{var u;return(u=this.clerkjs)==null?void 0:u.redirectToAfterSignOut()};this.clerkjs&&this.loaded?o():this.premountMethodCalls.set("redirectToAfterSignOut",o)},this.redirectToOrganizationProfile=()=>E(this,null,function*(){const o=()=>{var u;return(u=this.clerkjs)==null?void 0:u.redirectToOrganizationProfile()};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("redirectToOrganizationProfile",o)}),this.redirectToCreateOrganization=()=>E(this,null,function*(){const o=()=>{var u;return(u=this.clerkjs)==null?void 0:u.redirectToCreateOrganization()};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("redirectToCreateOrganization",o)}),this.redirectToWaitlist=()=>E(this,null,function*(){const o=()=>{var u;return(u=this.clerkjs)==null?void 0:u.redirectToWaitlist()};if(this.clerkjs&&this.loaded)return o();this.premountMethodCalls.set("redirectToWaitlist",o)}),this.handleRedirectCallback=o=>E(this,null,function*(){var u;const f=()=>{var m;return(m=this.clerkjs)==null?void 0:m.handleRedirectCallback(o)};this.clerkjs&&this.loaded?(u=f())==null||u.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",f)}),this.handleGoogleOneTapCallback=(o,u)=>E(this,null,function*(){var f;const m=()=>{var g;return(g=this.clerkjs)==null?void 0:g.handleGoogleOneTapCallback(o,u)};this.clerkjs&&this.loaded?(f=m())==null||f.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",m)}),this.handleEmailLinkVerification=o=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.handleEmailLinkVerification(o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("handleEmailLinkVerification",u)}),this.authenticateWithMetamask=o=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.authenticateWithMetamask(o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("authenticateWithMetamask",u)}),this.authenticateWithCoinbaseWallet=o=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.authenticateWithCoinbaseWallet(o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",u)}),this.authenticateWithOKXWallet=o=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.authenticateWithOKXWallet(o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("authenticateWithOKXWallet",u)}),this.authenticateWithWeb3=o=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.authenticateWithWeb3(o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("authenticateWithWeb3",u)}),this.authenticateWithGoogleOneTap=o=>E(this,null,function*(){return(yield sg(this,io,jd).call(this)).authenticateWithGoogleOneTap(o)}),this.createOrganization=o=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.createOrganization(o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("createOrganization",u)}),this.getOrganization=o=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.getOrganization(o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("getOrganization",u)}),this.joinWaitlist=o=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.joinWaitlist(o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("joinWaitlist",u)}),this.signOut=(...o)=>E(this,null,function*(){const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.signOut(...o)};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("signOut",u)});const{Clerk:r=null,publishableKey:l}=a||{};Fi(this,Ta,l),Fi(this,is,a==null?void 0:a.proxyUrl),Fi(this,as,a==null?void 0:a.domain),this.options=a,this.Clerk=r,this.mode=og()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=sS),tt(this,Qn).emit(Fl.Status,"loading"),tt(this,Qn).prioritizedOn(Fl.Status,o=>Fi(this,ao,o)),tt(this,Ta)&&this.loadClerkJS()}get publishableKey(){return tt(this,Ta)}get loaded(){var a;return((a=this.clerkjs)==null?void 0:a.loaded)||!1}get status(){var a;return this.clerkjs?((a=this.clerkjs)==null?void 0:a.status)||(this.clerkjs.loaded?"ready":"loading"):tt(this,ao)}static getOrCreateInstance(a){return(!og()||!tt(this,Oa)||a.Clerk&&tt(this,Oa).Clerk!==a.Clerk||tt(this,Oa).publishableKey!==a.publishableKey)&&Fi(this,Oa,new Cv(a)),tt(this,Oa)}static clearInstance(){Fi(this,Oa,null)}get domain(){return typeof window!="undefined"&&window.location?Ju(tt(this,as),new URL(window.location.href),""):typeof tt(this,as)=="function"?Mn.throw(Zu):tt(this,as)||""}get proxyUrl(){return typeof window!="undefined"&&window.location?Ju(tt(this,is),new URL(window.location.href),""):typeof tt(this,is)=="function"?Mn.throw(Zu):tt(this,is)||""}__internal_getOption(a){var r,l;return(r=this.clerkjs)!=null&&r.__internal_getOption?(l=this.clerkjs)==null?void 0:l.__internal_getOption(a):this.options[a]}get sdkMetadata(){var a;return((a=this.clerkjs)==null?void 0:a.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var a;return(a=this.clerkjs)==null?void 0:a.instanceType}get frontendApi(){var a;return((a=this.clerkjs)==null?void 0:a.frontendApi)||""}get isStandardBrowser(){var a;return((a=this.clerkjs)==null?void 0:a.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return typeof window!="undefined"&&window.location?Ju(this.options.isSatellite,new URL(window.location.href),!1):typeof this.options.isSatellite=="function"?Mn.throw(Zu):!1}loadClerkJS(){return E(this,null,function*(){var a;if(!(this.mode!=="browser"||this.loaded)){typeof window!="undefined"&&(window.__clerk_publishable_key=tt(this,Ta),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let r;F1(this.Clerk)?(r=new this.Clerk(tt(this,Ta),{proxyUrl:this.proxyUrl,domain:this.domain}),this.beforeLoad(r),yield r.load(this.options)):(r=this.Clerk,r.loaded||(this.beforeLoad(r),yield r.load(this.options))),global.Clerk=r}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||(yield L1(Ve(se({},this.options),{publishableKey:tt(this,Ta),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}))),!global.Clerk)throw new Error("Failed to download latest ClerkJS. Contact <EMAIL>.");this.beforeLoad(global.Clerk),yield global.Clerk.load(this.options)}return(a=global.Clerk)!=null&&a.loaded?this.hydrateClerkJS(global.Clerk):void 0}catch(r){const l=r;tt(this,Qn).emit(Fl.Status,"error"),console.error(l.stack||l.message||l);return}}})}get version(){var a;return(a=this.clerkjs)==null?void 0:a.version}get client(){if(this.clerkjs)return this.clerkjs.client}get session(){if(this.clerkjs)return this.clerkjs.session}get user(){if(this.clerkjs)return this.clerkjs.user}get organization(){if(this.clerkjs)return this.clerkjs.organization}get telemetry(){if(this.clerkjs)return this.clerkjs.telemetry}get __unstable__environment(){if(this.clerkjs)return this.clerkjs.__unstable__environment}get isSignedIn(){return this.clerkjs?this.clerkjs.isSignedIn:!1}get billing(){var a;return(a=this.clerkjs)==null?void 0:a.billing}get apiKeys(){var a;return(a=this.clerkjs)==null?void 0:a.apiKeys}__unstable__setEnvironment(...a){if(this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs)this.clerkjs.__unstable__setEnvironment(a);else return}};ao=new WeakMap;as=new WeakMap;is=new WeakMap;Ta=new WeakMap;Qn=new WeakMap;Oa=new WeakMap;io=new WeakSet;jd=function(){return new Promise(s=>{this.addOnLoaded(()=>s(this.clerkjs))})};ii(Tv,Oa);var gg=Tv;function rS(s){const{isomorphicClerkOptions:a,initialState:r,children:l}=s,{isomorphicClerk:o,clerkStatus:u}=lS(a),[f,m]=P.useState({client:o.client,session:o.session,user:o.user,organization:o.organization});P.useEffect(()=>o.addListener(Ae=>m(se({},Ae))),[]);const g=I1(o.loaded,f,r),v=P.useMemo(()=>({value:o}),[u]),y=P.useMemo(()=>({value:f.client}),[f.client]),{sessionId:S,sessionStatus:x,sessionClaims:C,session:U,userId:Y,user:V,orgId:F,actor:J,organization:q,orgRole:te,orgSlug:K,orgPermissions:W,factorVerificationAge:le}=g,ne=P.useMemo(()=>({value:{sessionId:S,sessionStatus:x,sessionClaims:C,userId:Y,actor:J,orgId:F,orgRole:te,orgSlug:K,orgPermissions:W,factorVerificationAge:le}}),[S,x,Y,J,F,te,K,le,C==null?void 0:C.__raw]),ye=P.useMemo(()=>({value:U}),[S,U]),ge=P.useMemo(()=>({value:V}),[Y,V]),ue=P.useMemo(()=>({value:{organization:q}}),[F,q]);return P.createElement(e1.Provider,{value:v},P.createElement($_.Provider,{value:y},P.createElement(G_.Provider,{value:ye},P.createElement(Y_,se({},ue.value),P.createElement(J_.Provider,{value:ne},P.createElement(I_.Provider,{value:ge},l))))))}var lS=s=>{const a=P.useRef(gg.getOrCreateInstance(s)),[r,l]=P.useState(a.current.status);return P.useEffect(()=>{a.current.__unstable__updateProps({appearance:s.appearance})},[s.appearance]),P.useEffect(()=>{a.current.__unstable__updateProps({options:s})},[s.localization]),P.useEffect(()=>(a.current.on("status",l),()=>{a.current&&a.current.off("status",l),gg.clearInstance()}),[]),{isomorphicClerk:a.current,clerkStatus:r}};function oS(s){const m=s,{initialState:a,children:r,__internal_bypassMissingPublishableKey:l}=m,o=Fe(m,["initialState","children","__internal_bypassMissingPublishableKey"]),{publishableKey:u="",Clerk:f}=o;return!f&&!l&&(u?u&&!ud(u)&&Mn.throwInvalidPublishableKeyError({key:u}):Mn.throwMissingPublishableKeyError()),P.createElement(rS,{initialState:a,isomorphicClerkOptions:o},r)}var Av=X1(oS,"ClerkProvider",t1);Av.displayName="ClerkProvider";Z_({packageName:"@clerk/clerk-react"});z1("@clerk/clerk-react");const cS="modulepreload",uS=function(s){return"/"+s},vg={},xr=function(a,r,l){let o=Promise.resolve();if(r&&r.length>0){let f=function(v){return Promise.all(v.map(y=>Promise.resolve(y).then(S=>({status:"fulfilled",value:S}),S=>({status:"rejected",reason:S}))))};document.getElementsByTagName("link");const m=document.querySelector("meta[property=csp-nonce]"),g=(m==null?void 0:m.nonce)||(m==null?void 0:m.getAttribute("nonce"));o=f(r.map(v=>{if(v=uS(v),v in vg)return;vg[v]=!0;const y=v.endsWith(".css"),S=y?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${v}"]${S}`))return;const x=document.createElement("link");if(x.rel=y?"stylesheet":cS,y||(x.as="script"),x.crossOrigin="",x.href=v,g&&x.setAttribute("nonce",g),document.head.appendChild(x),y)return new Promise((C,U)=>{x.addEventListener("load",C),x.addEventListener("error",()=>U(new Error(`Unable to preload CSS for ${v}`)))})}))}function u(f){const m=new Event("vite:preloadError",{cancelable:!0});if(m.payload=f,window.dispatchEvent(m),!m.defaultPrevented)throw f}return o.then(f=>{for(const m of f||[])m.status==="rejected"&&u(m.reason);return a().catch(u)})},dS=s=>{let a;return s?a=s:typeof fetch=="undefined"?a=(...r)=>xr(()=>E(null,null,function*(){const{default:l}=yield Promise.resolve().then(()=>cs);return{default:l}}),void 0).then(({default:l})=>l(...r)):a=fetch,(...r)=>a(...r)};class Bd extends Error{constructor(a,r="FunctionsError",l){super(a),this.name=r,this.context=l}}class fS extends Bd{constructor(a){super("Failed to send a request to the Edge Function","FunctionsFetchError",a)}}class hS extends Bd{constructor(a){super("Relay Error invoking the Edge Function","FunctionsRelayError",a)}}class mS extends Bd{constructor(a){super("Edge Function returned a non-2xx status code","FunctionsHttpError",a)}}var Ed;(function(s){s.Any="any",s.ApNortheast1="ap-northeast-1",s.ApNortheast2="ap-northeast-2",s.ApSouth1="ap-south-1",s.ApSoutheast1="ap-southeast-1",s.ApSoutheast2="ap-southeast-2",s.CaCentral1="ca-central-1",s.EuCentral1="eu-central-1",s.EuWest1="eu-west-1",s.EuWest2="eu-west-2",s.EuWest3="eu-west-3",s.SaEast1="sa-east-1",s.UsEast1="us-east-1",s.UsWest1="us-west-1",s.UsWest2="us-west-2"})(Ed||(Ed={}));var pS=function(s,a,r,l){function o(u){return u instanceof r?u:new r(function(f){f(u)})}return new(r||(r=Promise))(function(u,f){function m(y){try{v(l.next(y))}catch(S){f(S)}}function g(y){try{v(l.throw(y))}catch(S){f(S)}}function v(y){y.done?u(y.value):o(y.value).then(m,g)}v((l=l.apply(s,a||[])).next())})};class gS{constructor(a,{headers:r={},customFetch:l,region:o=Ed.Any}={}){this.url=a,this.headers=r,this.region=o,this.fetch=dS(l)}setAuth(a){this.headers.Authorization=`Bearer ${a}`}invoke(a,r={}){var l;return pS(this,void 0,void 0,function*(){try{const{headers:o,method:u,body:f}=r;let m={},{region:g}=r;g||(g=this.region),g&&g!=="any"&&(m["x-region"]=g);let v;f&&(o&&!Object.prototype.hasOwnProperty.call(o,"Content-Type")||!o)&&(typeof Blob!="undefined"&&f instanceof Blob||f instanceof ArrayBuffer?(m["Content-Type"]="application/octet-stream",v=f):typeof f=="string"?(m["Content-Type"]="text/plain",v=f):typeof FormData!="undefined"&&f instanceof FormData?v=f:(m["Content-Type"]="application/json",v=JSON.stringify(f)));const y=yield this.fetch(`${this.url}/${a}`,{method:u||"POST",headers:Object.assign(Object.assign(Object.assign({},m),this.headers),o),body:v}).catch(U=>{throw new fS(U)}),S=y.headers.get("x-relay-error");if(S&&S==="true")throw new hS(y);if(!y.ok)throw new mS(y);let x=((l=y.headers.get("Content-Type"))!==null&&l!==void 0?l:"text/plain").split(";")[0].trim(),C;return x==="application/json"?C=yield y.json():x==="application/octet-stream"?C=yield y.blob():x==="text/event-stream"?C=y:x==="multipart/form-data"?C=yield y.formData():C=yield y.text(),{data:C,error:null}}catch(o){return{data:null,error:o}}})}}var kt={},Ki={},Xi={},Qi={},Zi={},Ji={},vS=function(){if(typeof self!="undefined")return self;if(typeof window!="undefined")return window;if(typeof global!="undefined")return global;throw new Error("unable to locate global object")},ls=vS();const yS=ls.fetch,Ov=ls.fetch.bind(ls),Nv=ls.Headers,bS=ls.Request,_S=ls.Response,cs=Object.freeze(Object.defineProperty({__proto__:null,Headers:Nv,Request:bS,Response:_S,default:Ov,fetch:yS},Symbol.toStringTag,{value:"Module"})),SS=sb(cs);var Zl={},yg;function Rv(){if(yg)return Zl;yg=1,Object.defineProperty(Zl,"__esModule",{value:!0});class s extends Error{constructor(r){super(r.message),this.name="PostgrestError",this.details=r.details,this.hint=r.hint,this.code=r.code}}return Zl.default=s,Zl}var bg;function Mv(){if(bg)return Ji;bg=1;var s=Ji&&Ji.__importDefault||function(o){return o&&o.__esModule?o:{default:o}};Object.defineProperty(Ji,"__esModule",{value:!0});const a=s(SS),r=s(Rv());class l{constructor(u){this.shouldThrowOnError=!1,this.method=u.method,this.url=u.url,this.headers=u.headers,this.schema=u.schema,this.body=u.body,this.shouldThrowOnError=u.shouldThrowOnError,this.signal=u.signal,this.isMaybeSingle=u.isMaybeSingle,u.fetch?this.fetch=u.fetch:typeof fetch=="undefined"?this.fetch=a.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(u,f){return this.headers=Object.assign({},this.headers),this.headers[u]=f,this}then(u,f){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const m=this.fetch;let g=m(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(v=>E(this,null,function*(){var y,S,x;let C=null,U=null,Y=null,V=v.status,F=v.statusText;if(v.ok){if(this.method!=="HEAD"){const K=yield v.text();K===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?U=K:U=JSON.parse(K))}const q=(y=this.headers.Prefer)===null||y===void 0?void 0:y.match(/count=(exact|planned|estimated)/),te=(S=v.headers.get("content-range"))===null||S===void 0?void 0:S.split("/");q&&te&&te.length>1&&(Y=parseInt(te[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(U)&&(U.length>1?(C={code:"PGRST116",details:`Results contain ${U.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},U=null,Y=null,V=406,F="Not Acceptable"):U.length===1?U=U[0]:U=null)}else{const q=yield v.text();try{C=JSON.parse(q),Array.isArray(C)&&v.status===404&&(U=[],C=null,V=200,F="OK")}catch(te){v.status===404&&q===""?(V=204,F="No Content"):C={message:q}}if(C&&this.isMaybeSingle&&(!((x=C==null?void 0:C.details)===null||x===void 0)&&x.includes("0 rows"))&&(C=null,V=200,F="OK"),C&&this.shouldThrowOnError)throw new r.default(C)}return{error:C,data:U,count:Y,status:V,statusText:F}}));return this.shouldThrowOnError||(g=g.catch(v=>{var y,S,x;return{error:{message:`${(y=v==null?void 0:v.name)!==null&&y!==void 0?y:"FetchError"}: ${v==null?void 0:v.message}`,details:`${(S=v==null?void 0:v.stack)!==null&&S!==void 0?S:""}`,hint:"",code:`${(x=v==null?void 0:v.code)!==null&&x!==void 0?x:""}`},data:null,count:null,status:0,statusText:""}})),g.then(u,f)}returns(){return this}overrideTypes(){return this}}return Ji.default=l,Ji}var _g;function Uv(){if(_g)return Zi;_g=1;var s=Zi&&Zi.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(Zi,"__esModule",{value:!0});const a=s(Mv());class r extends a.default{select(o){let u=!1;const f=(o!=null?o:"*").split("").map(m=>/\s/.test(m)&&!u?"":(m==='"'&&(u=!u),m)).join("");return this.url.searchParams.set("select",f),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(o,{ascending:u=!0,nullsFirst:f,foreignTable:m,referencedTable:g=m}={}){const v=g?`${g}.order`:"order",y=this.url.searchParams.get(v);return this.url.searchParams.set(v,`${y?`${y},`:""}${o}.${u?"asc":"desc"}${f===void 0?"":f?".nullsfirst":".nullslast"}`),this}limit(o,{foreignTable:u,referencedTable:f=u}={}){const m=typeof f=="undefined"?"limit":`${f}.limit`;return this.url.searchParams.set(m,`${o}`),this}range(o,u,{foreignTable:f,referencedTable:m=f}={}){const g=typeof m=="undefined"?"offset":`${m}.offset`,v=typeof m=="undefined"?"limit":`${m}.limit`;return this.url.searchParams.set(g,`${o}`),this.url.searchParams.set(v,`${u-o+1}`),this}abortSignal(o){return this.signal=o,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:o=!1,verbose:u=!1,settings:f=!1,buffers:m=!1,wal:g=!1,format:v="text"}={}){var y;const S=[o?"analyze":null,u?"verbose":null,f?"settings":null,m?"buffers":null,g?"wal":null].filter(Boolean).join("|"),x=(y=this.headers.Accept)!==null&&y!==void 0?y:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${v}; for="${x}"; options=${S};`,v==="json"?this:this}rollback(){var o;return((o=this.headers.Prefer)!==null&&o!==void 0?o:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return Zi.default=r,Zi}var Sg;function qd(){if(Sg)return Qi;Sg=1;var s=Qi&&Qi.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(Qi,"__esModule",{value:!0});const a=s(Uv());class r extends a.default{eq(o,u){return this.url.searchParams.append(o,`eq.${u}`),this}neq(o,u){return this.url.searchParams.append(o,`neq.${u}`),this}gt(o,u){return this.url.searchParams.append(o,`gt.${u}`),this}gte(o,u){return this.url.searchParams.append(o,`gte.${u}`),this}lt(o,u){return this.url.searchParams.append(o,`lt.${u}`),this}lte(o,u){return this.url.searchParams.append(o,`lte.${u}`),this}like(o,u){return this.url.searchParams.append(o,`like.${u}`),this}likeAllOf(o,u){return this.url.searchParams.append(o,`like(all).{${u.join(",")}}`),this}likeAnyOf(o,u){return this.url.searchParams.append(o,`like(any).{${u.join(",")}}`),this}ilike(o,u){return this.url.searchParams.append(o,`ilike.${u}`),this}ilikeAllOf(o,u){return this.url.searchParams.append(o,`ilike(all).{${u.join(",")}}`),this}ilikeAnyOf(o,u){return this.url.searchParams.append(o,`ilike(any).{${u.join(",")}}`),this}is(o,u){return this.url.searchParams.append(o,`is.${u}`),this}in(o,u){const f=Array.from(new Set(u)).map(m=>typeof m=="string"&&new RegExp("[,()]").test(m)?`"${m}"`:`${m}`).join(",");return this.url.searchParams.append(o,`in.(${f})`),this}contains(o,u){return typeof u=="string"?this.url.searchParams.append(o,`cs.${u}`):Array.isArray(u)?this.url.searchParams.append(o,`cs.{${u.join(",")}}`):this.url.searchParams.append(o,`cs.${JSON.stringify(u)}`),this}containedBy(o,u){return typeof u=="string"?this.url.searchParams.append(o,`cd.${u}`):Array.isArray(u)?this.url.searchParams.append(o,`cd.{${u.join(",")}}`):this.url.searchParams.append(o,`cd.${JSON.stringify(u)}`),this}rangeGt(o,u){return this.url.searchParams.append(o,`sr.${u}`),this}rangeGte(o,u){return this.url.searchParams.append(o,`nxl.${u}`),this}rangeLt(o,u){return this.url.searchParams.append(o,`sl.${u}`),this}rangeLte(o,u){return this.url.searchParams.append(o,`nxr.${u}`),this}rangeAdjacent(o,u){return this.url.searchParams.append(o,`adj.${u}`),this}overlaps(o,u){return typeof u=="string"?this.url.searchParams.append(o,`ov.${u}`):this.url.searchParams.append(o,`ov.{${u.join(",")}}`),this}textSearch(o,u,{config:f,type:m}={}){let g="";m==="plain"?g="pl":m==="phrase"?g="ph":m==="websearch"&&(g="w");const v=f===void 0?"":`(${f})`;return this.url.searchParams.append(o,`${g}fts${v}.${u}`),this}match(o){return Object.entries(o).forEach(([u,f])=>{this.url.searchParams.append(u,`eq.${f}`)}),this}not(o,u,f){return this.url.searchParams.append(o,`not.${u}.${f}`),this}or(o,{foreignTable:u,referencedTable:f=u}={}){const m=f?`${f}.or`:"or";return this.url.searchParams.append(m,`(${o})`),this}filter(o,u,f){return this.url.searchParams.append(o,`${u}.${f}`),this}}return Qi.default=r,Qi}var wg;function Dv(){if(wg)return Xi;wg=1;var s=Xi&&Xi.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(Xi,"__esModule",{value:!0});const a=s(qd());class r{constructor(o,{headers:u={},schema:f,fetch:m}){this.url=o,this.headers=u,this.schema=f,this.fetch=m}select(o,{head:u=!1,count:f}={}){const m=u?"HEAD":"GET";let g=!1;const v=(o!=null?o:"*").split("").map(y=>/\s/.test(y)&&!g?"":(y==='"'&&(g=!g),y)).join("");return this.url.searchParams.set("select",v),f&&(this.headers.Prefer=`count=${f}`),new a.default({method:m,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(o,{count:u,defaultToNull:f=!0}={}){const m="POST",g=[];if(this.headers.Prefer&&g.push(this.headers.Prefer),u&&g.push(`count=${u}`),f||g.push("missing=default"),this.headers.Prefer=g.join(","),Array.isArray(o)){const v=o.reduce((y,S)=>y.concat(Object.keys(S)),[]);if(v.length>0){const y=[...new Set(v)].map(S=>`"${S}"`);this.url.searchParams.set("columns",y.join(","))}}return new a.default({method:m,url:this.url,headers:this.headers,schema:this.schema,body:o,fetch:this.fetch,allowEmpty:!1})}upsert(o,{onConflict:u,ignoreDuplicates:f=!1,count:m,defaultToNull:g=!0}={}){const v="POST",y=[`resolution=${f?"ignore":"merge"}-duplicates`];if(u!==void 0&&this.url.searchParams.set("on_conflict",u),this.headers.Prefer&&y.push(this.headers.Prefer),m&&y.push(`count=${m}`),g||y.push("missing=default"),this.headers.Prefer=y.join(","),Array.isArray(o)){const S=o.reduce((x,C)=>x.concat(Object.keys(C)),[]);if(S.length>0){const x=[...new Set(S)].map(C=>`"${C}"`);this.url.searchParams.set("columns",x.join(","))}}return new a.default({method:v,url:this.url,headers:this.headers,schema:this.schema,body:o,fetch:this.fetch,allowEmpty:!1})}update(o,{count:u}={}){const f="PATCH",m=[];return this.headers.Prefer&&m.push(this.headers.Prefer),u&&m.push(`count=${u}`),this.headers.Prefer=m.join(","),new a.default({method:f,url:this.url,headers:this.headers,schema:this.schema,body:o,fetch:this.fetch,allowEmpty:!1})}delete({count:o}={}){const u="DELETE",f=[];return o&&f.push(`count=${o}`),this.headers.Prefer&&f.unshift(this.headers.Prefer),this.headers.Prefer=f.join(","),new a.default({method:u,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}return Xi.default=r,Xi}var cr={},ur={},jg;function wS(){return jg||(jg=1,Object.defineProperty(ur,"__esModule",{value:!0}),ur.version=void 0,ur.version="0.0.0-automated"),ur}var Eg;function jS(){if(Eg)return cr;Eg=1,Object.defineProperty(cr,"__esModule",{value:!0}),cr.DEFAULT_HEADERS=void 0;const s=wS();return cr.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${s.version}`},cr}var xg;function ES(){if(xg)return Ki;xg=1;var s=Ki&&Ki.__importDefault||function(u){return u&&u.__esModule?u:{default:u}};Object.defineProperty(Ki,"__esModule",{value:!0});const a=s(Dv()),r=s(qd()),l=jS();class o{constructor(f,{headers:m={},schema:g,fetch:v}={}){this.url=f,this.headers=Object.assign(Object.assign({},l.DEFAULT_HEADERS),m),this.schemaName=g,this.fetch=v}from(f){const m=new URL(`${this.url}/${f}`);return new a.default(m,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(f){return new o(this.url,{headers:this.headers,schema:f,fetch:this.fetch})}rpc(f,m={},{head:g=!1,get:v=!1,count:y}={}){let S;const x=new URL(`${this.url}/rpc/${f}`);let C;g||v?(S=g?"HEAD":"GET",Object.entries(m).filter(([Y,V])=>V!==void 0).map(([Y,V])=>[Y,Array.isArray(V)?`{${V.join(",")}}`:`${V}`]).forEach(([Y,V])=>{x.searchParams.append(Y,V)})):(S="POST",C=m);const U=Object.assign({},this.headers);return y&&(U.Prefer=`count=${y}`),new r.default({method:S,url:x,headers:U,schema:this.schemaName,body:C,fetch:this.fetch,allowEmpty:!1})}}return Ki.default=o,Ki}var kg;function xS(){if(kg)return kt;kg=1;var s=kt&&kt.__importDefault||function(m){return m&&m.__esModule?m:{default:m}};Object.defineProperty(kt,"__esModule",{value:!0}),kt.PostgrestError=kt.PostgrestBuilder=kt.PostgrestTransformBuilder=kt.PostgrestFilterBuilder=kt.PostgrestQueryBuilder=kt.PostgrestClient=void 0;const a=s(ES());kt.PostgrestClient=a.default;const r=s(Dv());kt.PostgrestQueryBuilder=r.default;const l=s(qd());kt.PostgrestFilterBuilder=l.default;const o=s(Uv());kt.PostgrestTransformBuilder=o.default;const u=s(Mv());kt.PostgrestBuilder=u.default;const f=s(Rv());return kt.PostgrestError=f.default,kt.default={PostgrestClient:a.default,PostgrestQueryBuilder:r.default,PostgrestFilterBuilder:l.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:u.default,PostgrestError:f.default},kt}var kS=xS();const TS=Md(kS),{PostgrestClient:CS,PostgrestQueryBuilder:j2,PostgrestFilterBuilder:E2,PostgrestTransformBuilder:x2,PostgrestBuilder:k2,PostgrestError:T2}=TS;function AS(){if(typeof WebSocket!="undefined")return WebSocket;if(typeof global.WebSocket!="undefined")return global.WebSocket;if(typeof window.WebSocket!="undefined")return window.WebSocket;if(typeof self.WebSocket!="undefined")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const OS=AS(),NS="2.11.15",RS=`realtime-js/${NS}`,MS="1.0.0",zv=1e4,US=1e3;var pr;(function(s){s[s.connecting=0]="connecting",s[s.open=1]="open",s[s.closing=2]="closing",s[s.closed=3]="closed"})(pr||(pr={}));var Dt;(function(s){s.closed="closed",s.errored="errored",s.joined="joined",s.joining="joining",s.leaving="leaving"})(Dt||(Dt={}));var En;(function(s){s.close="phx_close",s.error="phx_error",s.join="phx_join",s.reply="phx_reply",s.leave="phx_leave",s.access_token="access_token"})(En||(En={}));var xd;(function(s){s.websocket="websocket"})(xd||(xd={}));var ri;(function(s){s.Connecting="connecting",s.Open="open",s.Closing="closing",s.Closed="closed"})(ri||(ri={}));class DS{constructor(){this.HEADER_LENGTH=1}decode(a,r){return a.constructor===ArrayBuffer?r(this._binaryDecode(a)):r(typeof a=="string"?JSON.parse(a):{})}_binaryDecode(a){const r=new DataView(a),l=new TextDecoder;return this._decodeBroadcast(a,r,l)}_decodeBroadcast(a,r,l){const o=r.getUint8(1),u=r.getUint8(2);let f=this.HEADER_LENGTH+2;const m=l.decode(a.slice(f,f+o));f=f+o;const g=l.decode(a.slice(f,f+u));f=f+u;const v=JSON.parse(l.decode(a.slice(f,a.byteLength)));return{ref:null,topic:m,event:g,payload:v}}}class Lv{constructor(a,r){this.callback=a,this.timerCalc=r,this.timer=void 0,this.tries=0,this.callback=a,this.timerCalc=r}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var Xe;(function(s){s.abstime="abstime",s.bool="bool",s.date="date",s.daterange="daterange",s.float4="float4",s.float8="float8",s.int2="int2",s.int4="int4",s.int4range="int4range",s.int8="int8",s.int8range="int8range",s.json="json",s.jsonb="jsonb",s.money="money",s.numeric="numeric",s.oid="oid",s.reltime="reltime",s.text="text",s.time="time",s.timestamp="timestamp",s.timestamptz="timestamptz",s.timetz="timetz",s.tsrange="tsrange",s.tstzrange="tstzrange"})(Xe||(Xe={}));const Tg=(s,a,r={})=>{var l;const o=(l=r.skipTypes)!==null&&l!==void 0?l:[];return Object.keys(a).reduce((u,f)=>(u[f]=zS(f,s,a,o),u),{})},zS=(s,a,r,l)=>{const o=a.find(m=>m.name===s),u=o==null?void 0:o.type,f=r[s];return u&&!l.includes(u)?Pv(u,f):kd(f)},Pv=(s,a)=>{if(s.charAt(0)==="_"){const r=s.slice(1,s.length);return qS(a,r)}switch(s){case Xe.bool:return LS(a);case Xe.float4:case Xe.float8:case Xe.int2:case Xe.int4:case Xe.int8:case Xe.numeric:case Xe.oid:return PS(a);case Xe.json:case Xe.jsonb:return BS(a);case Xe.timestamp:return HS(a);case Xe.abstime:case Xe.date:case Xe.daterange:case Xe.int4range:case Xe.int8range:case Xe.money:case Xe.reltime:case Xe.text:case Xe.time:case Xe.timestamptz:case Xe.timetz:case Xe.tsrange:case Xe.tstzrange:return kd(a);default:return kd(a)}},kd=s=>s,LS=s=>{switch(s){case"t":return!0;case"f":return!1;default:return s}},PS=s=>{if(typeof s=="string"){const a=parseFloat(s);if(!Number.isNaN(a))return a}return s},BS=s=>{if(typeof s=="string")try{return JSON.parse(s)}catch(a){return console.log(`JSON parse error: ${a}`),s}return s},qS=(s,a)=>{if(typeof s!="string")return s;const r=s.length-1,l=s[r];if(s[0]==="{"&&l==="}"){let u;const f=s.slice(1,r);try{u=JSON.parse("["+f+"]")}catch(m){u=f?f.split(","):[]}return u.map(m=>Pv(a,m))}return s},HS=s=>typeof s=="string"?s.replace(" ","T"):s,Bv=s=>{let a=s;return a=a.replace(/^ws/i,"http"),a=a.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),a.replace(/\/+$/,"")};class ad{constructor(a,r,l={},o=zv){this.channel=a,this.event=r,this.payload=l,this.timeout=o,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(a){this.timeout=a,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(a){this.payload=Object.assign(Object.assign({},this.payload),a)}receive(a,r){var l;return this._hasReceived(a)&&r((l=this.receivedResp)===null||l===void 0?void 0:l.response),this.recHooks.push({status:a,callback:r}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const a=r=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=r,this._matchReceive(r)};this.channel._on(this.refEvent,{},a),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(a,r){this.refEvent&&this.channel._trigger(this.refEvent,{status:a,response:r})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:a,response:r}){this.recHooks.filter(l=>l.status===a).forEach(l=>l.callback(r))}_hasReceived(a){return this.receivedResp&&this.receivedResp.status===a}}var Cg;(function(s){s.SYNC="sync",s.JOIN="join",s.LEAVE="leave"})(Cg||(Cg={}));class gr{constructor(a,r){this.channel=a,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const l=(r==null?void 0:r.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(l.state,{},o=>{const{onJoin:u,onLeave:f,onSync:m}=this.caller;this.joinRef=this.channel._joinRef(),this.state=gr.syncState(this.state,o,u,f),this.pendingDiffs.forEach(g=>{this.state=gr.syncDiff(this.state,g,u,f)}),this.pendingDiffs=[],m()}),this.channel._on(l.diff,{},o=>{const{onJoin:u,onLeave:f,onSync:m}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(o):(this.state=gr.syncDiff(this.state,o,u,f),m())}),this.onJoin((o,u,f)=>{this.channel._trigger("presence",{event:"join",key:o,currentPresences:u,newPresences:f})}),this.onLeave((o,u,f)=>{this.channel._trigger("presence",{event:"leave",key:o,currentPresences:u,leftPresences:f})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(a,r,l,o){const u=this.cloneDeep(a),f=this.transformState(r),m={},g={};return this.map(u,(v,y)=>{f[v]||(g[v]=y)}),this.map(f,(v,y)=>{const S=u[v];if(S){const x=y.map(V=>V.presence_ref),C=S.map(V=>V.presence_ref),U=y.filter(V=>C.indexOf(V.presence_ref)<0),Y=S.filter(V=>x.indexOf(V.presence_ref)<0);U.length>0&&(m[v]=U),Y.length>0&&(g[v]=Y)}else m[v]=y}),this.syncDiff(u,{joins:m,leaves:g},l,o)}static syncDiff(a,r,l,o){const{joins:u,leaves:f}={joins:this.transformState(r.joins),leaves:this.transformState(r.leaves)};return l||(l=()=>{}),o||(o=()=>{}),this.map(u,(m,g)=>{var v;const y=(v=a[m])!==null&&v!==void 0?v:[];if(a[m]=this.cloneDeep(g),y.length>0){const S=a[m].map(C=>C.presence_ref),x=y.filter(C=>S.indexOf(C.presence_ref)<0);a[m].unshift(...x)}l(m,y,g)}),this.map(f,(m,g)=>{let v=a[m];if(!v)return;const y=g.map(S=>S.presence_ref);v=v.filter(S=>y.indexOf(S.presence_ref)<0),a[m]=v,o(m,v,g),v.length===0&&delete a[m]}),a}static map(a,r){return Object.getOwnPropertyNames(a).map(l=>r(l,a[l]))}static transformState(a){return a=this.cloneDeep(a),Object.getOwnPropertyNames(a).reduce((r,l)=>{const o=a[l];return"metas"in o?r[l]=o.metas.map(u=>(u.presence_ref=u.phx_ref,delete u.phx_ref,delete u.phx_ref_prev,u)):r[l]=o,r},{})}static cloneDeep(a){return JSON.parse(JSON.stringify(a))}onJoin(a){this.caller.onJoin=a}onLeave(a){this.caller.onLeave=a}onSync(a){this.caller.onSync=a}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var Ag;(function(s){s.ALL="*",s.INSERT="INSERT",s.UPDATE="UPDATE",s.DELETE="DELETE"})(Ag||(Ag={}));var Og;(function(s){s.BROADCAST="broadcast",s.PRESENCE="presence",s.POSTGRES_CHANGES="postgres_changes",s.SYSTEM="system"})(Og||(Og={}));var Jn;(function(s){s.SUBSCRIBED="SUBSCRIBED",s.TIMED_OUT="TIMED_OUT",s.CLOSED="CLOSED",s.CHANNEL_ERROR="CHANNEL_ERROR"})(Jn||(Jn={}));class Hd{constructor(a,r={config:{}},l){this.topic=a,this.params=r,this.socket=l,this.bindings={},this.state=Dt.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=a.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},r.config),this.timeout=this.socket.timeout,this.joinPush=new ad(this,En.join,this.params,this.timeout),this.rejoinTimer=new Lv(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=Dt.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(o=>o.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=Dt.closed,this.socket._remove(this)}),this._onError(o=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,o),this.state=Dt.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=Dt.errored,this.rejoinTimer.scheduleTimeout())}),this._on(En.reply,{},(o,u)=>{this._trigger(this._replyEventName(u),o)}),this.presence=new gr(this),this.broadcastEndpointURL=Bv(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(a,r=this.timeout){var l,o;if(this.socket.isConnected()||this.socket.connect(),this.state==Dt.closed){const{config:{broadcast:u,presence:f,private:m}}=this.params;this._onError(y=>a==null?void 0:a(Jn.CHANNEL_ERROR,y)),this._onClose(()=>a==null?void 0:a(Jn.CLOSED));const g={},v={broadcast:u,presence:f,postgres_changes:(o=(l=this.bindings.postgres_changes)===null||l===void 0?void 0:l.map(y=>y.filter))!==null&&o!==void 0?o:[],private:m};this.socket.accessTokenValue&&(g.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:v},g)),this.joinedOnce=!0,this._rejoin(r),this.joinPush.receive("ok",S=>E(this,[S],function*({postgres_changes:y}){var x;if(this.socket.setAuth(),y===void 0){a==null||a(Jn.SUBSCRIBED);return}else{const C=this.bindings.postgres_changes,U=(x=C==null?void 0:C.length)!==null&&x!==void 0?x:0,Y=[];for(let V=0;V<U;V++){const F=C[V],{filter:{event:J,schema:q,table:te,filter:K}}=F,W=y&&y[V];if(W&&W.event===J&&W.schema===q&&W.table===te&&W.filter===K)Y.push(Object.assign(Object.assign({},F),{id:W.id}));else{this.unsubscribe(),this.state=Dt.errored,a==null||a(Jn.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=Y,a&&a(Jn.SUBSCRIBED);return}})).receive("error",y=>{this.state=Dt.errored,a==null||a(Jn.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(y).join(", ")||"error")))}).receive("timeout",()=>{a==null||a(Jn.TIMED_OUT)})}return this}presenceState(){return this.presence.state}track(l){return E(this,arguments,function*(a,r={}){return yield this.send({type:"presence",event:"track",payload:a},r.timeout||this.timeout)})}untrack(){return E(this,arguments,function*(a={}){return yield this.send({type:"presence",event:"untrack"},a)})}on(a,r,l){return this._on(a,r,l)}send(l){return E(this,arguments,function*(a,r={}){var o,u;if(!this._canPush()&&a.type==="broadcast"){const{event:f,payload:m}=a,v={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:f,payload:m,private:this.private}]})};try{const y=yield this._fetchWithTimeout(this.broadcastEndpointURL,v,(o=r.timeout)!==null&&o!==void 0?o:this.timeout);return yield(u=y.body)===null||u===void 0?void 0:u.cancel(),y.ok?"ok":"error"}catch(y){return y.name==="AbortError"?"timed out":"error"}}else return new Promise(f=>{var m,g,v;const y=this._push(a.type,a,r.timeout||this.timeout);a.type==="broadcast"&&!(!((v=(g=(m=this.params)===null||m===void 0?void 0:m.config)===null||g===void 0?void 0:g.broadcast)===null||v===void 0)&&v.ack)&&f("ok"),y.receive("ok",()=>f("ok")),y.receive("error",()=>f("error")),y.receive("timeout",()=>f("timed out"))})})}updateJoinPayload(a){this.joinPush.updatePayload(a)}unsubscribe(a=this.timeout){this.state=Dt.leaving;const r=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(En.close,"leave",this._joinRef())};this.joinPush.destroy();let l=null;return new Promise(o=>{l=new ad(this,En.leave,{},a),l.receive("ok",()=>{r(),o("ok")}).receive("timeout",()=>{r(),o("timed out")}).receive("error",()=>{o("error")}),l.send(),this._canPush()||l.trigger("ok",{})}).finally(()=>{l==null||l.destroy()})}teardown(){this.pushBuffer.forEach(a=>a.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}_fetchWithTimeout(a,r,l){return E(this,null,function*(){const o=new AbortController,u=setTimeout(()=>o.abort(),l),f=yield this.socket.fetch(a,Object.assign(Object.assign({},r),{signal:o.signal}));return clearTimeout(u),f})}_push(a,r,l=this.timeout){if(!this.joinedOnce)throw`tried to push '${a}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let o=new ad(this,a,r,l);return this._canPush()?o.send():(o.startTimeout(),this.pushBuffer.push(o)),o}_onMessage(a,r,l){return r}_isMember(a){return this.topic===a}_joinRef(){return this.joinPush.ref}_trigger(a,r,l){var o,u;const f=a.toLocaleLowerCase(),{close:m,error:g,leave:v,join:y}=En;if(l&&[m,g,v,y].indexOf(f)>=0&&l!==this._joinRef())return;let x=this._onMessage(f,r,l);if(r&&!x)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(f)?(o=this.bindings.postgres_changes)===null||o===void 0||o.filter(C=>{var U,Y,V;return((U=C.filter)===null||U===void 0?void 0:U.event)==="*"||((V=(Y=C.filter)===null||Y===void 0?void 0:Y.event)===null||V===void 0?void 0:V.toLocaleLowerCase())===f}).map(C=>C.callback(x,l)):(u=this.bindings[f])===null||u===void 0||u.filter(C=>{var U,Y,V,F,J,q;if(["broadcast","presence","postgres_changes"].includes(f))if("id"in C){const te=C.id,K=(U=C.filter)===null||U===void 0?void 0:U.event;return te&&((Y=r.ids)===null||Y===void 0?void 0:Y.includes(te))&&(K==="*"||(K==null?void 0:K.toLocaleLowerCase())===((V=r.data)===null||V===void 0?void 0:V.type.toLocaleLowerCase()))}else{const te=(J=(F=C==null?void 0:C.filter)===null||F===void 0?void 0:F.event)===null||J===void 0?void 0:J.toLocaleLowerCase();return te==="*"||te===((q=r==null?void 0:r.event)===null||q===void 0?void 0:q.toLocaleLowerCase())}else return C.type.toLocaleLowerCase()===f}).map(C=>{if(typeof x=="object"&&"ids"in x){const U=x.data,{schema:Y,table:V,commit_timestamp:F,type:J,errors:q}=U;x=Object.assign(Object.assign({},{schema:Y,table:V,commit_timestamp:F,eventType:J,new:{},old:{},errors:q}),this._getPayloadRecords(U))}C.callback(x,l)})}_isClosed(){return this.state===Dt.closed}_isJoined(){return this.state===Dt.joined}_isJoining(){return this.state===Dt.joining}_isLeaving(){return this.state===Dt.leaving}_replyEventName(a){return`chan_reply_${a}`}_on(a,r,l){const o=a.toLocaleLowerCase(),u={type:o,filter:r,callback:l};return this.bindings[o]?this.bindings[o].push(u):this.bindings[o]=[u],this}_off(a,r){const l=a.toLocaleLowerCase();return this.bindings[l]=this.bindings[l].filter(o=>{var u;return!(((u=o.type)===null||u===void 0?void 0:u.toLocaleLowerCase())===l&&Hd.isEqual(o.filter,r))}),this}static isEqual(a,r){if(Object.keys(a).length!==Object.keys(r).length)return!1;for(const l in a)if(a[l]!==r[l])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(a){this._on(En.close,{},a)}_onError(a){this._on(En.error,{},r=>a(r))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(a=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=Dt.joining,this.joinPush.resend(a))}_getPayloadRecords(a){const r={new:{},old:{}};return(a.type==="INSERT"||a.type==="UPDATE")&&(r.new=Tg(a.columns,a.record)),(a.type==="UPDATE"||a.type==="DELETE")&&(r.old=Tg(a.columns,a.old_record)),r}}const Ng=()=>{},IS=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class $S{constructor(a,r){var l;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=zv,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Ng,this.ref=0,this.logger=Ng,this.conn=null,this.sendBuffer=[],this.serializer=new DS,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=u=>{let f;return u?f=u:typeof fetch=="undefined"?f=(...m)=>xr(()=>E(this,null,function*(){const{default:g}=yield Promise.resolve().then(()=>cs);return{default:g}}),void 0).then(({default:g})=>g(...m)):f=fetch,(...m)=>f(...m)},this.endPoint=`${a}/${xd.websocket}`,this.httpEndpoint=Bv(a),r!=null&&r.transport?this.transport=r.transport:this.transport=null,r!=null&&r.params&&(this.params=r.params),r!=null&&r.timeout&&(this.timeout=r.timeout),r!=null&&r.logger&&(this.logger=r.logger),(r!=null&&r.logLevel||r!=null&&r.log_level)&&(this.logLevel=r.logLevel||r.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),r!=null&&r.heartbeatIntervalMs&&(this.heartbeatIntervalMs=r.heartbeatIntervalMs);const o=(l=r==null?void 0:r.params)===null||l===void 0?void 0:l.apikey;if(o&&(this.accessTokenValue=o,this.apiKey=o),this.reconnectAfterMs=r!=null&&r.reconnectAfterMs?r.reconnectAfterMs:u=>[1e3,2e3,5e3,1e4][u-1]||1e4,this.encode=r!=null&&r.encode?r.encode:(u,f)=>f(JSON.stringify(u)),this.decode=r!=null&&r.decode?r.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Lv(()=>E(this,null,function*(){this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(r==null?void 0:r.fetch),r!=null&&r.worker){if(typeof window!="undefined"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(r==null?void 0:r.worker)||!1,this.workerUrl=r==null?void 0:r.workerUrl}this.accessToken=(r==null?void 0:r.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=OS),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:MS}))}disconnect(a,r){this.conn&&(this.conn.onclose=function(){},a?this.conn.close(a,r!=null?r:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(l=>l.teardown()))}getChannels(){return this.channels}removeChannel(a){return E(this,null,function*(){const r=yield a.unsubscribe();return this.channels.length===0&&this.disconnect(),r})}removeAllChannels(){return E(this,null,function*(){const a=yield Promise.all(this.channels.map(r=>r.unsubscribe()));return this.channels=[],this.disconnect(),a})}log(a,r,l){this.logger(a,r,l)}connectionState(){switch(this.conn&&this.conn.readyState){case pr.connecting:return ri.Connecting;case pr.open:return ri.Open;case pr.closing:return ri.Closing;default:return ri.Closed}}isConnected(){return this.connectionState()===ri.Open}channel(a,r={config:{}}){const l=`realtime:${a}`,o=this.getChannels().find(u=>u.topic===l);if(o)return o;{const u=new Hd(`realtime:${a}`,r,this);return this.channels.push(u),u}}push(a){const{topic:r,event:l,payload:o,ref:u}=a,f=()=>{this.encode(a,m=>{var g;(g=this.conn)===null||g===void 0||g.send(m)})};this.log("push",`${r} ${l} (${u})`,o),this.isConnected()?f():this.sendBuffer.push(f)}setAuth(a=null){return E(this,null,function*(){let r=a||this.accessToken&&(yield this.accessToken())||this.accessTokenValue;this.accessTokenValue!=r&&(this.accessTokenValue=r,this.channels.forEach(l=>{const o={access_token:r,version:RS};r&&l.updateJoinPayload(o),l.joinedOnce&&l._isJoined()&&l._push(En.access_token,{access_token:r})}))})}sendHeartbeat(){return E(this,null,function*(){var a;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(a=this.conn)===null||a===void 0||a.close(US,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),yield this.setAuth()})}onHeartbeat(a){this.heartbeatCallback=a}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(a=>a()),this.sendBuffer=[])}_makeRef(){let a=this.ref+1;return a===this.ref?this.ref=0:this.ref=a,this.ref.toString()}_leaveOpenTopic(a){let r=this.channels.find(l=>l.topic===a&&(l._isJoined()||l._isJoining()));r&&(this.log("transport",`leaving duplicate topic "${a}"`),r.unsubscribe())}_remove(a){this.channels=this.channels.filter(r=>r.topic!==a.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=a=>this._onConnError(a),this.conn.onmessage=a=>this._onConnMessage(a),this.conn.onclose=a=>this._onConnClose(a))}_onConnMessage(a){this.decode(a.data,r=>{let{topic:l,event:o,payload:u,ref:f}=r;l==="phoenix"&&o==="phx_reply"&&this.heartbeatCallback(r.payload.status=="ok"?"ok":"error"),f&&f===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${u.status||""} ${l} ${o} ${f&&"("+f+")"||""}`,u),Array.from(this.channels).filter(m=>m._isMember(l)).forEach(m=>m._trigger(o,u,f)),this.stateChangeCallbacks.message.forEach(m=>m(r))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(a=>a())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const a=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(a),this.workerRef.onerror=r=>{this.log("worker","worker error",r.message),this.workerRef.terminate()},this.workerRef.onmessage=r=>{r.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(a){this.log("transport","close",a),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(r=>r(a))}_onConnError(a){this.log("transport",`${a}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(r=>r(a))}_triggerChanError(){this.channels.forEach(a=>a._trigger(En.error))}_appendParams(a,r){if(Object.keys(r).length===0)return a;const l=a.match(/\?/)?"&":"?",o=new URLSearchParams(r);return`${a}${l}${o}`}_workerObjectUrl(a){let r;if(a)r=a;else{const l=new Blob([IS],{type:"application/javascript"});r=URL.createObjectURL(l)}return r}}class Id extends Error{constructor(a){super(a),this.__isStorageError=!0,this.name="StorageError"}}function Tt(s){return typeof s=="object"&&s!==null&&"__isStorageError"in s}class GS extends Id{constructor(a,r){super(a),this.name="StorageApiError",this.status=r}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Td extends Id{constructor(a,r){super(a),this.name="StorageUnknownError",this.originalError=r}}var VS=function(s,a,r,l){function o(u){return u instanceof r?u:new r(function(f){f(u)})}return new(r||(r=Promise))(function(u,f){function m(y){try{v(l.next(y))}catch(S){f(S)}}function g(y){try{v(l.throw(y))}catch(S){f(S)}}function v(y){y.done?u(y.value):o(y.value).then(m,g)}v((l=l.apply(s,a||[])).next())})};const qv=s=>{let a;return s?a=s:typeof fetch=="undefined"?a=(...r)=>xr(()=>E(null,null,function*(){const{default:l}=yield Promise.resolve().then(()=>cs);return{default:l}}),void 0).then(({default:l})=>l(...r)):a=fetch,(...r)=>a(...r)},YS=()=>VS(void 0,void 0,void 0,function*(){return typeof Response=="undefined"?(yield xr(()=>Promise.resolve().then(()=>cs),void 0)).Response:Response}),Cd=s=>{if(Array.isArray(s))return s.map(r=>Cd(r));if(typeof s=="function"||s!==Object(s))return s;const a={};return Object.entries(s).forEach(([r,l])=>{const o=r.replace(/([-_][a-z])/gi,u=>u.toUpperCase().replace(/[-_]/g,""));a[o]=Cd(l)}),a};var oi=function(s,a,r,l){function o(u){return u instanceof r?u:new r(function(f){f(u)})}return new(r||(r=Promise))(function(u,f){function m(y){try{v(l.next(y))}catch(S){f(S)}}function g(y){try{v(l.throw(y))}catch(S){f(S)}}function v(y){y.done?u(y.value):o(y.value).then(m,g)}v((l=l.apply(s,a||[])).next())})};const id=s=>s.msg||s.message||s.error_description||s.error||JSON.stringify(s),FS=(s,a,r)=>oi(void 0,void 0,void 0,function*(){const l=yield YS();s instanceof l&&!(r!=null&&r.noResolveJson)?s.json().then(o=>{a(new GS(id(o),s.status||500))}).catch(o=>{a(new Td(id(o),o))}):a(new Td(id(s),s))}),KS=(s,a,r,l)=>{const o={method:s,headers:(a==null?void 0:a.headers)||{}};return s==="GET"?o:(o.headers=Object.assign({"Content-Type":"application/json"},a==null?void 0:a.headers),l&&(o.body=JSON.stringify(l)),Object.assign(Object.assign({},o),r))};function kr(s,a,r,l,o,u){return oi(this,void 0,void 0,function*(){return new Promise((f,m)=>{s(r,KS(a,l,o,u)).then(g=>{if(!g.ok)throw g;return l!=null&&l.noResolveJson?g:g.json()}).then(g=>f(g)).catch(g=>FS(g,m,l))})})}function co(s,a,r,l){return oi(this,void 0,void 0,function*(){return kr(s,"GET",a,r,l)})}function Na(s,a,r,l,o){return oi(this,void 0,void 0,function*(){return kr(s,"POST",a,l,o,r)})}function XS(s,a,r,l,o){return oi(this,void 0,void 0,function*(){return kr(s,"PUT",a,l,o,r)})}function QS(s,a,r,l){return oi(this,void 0,void 0,function*(){return kr(s,"HEAD",a,Object.assign(Object.assign({},r),{noResolveJson:!0}),l)})}function Hv(s,a,r,l,o){return oi(this,void 0,void 0,function*(){return kr(s,"DELETE",a,l,o,r)})}var Yt=function(s,a,r,l){function o(u){return u instanceof r?u:new r(function(f){f(u)})}return new(r||(r=Promise))(function(u,f){function m(y){try{v(l.next(y))}catch(S){f(S)}}function g(y){try{v(l.throw(y))}catch(S){f(S)}}function v(y){y.done?u(y.value):o(y.value).then(m,g)}v((l=l.apply(s,a||[])).next())})};const ZS={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Rg={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class JS{constructor(a,r={},l,o){this.url=a,this.headers=r,this.bucketId=l,this.fetch=qv(o)}uploadOrUpdate(a,r,l,o){return Yt(this,void 0,void 0,function*(){try{let u;const f=Object.assign(Object.assign({},Rg),o);let m=Object.assign(Object.assign({},this.headers),a==="POST"&&{"x-upsert":String(f.upsert)});const g=f.metadata;typeof Blob!="undefined"&&l instanceof Blob?(u=new FormData,u.append("cacheControl",f.cacheControl),g&&u.append("metadata",this.encodeMetadata(g)),u.append("",l)):typeof FormData!="undefined"&&l instanceof FormData?(u=l,u.append("cacheControl",f.cacheControl),g&&u.append("metadata",this.encodeMetadata(g))):(u=l,m["cache-control"]=`max-age=${f.cacheControl}`,m["content-type"]=f.contentType,g&&(m["x-metadata"]=this.toBase64(this.encodeMetadata(g)))),o!=null&&o.headers&&(m=Object.assign(Object.assign({},m),o.headers));const v=this._removeEmptyFolders(r),y=this._getFinalPath(v),S=yield this.fetch(`${this.url}/object/${y}`,Object.assign({method:a,body:u,headers:m},f!=null&&f.duplex?{duplex:f.duplex}:{})),x=yield S.json();return S.ok?{data:{path:v,id:x.Id,fullPath:x.Key},error:null}:{data:null,error:x}}catch(u){if(Tt(u))return{data:null,error:u};throw u}})}upload(a,r,l){return Yt(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",a,r,l)})}uploadToSignedUrl(a,r,l,o){return Yt(this,void 0,void 0,function*(){const u=this._removeEmptyFolders(a),f=this._getFinalPath(u),m=new URL(this.url+`/object/upload/sign/${f}`);m.searchParams.set("token",r);try{let g;const v=Object.assign({upsert:Rg.upsert},o),y=Object.assign(Object.assign({},this.headers),{"x-upsert":String(v.upsert)});typeof Blob!="undefined"&&l instanceof Blob?(g=new FormData,g.append("cacheControl",v.cacheControl),g.append("",l)):typeof FormData!="undefined"&&l instanceof FormData?(g=l,g.append("cacheControl",v.cacheControl)):(g=l,y["cache-control"]=`max-age=${v.cacheControl}`,y["content-type"]=v.contentType);const S=yield this.fetch(m.toString(),{method:"PUT",body:g,headers:y}),x=yield S.json();return S.ok?{data:{path:u,fullPath:x.Key},error:null}:{data:null,error:x}}catch(g){if(Tt(g))return{data:null,error:g};throw g}})}createSignedUploadUrl(a,r){return Yt(this,void 0,void 0,function*(){try{let l=this._getFinalPath(a);const o=Object.assign({},this.headers);r!=null&&r.upsert&&(o["x-upsert"]="true");const u=yield Na(this.fetch,`${this.url}/object/upload/sign/${l}`,{},{headers:o}),f=new URL(this.url+u.url),m=f.searchParams.get("token");if(!m)throw new Id("No token returned by API");return{data:{signedUrl:f.toString(),path:a,token:m},error:null}}catch(l){if(Tt(l))return{data:null,error:l};throw l}})}update(a,r,l){return Yt(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",a,r,l)})}move(a,r,l){return Yt(this,void 0,void 0,function*(){try{return{data:yield Na(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:a,destinationKey:r,destinationBucket:l==null?void 0:l.destinationBucket},{headers:this.headers}),error:null}}catch(o){if(Tt(o))return{data:null,error:o};throw o}})}copy(a,r,l){return Yt(this,void 0,void 0,function*(){try{return{data:{path:(yield Na(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:a,destinationKey:r,destinationBucket:l==null?void 0:l.destinationBucket},{headers:this.headers})).Key},error:null}}catch(o){if(Tt(o))return{data:null,error:o};throw o}})}createSignedUrl(a,r,l){return Yt(this,void 0,void 0,function*(){try{let o=this._getFinalPath(a),u=yield Na(this.fetch,`${this.url}/object/sign/${o}`,Object.assign({expiresIn:r},l!=null&&l.transform?{transform:l.transform}:{}),{headers:this.headers});const f=l!=null&&l.download?`&download=${l.download===!0?"":l.download}`:"";return u={signedUrl:encodeURI(`${this.url}${u.signedURL}${f}`)},{data:u,error:null}}catch(o){if(Tt(o))return{data:null,error:o};throw o}})}createSignedUrls(a,r,l){return Yt(this,void 0,void 0,function*(){try{const o=yield Na(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:r,paths:a},{headers:this.headers}),u=l!=null&&l.download?`&download=${l.download===!0?"":l.download}`:"";return{data:o.map(f=>Object.assign(Object.assign({},f),{signedUrl:f.signedURL?encodeURI(`${this.url}${f.signedURL}${u}`):null})),error:null}}catch(o){if(Tt(o))return{data:null,error:o};throw o}})}download(a,r){return Yt(this,void 0,void 0,function*(){const o=typeof(r==null?void 0:r.transform)!="undefined"?"render/image/authenticated":"object",u=this.transformOptsToQueryString((r==null?void 0:r.transform)||{}),f=u?`?${u}`:"";try{const m=this._getFinalPath(a);return{data:yield(yield co(this.fetch,`${this.url}/${o}/${m}${f}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(m){if(Tt(m))return{data:null,error:m};throw m}})}info(a){return Yt(this,void 0,void 0,function*(){const r=this._getFinalPath(a);try{const l=yield co(this.fetch,`${this.url}/object/info/${r}`,{headers:this.headers});return{data:Cd(l),error:null}}catch(l){if(Tt(l))return{data:null,error:l};throw l}})}exists(a){return Yt(this,void 0,void 0,function*(){const r=this._getFinalPath(a);try{return yield QS(this.fetch,`${this.url}/object/${r}`,{headers:this.headers}),{data:!0,error:null}}catch(l){if(Tt(l)&&l instanceof Td){const o=l.originalError;if([400,404].includes(o==null?void 0:o.status))return{data:!1,error:l}}throw l}})}getPublicUrl(a,r){const l=this._getFinalPath(a),o=[],u=r!=null&&r.download?`download=${r.download===!0?"":r.download}`:"";u!==""&&o.push(u);const m=typeof(r==null?void 0:r.transform)!="undefined"?"render/image":"object",g=this.transformOptsToQueryString((r==null?void 0:r.transform)||{});g!==""&&o.push(g);let v=o.join("&");return v!==""&&(v=`?${v}`),{data:{publicUrl:encodeURI(`${this.url}/${m}/public/${l}${v}`)}}}remove(a){return Yt(this,void 0,void 0,function*(){try{return{data:yield Hv(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:a},{headers:this.headers}),error:null}}catch(r){if(Tt(r))return{data:null,error:r};throw r}})}list(a,r,l){return Yt(this,void 0,void 0,function*(){try{const o=Object.assign(Object.assign(Object.assign({},ZS),r),{prefix:a||""});return{data:yield Na(this.fetch,`${this.url}/object/list/${this.bucketId}`,o,{headers:this.headers},l),error:null}}catch(o){if(Tt(o))return{data:null,error:o};throw o}})}encodeMetadata(a){return JSON.stringify(a)}toBase64(a){return typeof Buffer!="undefined"?Buffer.from(a).toString("base64"):btoa(a)}_getFinalPath(a){return`${this.bucketId}/${a}`}_removeEmptyFolders(a){return a.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(a){const r=[];return a.width&&r.push(`width=${a.width}`),a.height&&r.push(`height=${a.height}`),a.resize&&r.push(`resize=${a.resize}`),a.format&&r.push(`format=${a.format}`),a.quality&&r.push(`quality=${a.quality}`),r.join("&")}}const WS="2.7.1",ew={"X-Client-Info":`storage-js/${WS}`};var Wi=function(s,a,r,l){function o(u){return u instanceof r?u:new r(function(f){f(u)})}return new(r||(r=Promise))(function(u,f){function m(y){try{v(l.next(y))}catch(S){f(S)}}function g(y){try{v(l.throw(y))}catch(S){f(S)}}function v(y){y.done?u(y.value):o(y.value).then(m,g)}v((l=l.apply(s,a||[])).next())})};class tw{constructor(a,r={},l){this.url=a,this.headers=Object.assign(Object.assign({},ew),r),this.fetch=qv(l)}listBuckets(){return Wi(this,void 0,void 0,function*(){try{return{data:yield co(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(a){if(Tt(a))return{data:null,error:a};throw a}})}getBucket(a){return Wi(this,void 0,void 0,function*(){try{return{data:yield co(this.fetch,`${this.url}/bucket/${a}`,{headers:this.headers}),error:null}}catch(r){if(Tt(r))return{data:null,error:r};throw r}})}createBucket(a,r={public:!1}){return Wi(this,void 0,void 0,function*(){try{return{data:yield Na(this.fetch,`${this.url}/bucket`,{id:a,name:a,public:r.public,file_size_limit:r.fileSizeLimit,allowed_mime_types:r.allowedMimeTypes},{headers:this.headers}),error:null}}catch(l){if(Tt(l))return{data:null,error:l};throw l}})}updateBucket(a,r){return Wi(this,void 0,void 0,function*(){try{return{data:yield XS(this.fetch,`${this.url}/bucket/${a}`,{id:a,name:a,public:r.public,file_size_limit:r.fileSizeLimit,allowed_mime_types:r.allowedMimeTypes},{headers:this.headers}),error:null}}catch(l){if(Tt(l))return{data:null,error:l};throw l}})}emptyBucket(a){return Wi(this,void 0,void 0,function*(){try{return{data:yield Na(this.fetch,`${this.url}/bucket/${a}/empty`,{},{headers:this.headers}),error:null}}catch(r){if(Tt(r))return{data:null,error:r};throw r}})}deleteBucket(a){return Wi(this,void 0,void 0,function*(){try{return{data:yield Hv(this.fetch,`${this.url}/bucket/${a}`,{},{headers:this.headers}),error:null}}catch(r){if(Tt(r))return{data:null,error:r};throw r}})}}class nw extends tw{constructor(a,r={},l){super(a,r,l)}from(a){return new JS(this.url,this.headers,a,this.fetch)}}const aw="2.50.2";let dr="";typeof Deno!="undefined"?dr="deno":typeof document!="undefined"?dr="web":typeof navigator!="undefined"&&navigator.product==="ReactNative"?dr="react-native":dr="node";const iw={"X-Client-Info":`supabase-js-${dr}/${aw}`},sw={headers:iw},rw={schema:"public"},lw={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},ow={};var cw=function(s,a,r,l){function o(u){return u instanceof r?u:new r(function(f){f(u)})}return new(r||(r=Promise))(function(u,f){function m(y){try{v(l.next(y))}catch(S){f(S)}}function g(y){try{v(l.throw(y))}catch(S){f(S)}}function v(y){y.done?u(y.value):o(y.value).then(m,g)}v((l=l.apply(s,a||[])).next())})};const uw=s=>{let a;return s?a=s:typeof fetch=="undefined"?a=Ov:a=fetch,(...r)=>a(...r)},dw=()=>typeof Headers=="undefined"?Nv:Headers,fw=(s,a,r)=>{const l=uw(r),o=dw();return(u,f)=>cw(void 0,void 0,void 0,function*(){var m;const g=(m=yield a())!==null&&m!==void 0?m:s;let v=new o(f==null?void 0:f.headers);return v.has("apikey")||v.set("apikey",s),v.has("Authorization")||v.set("Authorization",`Bearer ${g}`),l(u,Object.assign(Object.assign({},f),{headers:v}))})};var hw=function(s,a,r,l){function o(u){return u instanceof r?u:new r(function(f){f(u)})}return new(r||(r=Promise))(function(u,f){function m(y){try{v(l.next(y))}catch(S){f(S)}}function g(y){try{v(l.throw(y))}catch(S){f(S)}}function v(y){y.done?u(y.value):o(y.value).then(m,g)}v((l=l.apply(s,a||[])).next())})};function mw(s){return s.endsWith("/")?s:s+"/"}function pw(s,a){var r,l;const{db:o,auth:u,realtime:f,global:m}=s,{db:g,auth:v,realtime:y,global:S}=a,x={db:Object.assign(Object.assign({},g),o),auth:Object.assign(Object.assign({},v),u),realtime:Object.assign(Object.assign({},y),f),global:Object.assign(Object.assign(Object.assign({},S),m),{headers:Object.assign(Object.assign({},(r=S==null?void 0:S.headers)!==null&&r!==void 0?r:{}),(l=m==null?void 0:m.headers)!==null&&l!==void 0?l:{})}),accessToken:()=>hw(this,void 0,void 0,function*(){return""})};return s.accessToken?x.accessToken=s.accessToken:delete x.accessToken,x}const Iv="2.70.0",ss=30*1e3,Ad=3,sd=Ad*ss,gw="http://localhost:9999",vw="supabase.auth.token",yw={"X-Client-Info":`gotrue-js/${Iv}`},Od="X-Supabase-Api-Version",$v={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},bw=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,_w=6e5;class $d extends Error{constructor(a,r,l){super(a),this.__isAuthError=!0,this.name="AuthError",this.status=r,this.code=l}}function be(s){return typeof s=="object"&&s!==null&&"__isAuthError"in s}class Sw extends $d{constructor(a,r,l){super(a,r,l),this.name="AuthApiError",this.status=r,this.code=l}}function ww(s){return be(s)&&s.name==="AuthApiError"}class Gv extends $d{constructor(a,r){super(a),this.name="AuthUnknownError",this.originalError=r}}class Ua extends $d{constructor(a,r,l,o){super(a,l,o),this.name=r,this.status=l}}class Ca extends Ua{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function jw(s){return be(s)&&s.name==="AuthSessionMissingError"}class Jl extends Ua{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Wl extends Ua{constructor(a){super(a,"AuthInvalidCredentialsError",400,void 0)}}class eo extends Ua{constructor(a,r=null){super(a,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=r}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function Ew(s){return be(s)&&s.name==="AuthImplicitGrantRedirectError"}class Mg extends Ua{constructor(a,r=null){super(a,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=r}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Nd extends Ua{constructor(a,r){super(a,"AuthRetryableFetchError",r,void 0)}}function rd(s){return be(s)&&s.name==="AuthRetryableFetchError"}class Ug extends Ua{constructor(a,r,l){super(a,"AuthWeakPasswordError",r,"weak_password"),this.reasons=l}}class vr extends Ua{constructor(a){super(a,"AuthInvalidJwtError",400,"invalid_jwt")}}const uo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Dg=` 	
\r=`.split(""),xw=(()=>{const s=new Array(128);for(let a=0;a<s.length;a+=1)s[a]=-1;for(let a=0;a<Dg.length;a+=1)s[Dg[a].charCodeAt(0)]=-2;for(let a=0;a<uo.length;a+=1)s[uo[a].charCodeAt(0)]=a;return s})();function zg(s,a,r){if(s!==null)for(a.queue=a.queue<<8|s,a.queuedBits+=8;a.queuedBits>=6;){const l=a.queue>>a.queuedBits-6&63;r(uo[l]),a.queuedBits-=6}else if(a.queuedBits>0)for(a.queue=a.queue<<6-a.queuedBits,a.queuedBits=6;a.queuedBits>=6;){const l=a.queue>>a.queuedBits-6&63;r(uo[l]),a.queuedBits-=6}}function Vv(s,a,r){const l=xw[s];if(l>-1)for(a.queue=a.queue<<6|l,a.queuedBits+=6;a.queuedBits>=8;)r(a.queue>>a.queuedBits-8&255),a.queuedBits-=8;else{if(l===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(s)}"`)}}function Lg(s){const a=[],r=f=>{a.push(String.fromCodePoint(f))},l={utf8seq:0,codepoint:0},o={queue:0,queuedBits:0},u=f=>{Cw(f,l,r)};for(let f=0;f<s.length;f+=1)Vv(s.charCodeAt(f),o,u);return a.join("")}function kw(s,a){if(s<=127){a(s);return}else if(s<=2047){a(192|s>>6),a(128|s&63);return}else if(s<=65535){a(224|s>>12),a(128|s>>6&63),a(128|s&63);return}else if(s<=1114111){a(240|s>>18),a(128|s>>12&63),a(128|s>>6&63),a(128|s&63);return}throw new Error(`Unrecognized Unicode codepoint: ${s.toString(16)}`)}function Tw(s,a){for(let r=0;r<s.length;r+=1){let l=s.charCodeAt(r);if(l>55295&&l<=56319){const o=(l-55296)*1024&65535;l=(s.charCodeAt(r+1)-56320&65535|o)+65536,r+=1}kw(l,a)}}function Cw(s,a,r){if(a.utf8seq===0){if(s<=127){r(s);return}for(let l=1;l<6;l+=1)if((s>>7-l&1)===0){a.utf8seq=l;break}if(a.utf8seq===2)a.codepoint=s&31;else if(a.utf8seq===3)a.codepoint=s&15;else if(a.utf8seq===4)a.codepoint=s&7;else throw new Error("Invalid UTF-8 sequence");a.utf8seq-=1}else if(a.utf8seq>0){if(s<=127)throw new Error("Invalid UTF-8 sequence");a.codepoint=a.codepoint<<6|s&63,a.utf8seq-=1,a.utf8seq===0&&r(a.codepoint)}}function Aw(s){const a=[],r={queue:0,queuedBits:0},l=o=>{a.push(o)};for(let o=0;o<s.length;o+=1)Vv(s.charCodeAt(o),r,l);return new Uint8Array(a)}function Ow(s){const a=[];return Tw(s,r=>a.push(r)),new Uint8Array(a)}function Nw(s){const a=[],r={queue:0,queuedBits:0},l=o=>{a.push(o)};return s.forEach(o=>zg(o,r,l)),zg(null,r,l),a.join("")}function Rw(s){return Math.round(Date.now()/1e3)+s}function Mw(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const a=Math.random()*16|0;return(s=="x"?a:a&3|8).toString(16)})}const jn=()=>typeof window!="undefined"&&typeof document!="undefined",ai={tested:!1,writable:!1},yr=()=>{if(!jn())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch(a){return!1}if(ai.tested)return ai.writable;const s=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(s,s),globalThis.localStorage.removeItem(s),ai.tested=!0,ai.writable=!0}catch(a){ai.tested=!0,ai.writable=!1}return ai.writable};function Uw(s){const a={},r=new URL(s);if(r.hash&&r.hash[0]==="#")try{new URLSearchParams(r.hash.substring(1)).forEach((o,u)=>{a[u]=o})}catch(l){}return r.searchParams.forEach((l,o)=>{a[o]=l}),a}const Yv=s=>{let a;return s?a=s:typeof fetch=="undefined"?a=(...r)=>xr(()=>E(null,null,function*(){const{default:l}=yield Promise.resolve().then(()=>cs);return{default:l}}),void 0).then(({default:l})=>l(...r)):a=fetch,(...r)=>a(...r)},Dw=s=>typeof s=="object"&&s!==null&&"status"in s&&"ok"in s&&"json"in s&&typeof s.json=="function",Fv=(s,a,r)=>E(null,null,function*(){yield s.setItem(a,JSON.stringify(r))}),to=(s,a)=>E(null,null,function*(){const r=yield s.getItem(a);if(!r)return null;try{return JSON.parse(r)}catch(l){return r}}),no=(s,a)=>E(null,null,function*(){yield s.removeItem(a)});class _o{constructor(){this.promise=new _o.promiseConstructor((a,r)=>{this.resolve=a,this.reject=r})}}_o.promiseConstructor=Promise;function ld(s){const a=s.split(".");if(a.length!==3)throw new vr("Invalid JWT structure");for(let l=0;l<a.length;l++)if(!bw.test(a[l]))throw new vr("JWT not in base64url format");return{header:JSON.parse(Lg(a[0])),payload:JSON.parse(Lg(a[1])),signature:Aw(a[2]),raw:{header:a[0],payload:a[1]}}}function zw(s){return E(this,null,function*(){return yield new Promise(a=>{setTimeout(()=>a(null),s)})})}function Lw(s,a){return new Promise((l,o)=>{E(null,null,function*(){for(let u=0;u<1/0;u++)try{const f=yield s(u);if(!a(u,null,f)){l(f);return}}catch(f){if(!a(u,f)){o(f);return}}})})}function Pw(s){return("0"+s.toString(16)).substr(-2)}function Bw(){const a=new Uint32Array(56);if(typeof crypto=="undefined"){const r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",l=r.length;let o="";for(let u=0;u<56;u++)o+=r.charAt(Math.floor(Math.random()*l));return o}return crypto.getRandomValues(a),Array.from(a,Pw).join("")}function qw(s){return E(this,null,function*(){const r=new TextEncoder().encode(s),l=yield crypto.subtle.digest("SHA-256",r),o=new Uint8Array(l);return Array.from(o).map(u=>String.fromCharCode(u)).join("")})}function Hw(s){return E(this,null,function*(){if(!(typeof crypto!="undefined"&&typeof crypto.subtle!="undefined"&&typeof TextEncoder!="undefined"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),s;const r=yield qw(s);return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")})}function es(s,a,r=!1){return E(this,null,function*(){const l=Bw();let o=l;r&&(o+="/PASSWORD_RECOVERY"),yield Fv(s,`${a}-code-verifier`,o);const u=yield Hw(l);return[u,l===u?"plain":"s256"]})}const Iw=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function $w(s){const a=s.headers.get(Od);if(!a||!a.match(Iw))return null;try{return new Date(`${a}T00:00:00.0Z`)}catch(r){return null}}function Gw(s){if(!s)throw new Error("Missing exp claim");const a=Math.floor(Date.now()/1e3);if(s<=a)throw new Error("JWT has expired")}function Vw(s){switch(s){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const Yw=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function ts(s){if(!Yw.test(s))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var Fw=function(s,a){var r={};for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&a.indexOf(l)<0&&(r[l]=s[l]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,l=Object.getOwnPropertySymbols(s);o<l.length;o++)a.indexOf(l[o])<0&&Object.prototype.propertyIsEnumerable.call(s,l[o])&&(r[l[o]]=s[l[o]]);return r};const si=s=>s.msg||s.message||s.error_description||s.error||JSON.stringify(s),Kw=[502,503,504];function Pg(s){return E(this,null,function*(){var a;if(!Dw(s))throw new Nd(si(s),0);if(Kw.includes(s.status))throw new Nd(si(s),s.status);let r;try{r=yield s.json()}catch(u){throw new Gv(si(u),u)}let l;const o=$w(s);if(o&&o.getTime()>=$v["2024-01-01"].timestamp&&typeof r=="object"&&r&&typeof r.code=="string"?l=r.code:typeof r=="object"&&r&&typeof r.error_code=="string"&&(l=r.error_code),l){if(l==="weak_password")throw new Ug(si(r),s.status,((a=r.weak_password)===null||a===void 0?void 0:a.reasons)||[]);if(l==="session_not_found")throw new Ca}else if(typeof r=="object"&&r&&typeof r.weak_password=="object"&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((u,f)=>u&&typeof f=="string",!0))throw new Ug(si(r),s.status,r.weak_password.reasons);throw new Sw(si(r),s.status||500,l)})}const Xw=(s,a,r,l)=>{const o={method:s,headers:(a==null?void 0:a.headers)||{}};return s==="GET"?o:(o.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},a==null?void 0:a.headers),o.body=JSON.stringify(l),Object.assign(Object.assign({},o),r))};function Ce(s,a,r,l){return E(this,null,function*(){var o;const u=Object.assign({},l==null?void 0:l.headers);u[Od]||(u[Od]=$v["2024-01-01"].name),l!=null&&l.jwt&&(u.Authorization=`Bearer ${l.jwt}`);const f=(o=l==null?void 0:l.query)!==null&&o!==void 0?o:{};l!=null&&l.redirectTo&&(f.redirect_to=l.redirectTo);const m=Object.keys(f).length?"?"+new URLSearchParams(f).toString():"",g=yield Qw(s,a,r+m,{headers:u,noResolveJson:l==null?void 0:l.noResolveJson},{},l==null?void 0:l.body);return l!=null&&l.xform?l==null?void 0:l.xform(g):{data:Object.assign({},g),error:null}})}function Qw(s,a,r,l,o,u){return E(this,null,function*(){const f=Xw(a,l,o,u);let m;try{m=yield s(r,Object.assign({},f))}catch(g){throw console.error(g),new Nd(si(g),0)}if(m.ok||(yield Pg(m)),l!=null&&l.noResolveJson)return m;try{return yield m.json()}catch(g){yield Pg(g)}})}function Zn(s){var a;let r=null;e2(s)&&(r=Object.assign({},s),s.expires_at||(r.expires_at=Rw(s.expires_in)));const l=(a=s.user)!==null&&a!==void 0?a:s;return{data:{session:r,user:l},error:null}}function Bg(s){const a=Zn(s);return!a.error&&s.weak_password&&typeof s.weak_password=="object"&&Array.isArray(s.weak_password.reasons)&&s.weak_password.reasons.length&&s.weak_password.message&&typeof s.weak_password.message=="string"&&s.weak_password.reasons.reduce((r,l)=>r&&typeof l=="string",!0)&&(a.data.weak_password=s.weak_password),a}function Ra(s){var a;return{data:{user:(a=s.user)!==null&&a!==void 0?a:s},error:null}}function Zw(s){return{data:s,error:null}}function Jw(s){const{action_link:a,email_otp:r,hashed_token:l,redirect_to:o,verification_type:u}=s,f=Fw(s,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),m={action_link:a,email_otp:r,hashed_token:l,redirect_to:o,verification_type:u},g=Object.assign({},f);return{data:{properties:m,user:g},error:null}}function Ww(s){return s}function e2(s){return s.access_token&&s.refresh_token&&s.expires_in}const od=["global","local","others"];var t2=function(s,a){var r={};for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&a.indexOf(l)<0&&(r[l]=s[l]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,l=Object.getOwnPropertySymbols(s);o<l.length;o++)a.indexOf(l[o])<0&&Object.prototype.propertyIsEnumerable.call(s,l[o])&&(r[l[o]]=s[l[o]]);return r};class n2{constructor({url:a="",headers:r={},fetch:l}){this.url=a,this.headers=r,this.fetch=Yv(l),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}signOut(l){return E(this,arguments,function*(a,r=od[0]){if(od.indexOf(r)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${od.join(", ")}`);try{return yield Ce(this.fetch,"POST",`${this.url}/logout?scope=${r}`,{headers:this.headers,jwt:a,noResolveJson:!0}),{data:null,error:null}}catch(o){if(be(o))return{data:null,error:o};throw o}})}inviteUserByEmail(l){return E(this,arguments,function*(a,r={}){try{return yield Ce(this.fetch,"POST",`${this.url}/invite`,{body:{email:a,data:r.data},headers:this.headers,redirectTo:r.redirectTo,xform:Ra})}catch(o){if(be(o))return{data:{user:null},error:o};throw o}})}generateLink(a){return E(this,null,function*(){try{const{options:r}=a,l=t2(a,["options"]),o=Object.assign(Object.assign({},l),r);return"newEmail"in l&&(o.new_email=l==null?void 0:l.newEmail,delete o.newEmail),yield Ce(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:o,headers:this.headers,xform:Jw,redirectTo:r==null?void 0:r.redirectTo})}catch(r){if(be(r))return{data:{properties:null,user:null},error:r};throw r}})}createUser(a){return E(this,null,function*(){try{return yield Ce(this.fetch,"POST",`${this.url}/admin/users`,{body:a,headers:this.headers,xform:Ra})}catch(r){if(be(r))return{data:{user:null},error:r};throw r}})}listUsers(a){return E(this,null,function*(){var r,l,o,u,f,m,g;try{const v={nextPage:null,lastPage:0,total:0},y=yield Ce(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(l=(r=a==null?void 0:a.page)===null||r===void 0?void 0:r.toString())!==null&&l!==void 0?l:"",per_page:(u=(o=a==null?void 0:a.perPage)===null||o===void 0?void 0:o.toString())!==null&&u!==void 0?u:""},xform:Ww});if(y.error)throw y.error;const S=yield y.json(),x=(f=y.headers.get("x-total-count"))!==null&&f!==void 0?f:0,C=(g=(m=y.headers.get("link"))===null||m===void 0?void 0:m.split(","))!==null&&g!==void 0?g:[];return C.length>0&&(C.forEach(U=>{const Y=parseInt(U.split(";")[0].split("=")[1].substring(0,1)),V=JSON.parse(U.split(";")[1].split("=")[1]);v[`${V}Page`]=Y}),v.total=parseInt(x)),{data:Object.assign(Object.assign({},S),v),error:null}}catch(v){if(be(v))return{data:{users:[]},error:v};throw v}})}getUserById(a){return E(this,null,function*(){ts(a);try{return yield Ce(this.fetch,"GET",`${this.url}/admin/users/${a}`,{headers:this.headers,xform:Ra})}catch(r){if(be(r))return{data:{user:null},error:r};throw r}})}updateUserById(a,r){return E(this,null,function*(){ts(a);try{return yield Ce(this.fetch,"PUT",`${this.url}/admin/users/${a}`,{body:r,headers:this.headers,xform:Ra})}catch(l){if(be(l))return{data:{user:null},error:l};throw l}})}deleteUser(a,r=!1){return E(this,null,function*(){ts(a);try{return yield Ce(this.fetch,"DELETE",`${this.url}/admin/users/${a}`,{headers:this.headers,body:{should_soft_delete:r},xform:Ra})}catch(l){if(be(l))return{data:{user:null},error:l};throw l}})}_listFactors(a){return E(this,null,function*(){ts(a.userId);try{const{data:r,error:l}=yield Ce(this.fetch,"GET",`${this.url}/admin/users/${a.userId}/factors`,{headers:this.headers,xform:o=>({data:{factors:o},error:null})});return{data:r,error:l}}catch(r){if(be(r))return{data:null,error:r};throw r}})}_deleteFactor(a){return E(this,null,function*(){ts(a.userId),ts(a.id);try{return{data:yield Ce(this.fetch,"DELETE",`${this.url}/admin/users/${a.userId}/factors/${a.id}`,{headers:this.headers}),error:null}}catch(r){if(be(r))return{data:null,error:r};throw r}})}}const a2={getItem:s=>yr()?globalThis.localStorage.getItem(s):null,setItem:(s,a)=>{yr()&&globalThis.localStorage.setItem(s,a)},removeItem:s=>{yr()&&globalThis.localStorage.removeItem(s)}};function qg(s={}){return{getItem:a=>s[a]||null,setItem:(a,r)=>{s[a]=r},removeItem:a=>{delete s[a]}}}function i2(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(s){typeof self!="undefined"&&(self.globalThis=self)}}const ns={debug:!!(globalThis&&yr()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class Kv extends Error{constructor(a){super(a),this.isAcquireTimeout=!0}}class s2 extends Kv{}function r2(s,a,r){return E(this,null,function*(){ns.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",s,a);const l=new globalThis.AbortController;return a>0&&setTimeout(()=>{l.abort(),ns.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",s)},a),yield Promise.resolve().then(()=>globalThis.navigator.locks.request(s,a===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:l.signal},o=>E(null,null,function*(){if(o){ns.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",s,o.name);try{return yield r()}finally{ns.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",s,o.name)}}else{if(a===0)throw ns.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",s),new s2(`Acquiring an exclusive Navigator LockManager lock "${s}" immediately failed`);if(ns.debug)try{const u=yield globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(u,null,"  "))}catch(u){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",u)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),yield r()}})))})}i2();const l2={url:gw,storageKey:vw,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:yw,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};function Hg(s,a,r){return E(this,null,function*(){return yield r()})}class _r{constructor(a){var r,l;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=_r.nextInstanceID,_r.nextInstanceID+=1,this.instanceID>0&&jn()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const o=Object.assign(Object.assign({},l2),a);if(this.logDebugMessages=!!o.debug,typeof o.debug=="function"&&(this.logger=o.debug),this.persistSession=o.persistSession,this.storageKey=o.storageKey,this.autoRefreshToken=o.autoRefreshToken,this.admin=new n2({url:o.url,headers:o.headers,fetch:o.fetch}),this.url=o.url,this.headers=o.headers,this.fetch=Yv(o.fetch),this.lock=o.lock||Hg,this.detectSessionInUrl=o.detectSessionInUrl,this.flowType=o.flowType,this.hasCustomAuthorizationHeader=o.hasCustomAuthorizationHeader,o.lock?this.lock=o.lock:jn()&&(!((r=globalThis==null?void 0:globalThis.navigator)===null||r===void 0)&&r.locks)?this.lock=r2:this.lock=Hg,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?o.storage?this.storage=o.storage:yr()?this.storage=a2:(this.memoryStorage={},this.storage=qg(this.memoryStorage)):(this.memoryStorage={},this.storage=qg(this.memoryStorage)),jn()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(u){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",u)}(l=this.broadcastChannel)===null||l===void 0||l.addEventListener("message",u=>E(this,null,function*(){this._debug("received broadcast notification from other tab or client",u),yield this._notifyAllSubscribers(u.data.event,u.data.session,!1)}))}this.initialize()}_debug(...a){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Iv}) ${new Date().toISOString()}`,...a),this}initialize(){return E(this,null,function*(){return this.initializePromise?yield this.initializePromise:(this.initializePromise=E(this,null,function*(){return yield this._acquireLock(-1,()=>E(this,null,function*(){return yield this._initialize()}))}),yield this.initializePromise)})}_initialize(){return E(this,null,function*(){var a;try{const r=Uw(window.location.href);let l="none";if(this._isImplicitGrantCallback(r)?l="implicit":(yield this._isPKCECallback(r))&&(l="pkce"),jn()&&this.detectSessionInUrl&&l!=="none"){const{data:o,error:u}=yield this._getSessionFromURL(r,l);if(u){if(this._debug("#_initialize()","error detecting session from URL",u),Ew(u)){const g=(a=u.details)===null||a===void 0?void 0:a.code;if(g==="identity_already_exists"||g==="identity_not_found"||g==="single_identity_not_deletable")return{error:u}}return yield this._removeSession(),{error:u}}const{session:f,redirectType:m}=o;return this._debug("#_initialize()","detected session in URL",f,"redirect type",m),yield this._saveSession(f),setTimeout(()=>E(this,null,function*(){m==="recovery"?yield this._notifyAllSubscribers("PASSWORD_RECOVERY",f):yield this._notifyAllSubscribers("SIGNED_IN",f)}),0),{error:null}}return yield this._recoverAndRefresh(),{error:null}}catch(r){return be(r)?{error:r}:{error:new Gv("Unexpected error during initialization",r)}}finally{yield this._handleVisibilityChange(),this._debug("#_initialize()","end")}})}signInAnonymously(a){return E(this,null,function*(){var r,l,o;try{const u=yield Ce(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(l=(r=a==null?void 0:a.options)===null||r===void 0?void 0:r.data)!==null&&l!==void 0?l:{},gotrue_meta_security:{captcha_token:(o=a==null?void 0:a.options)===null||o===void 0?void 0:o.captchaToken}},xform:Zn}),{data:f,error:m}=u;if(m||!f)return{data:{user:null,session:null},error:m};const g=f.session,v=f.user;return f.session&&(yield this._saveSession(f.session),yield this._notifyAllSubscribers("SIGNED_IN",g)),{data:{user:v,session:g},error:null}}catch(u){if(be(u))return{data:{user:null,session:null},error:u};throw u}})}signUp(a){return E(this,null,function*(){var r,l,o;try{let u;if("email"in a){const{email:y,password:S,options:x}=a;let C=null,U=null;this.flowType==="pkce"&&([C,U]=yield es(this.storage,this.storageKey)),u=yield Ce(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:x==null?void 0:x.emailRedirectTo,body:{email:y,password:S,data:(r=x==null?void 0:x.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:x==null?void 0:x.captchaToken},code_challenge:C,code_challenge_method:U},xform:Zn})}else if("phone"in a){const{phone:y,password:S,options:x}=a;u=yield Ce(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:y,password:S,data:(l=x==null?void 0:x.data)!==null&&l!==void 0?l:{},channel:(o=x==null?void 0:x.channel)!==null&&o!==void 0?o:"sms",gotrue_meta_security:{captcha_token:x==null?void 0:x.captchaToken}},xform:Zn})}else throw new Wl("You must provide either an email or phone number and a password");const{data:f,error:m}=u;if(m||!f)return{data:{user:null,session:null},error:m};const g=f.session,v=f.user;return f.session&&(yield this._saveSession(f.session),yield this._notifyAllSubscribers("SIGNED_IN",g)),{data:{user:v,session:g},error:null}}catch(u){if(be(u))return{data:{user:null,session:null},error:u};throw u}})}signInWithPassword(a){return E(this,null,function*(){try{let r;if("email"in a){const{email:u,password:f,options:m}=a;r=yield Ce(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:u,password:f,gotrue_meta_security:{captcha_token:m==null?void 0:m.captchaToken}},xform:Bg})}else if("phone"in a){const{phone:u,password:f,options:m}=a;r=yield Ce(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:u,password:f,gotrue_meta_security:{captcha_token:m==null?void 0:m.captchaToken}},xform:Bg})}else throw new Wl("You must provide either an email or phone number and a password");const{data:l,error:o}=r;return o?{data:{user:null,session:null},error:o}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new Jl}:(l.session&&(yield this._saveSession(l.session),yield this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:Object.assign({user:l.user,session:l.session},l.weak_password?{weakPassword:l.weak_password}:null),error:o})}catch(r){if(be(r))return{data:{user:null,session:null},error:r};throw r}})}signInWithOAuth(a){return E(this,null,function*(){var r,l,o,u;return yield this._handleProviderSignIn(a.provider,{redirectTo:(r=a.options)===null||r===void 0?void 0:r.redirectTo,scopes:(l=a.options)===null||l===void 0?void 0:l.scopes,queryParams:(o=a.options)===null||o===void 0?void 0:o.queryParams,skipBrowserRedirect:(u=a.options)===null||u===void 0?void 0:u.skipBrowserRedirect})})}exchangeCodeForSession(a){return E(this,null,function*(){return yield this.initializePromise,this._acquireLock(-1,()=>E(this,null,function*(){return this._exchangeCodeForSession(a)}))})}signInWithWeb3(a){return E(this,null,function*(){const{chain:r}=a;if(r==="solana")return yield this.signInWithSolana(a);throw new Error(`@supabase/auth-js: Unsupported chain "${r}"`)})}signInWithSolana(a){return E(this,null,function*(){var r,l,o,u,f,m,g,v,y,S,x,C;let U,Y;if("message"in a)U=a.message,Y=a.signature;else{const{chain:V,wallet:F,statement:J,options:q}=a;let te;if(jn())if(typeof F=="object")te=F;else{const W=window;if("solana"in W&&typeof W.solana=="object"&&("signIn"in W.solana&&typeof W.solana.signIn=="function"||"signMessage"in W.solana&&typeof W.solana.signMessage=="function"))te=W.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof F!="object"||!(q!=null&&q.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");te=F}const K=new URL((r=q==null?void 0:q.url)!==null&&r!==void 0?r:window.location.href);if("signIn"in te&&te.signIn){const W=yield te.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},q==null?void 0:q.signInWithSolana),{version:"1",domain:K.host,uri:K.href}),J?{statement:J}:null));let le;if(Array.isArray(W)&&W[0]&&typeof W[0]=="object")le=W[0];else if(W&&typeof W=="object"&&"signedMessage"in W&&"signature"in W)le=W;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in le&&"signature"in le&&(typeof le.signedMessage=="string"||le.signedMessage instanceof Uint8Array)&&le.signature instanceof Uint8Array)U=typeof le.signedMessage=="string"?le.signedMessage:new TextDecoder().decode(le.signedMessage),Y=le.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in te)||typeof te.signMessage!="function"||!("publicKey"in te)||typeof te!="object"||!te.publicKey||!("toBase58"in te.publicKey)||typeof te.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");U=[`${K.host} wants you to sign in with your Solana account:`,te.publicKey.toBase58(),...J?["",J,""]:[""],"Version: 1",`URI: ${K.href}`,`Issued At: ${(o=(l=q==null?void 0:q.signInWithSolana)===null||l===void 0?void 0:l.issuedAt)!==null&&o!==void 0?o:new Date().toISOString()}`,...!((u=q==null?void 0:q.signInWithSolana)===null||u===void 0)&&u.notBefore?[`Not Before: ${q.signInWithSolana.notBefore}`]:[],...!((f=q==null?void 0:q.signInWithSolana)===null||f===void 0)&&f.expirationTime?[`Expiration Time: ${q.signInWithSolana.expirationTime}`]:[],...!((m=q==null?void 0:q.signInWithSolana)===null||m===void 0)&&m.chainId?[`Chain ID: ${q.signInWithSolana.chainId}`]:[],...!((g=q==null?void 0:q.signInWithSolana)===null||g===void 0)&&g.nonce?[`Nonce: ${q.signInWithSolana.nonce}`]:[],...!((v=q==null?void 0:q.signInWithSolana)===null||v===void 0)&&v.requestId?[`Request ID: ${q.signInWithSolana.requestId}`]:[],...!((S=(y=q==null?void 0:q.signInWithSolana)===null||y===void 0?void 0:y.resources)===null||S===void 0)&&S.length?["Resources",...q.signInWithSolana.resources.map(le=>`- ${le}`)]:[]].join(`
`);const W=yield te.signMessage(new TextEncoder().encode(U),"utf8");if(!W||!(W instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");Y=W}}try{const{data:V,error:F}=yield Ce(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:U,signature:Nw(Y)},!((x=a.options)===null||x===void 0)&&x.captchaToken?{gotrue_meta_security:{captcha_token:(C=a.options)===null||C===void 0?void 0:C.captchaToken}}:null),xform:Zn});if(F)throw F;return!V||!V.session||!V.user?{data:{user:null,session:null},error:new Jl}:(V.session&&(yield this._saveSession(V.session),yield this._notifyAllSubscribers("SIGNED_IN",V.session)),{data:Object.assign({},V),error:F})}catch(V){if(be(V))return{data:{user:null,session:null},error:V};throw V}})}_exchangeCodeForSession(a){return E(this,null,function*(){const r=yield to(this.storage,`${this.storageKey}-code-verifier`),[l,o]=(r!=null?r:"").split("/");try{const{data:u,error:f}=yield Ce(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:a,code_verifier:l},xform:Zn});if(yield no(this.storage,`${this.storageKey}-code-verifier`),f)throw f;return!u||!u.session||!u.user?{data:{user:null,session:null,redirectType:null},error:new Jl}:(u.session&&(yield this._saveSession(u.session),yield this._notifyAllSubscribers("SIGNED_IN",u.session)),{data:Object.assign(Object.assign({},u),{redirectType:o!=null?o:null}),error:f})}catch(u){if(be(u))return{data:{user:null,session:null,redirectType:null},error:u};throw u}})}signInWithIdToken(a){return E(this,null,function*(){try{const{options:r,provider:l,token:o,access_token:u,nonce:f}=a,m=yield Ce(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:l,id_token:o,access_token:u,nonce:f,gotrue_meta_security:{captcha_token:r==null?void 0:r.captchaToken}},xform:Zn}),{data:g,error:v}=m;return v?{data:{user:null,session:null},error:v}:!g||!g.session||!g.user?{data:{user:null,session:null},error:new Jl}:(g.session&&(yield this._saveSession(g.session),yield this._notifyAllSubscribers("SIGNED_IN",g.session)),{data:g,error:v})}catch(r){if(be(r))return{data:{user:null,session:null},error:r};throw r}})}signInWithOtp(a){return E(this,null,function*(){var r,l,o,u,f;try{if("email"in a){const{email:m,options:g}=a;let v=null,y=null;this.flowType==="pkce"&&([v,y]=yield es(this.storage,this.storageKey));const{error:S}=yield Ce(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:m,data:(r=g==null?void 0:g.data)!==null&&r!==void 0?r:{},create_user:(l=g==null?void 0:g.shouldCreateUser)!==null&&l!==void 0?l:!0,gotrue_meta_security:{captcha_token:g==null?void 0:g.captchaToken},code_challenge:v,code_challenge_method:y},redirectTo:g==null?void 0:g.emailRedirectTo});return{data:{user:null,session:null},error:S}}if("phone"in a){const{phone:m,options:g}=a,{data:v,error:y}=yield Ce(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:m,data:(o=g==null?void 0:g.data)!==null&&o!==void 0?o:{},create_user:(u=g==null?void 0:g.shouldCreateUser)!==null&&u!==void 0?u:!0,gotrue_meta_security:{captcha_token:g==null?void 0:g.captchaToken},channel:(f=g==null?void 0:g.channel)!==null&&f!==void 0?f:"sms"}});return{data:{user:null,session:null,messageId:v==null?void 0:v.message_id},error:y}}throw new Wl("You must provide either an email or phone number.")}catch(m){if(be(m))return{data:{user:null,session:null},error:m};throw m}})}verifyOtp(a){return E(this,null,function*(){var r,l;try{let o,u;"options"in a&&(o=(r=a.options)===null||r===void 0?void 0:r.redirectTo,u=(l=a.options)===null||l===void 0?void 0:l.captchaToken);const{data:f,error:m}=yield Ce(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},a),{gotrue_meta_security:{captcha_token:u}}),redirectTo:o,xform:Zn});if(m)throw m;if(!f)throw new Error("An error occurred on token verification.");const g=f.session,v=f.user;return g!=null&&g.access_token&&(yield this._saveSession(g),yield this._notifyAllSubscribers(a.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",g)),{data:{user:v,session:g},error:null}}catch(o){if(be(o))return{data:{user:null,session:null},error:o};throw o}})}signInWithSSO(a){return E(this,null,function*(){var r,l,o;try{let u=null,f=null;return this.flowType==="pkce"&&([u,f]=yield es(this.storage,this.storageKey)),yield Ce(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in a?{provider_id:a.providerId}:null),"domain"in a?{domain:a.domain}:null),{redirect_to:(l=(r=a.options)===null||r===void 0?void 0:r.redirectTo)!==null&&l!==void 0?l:void 0}),!((o=a==null?void 0:a.options)===null||o===void 0)&&o.captchaToken?{gotrue_meta_security:{captcha_token:a.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:u,code_challenge_method:f}),headers:this.headers,xform:Zw})}catch(u){if(be(u))return{data:null,error:u};throw u}})}reauthenticate(){return E(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>E(this,null,function*(){return yield this._reauthenticate()}))})}_reauthenticate(){return E(this,null,function*(){try{return yield this._useSession(a=>E(this,null,function*(){const{data:{session:r},error:l}=a;if(l)throw l;if(!r)throw new Ca;const{error:o}=yield Ce(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:r.access_token});return{data:{user:null,session:null},error:o}}))}catch(a){if(be(a))return{data:{user:null,session:null},error:a};throw a}})}resend(a){return E(this,null,function*(){try{const r=`${this.url}/resend`;if("email"in a){const{email:l,type:o,options:u}=a,{error:f}=yield Ce(this.fetch,"POST",r,{headers:this.headers,body:{email:l,type:o,gotrue_meta_security:{captcha_token:u==null?void 0:u.captchaToken}},redirectTo:u==null?void 0:u.emailRedirectTo});return{data:{user:null,session:null},error:f}}else if("phone"in a){const{phone:l,type:o,options:u}=a,{data:f,error:m}=yield Ce(this.fetch,"POST",r,{headers:this.headers,body:{phone:l,type:o,gotrue_meta_security:{captcha_token:u==null?void 0:u.captchaToken}}});return{data:{user:null,session:null,messageId:f==null?void 0:f.message_id},error:m}}throw new Wl("You must provide either an email or phone number and a type")}catch(r){if(be(r))return{data:{user:null,session:null},error:r};throw r}})}getSession(){return E(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>E(this,null,function*(){return this._useSession(r=>E(this,null,function*(){return r}))}))})}_acquireLock(a,r){return E(this,null,function*(){this._debug("#_acquireLock","begin",a);try{if(this.lockAcquired){const l=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),o=E(this,null,function*(){return yield l,yield r()});return this.pendingInLock.push(E(this,null,function*(){try{yield o}catch(u){}})),o}return yield this.lock(`lock:${this.storageKey}`,a,()=>E(this,null,function*(){this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const l=r();for(this.pendingInLock.push(E(this,null,function*(){try{yield l}catch(o){}})),yield l;this.pendingInLock.length;){const o=[...this.pendingInLock];yield Promise.all(o),this.pendingInLock.splice(0,o.length)}return yield l}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}})}_useSession(a){return E(this,null,function*(){this._debug("#_useSession","begin");try{const r=yield this.__loadSession();return yield a(r)}finally{this._debug("#_useSession","end")}})}__loadSession(){return E(this,null,function*(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let a=null;const r=yield to(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",r),r!==null&&(this._isValidSession(r)?a=r:(this._debug("#getSession()","session from storage is not valid"),yield this._removeSession())),!a)return{data:{session:null},error:null};const l=a.expires_at?a.expires_at*1e3-Date.now()<sd:!1;if(this._debug("#__loadSession()",`session has${l?"":" not"} expired`,"expires_at",a.expires_at),!l){if(this.storage.isServer){let f=this.suppressGetSessionWarning;a=new Proxy(a,{get:(g,v,y)=>(!f&&v==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),f=!0,this.suppressGetSessionWarning=!0),Reflect.get(g,v,y))})}return{data:{session:a},error:null}}const{session:o,error:u}=yield this._callRefreshToken(a.refresh_token);return u?{data:{session:null},error:u}:{data:{session:o},error:null}}finally{this._debug("#__loadSession()","end")}})}getUser(a){return E(this,null,function*(){return a?yield this._getUser(a):(yield this.initializePromise,yield this._acquireLock(-1,()=>E(this,null,function*(){return yield this._getUser()})))})}_getUser(a){return E(this,null,function*(){try{return a?yield Ce(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:a,xform:Ra}):yield this._useSession(r=>E(this,null,function*(){var l,o,u;const{data:f,error:m}=r;if(m)throw m;return!(!((l=f.session)===null||l===void 0)&&l.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new Ca}:yield Ce(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(u=(o=f.session)===null||o===void 0?void 0:o.access_token)!==null&&u!==void 0?u:void 0,xform:Ra})}))}catch(r){if(be(r))return jw(r)&&(yield this._removeSession(),yield no(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:r};throw r}})}updateUser(l){return E(this,arguments,function*(a,r={}){return yield this.initializePromise,yield this._acquireLock(-1,()=>E(this,null,function*(){return yield this._updateUser(a,r)}))})}_updateUser(l){return E(this,arguments,function*(a,r={}){try{return yield this._useSession(o=>E(this,null,function*(){const{data:u,error:f}=o;if(f)throw f;if(!u.session)throw new Ca;const m=u.session;let g=null,v=null;this.flowType==="pkce"&&a.email!=null&&([g,v]=yield es(this.storage,this.storageKey));const{data:y,error:S}=yield Ce(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:r==null?void 0:r.emailRedirectTo,body:Object.assign(Object.assign({},a),{code_challenge:g,code_challenge_method:v}),jwt:m.access_token,xform:Ra});if(S)throw S;return m.user=y.user,yield this._saveSession(m),yield this._notifyAllSubscribers("USER_UPDATED",m),{data:{user:m.user},error:null}}))}catch(o){if(be(o))return{data:{user:null},error:o};throw o}})}setSession(a){return E(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>E(this,null,function*(){return yield this._setSession(a)}))})}_setSession(a){return E(this,null,function*(){try{if(!a.access_token||!a.refresh_token)throw new Ca;const r=Date.now()/1e3;let l=r,o=!0,u=null;const{payload:f}=ld(a.access_token);if(f.exp&&(l=f.exp,o=l<=r),o){const{session:m,error:g}=yield this._callRefreshToken(a.refresh_token);if(g)return{data:{user:null,session:null},error:g};if(!m)return{data:{user:null,session:null},error:null};u=m}else{const{data:m,error:g}=yield this._getUser(a.access_token);if(g)throw g;u={access_token:a.access_token,refresh_token:a.refresh_token,user:m.user,token_type:"bearer",expires_in:l-r,expires_at:l},yield this._saveSession(u),yield this._notifyAllSubscribers("SIGNED_IN",u)}return{data:{user:u.user,session:u},error:null}}catch(r){if(be(r))return{data:{session:null,user:null},error:r};throw r}})}refreshSession(a){return E(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>E(this,null,function*(){return yield this._refreshSession(a)}))})}_refreshSession(a){return E(this,null,function*(){try{return yield this._useSession(r=>E(this,null,function*(){var l;if(!a){const{data:f,error:m}=r;if(m)throw m;a=(l=f.session)!==null&&l!==void 0?l:void 0}if(!(a!=null&&a.refresh_token))throw new Ca;const{session:o,error:u}=yield this._callRefreshToken(a.refresh_token);return u?{data:{user:null,session:null},error:u}:o?{data:{user:o.user,session:o},error:null}:{data:{user:null,session:null},error:null}}))}catch(r){if(be(r))return{data:{user:null,session:null},error:r};throw r}})}_getSessionFromURL(a,r){return E(this,null,function*(){try{if(!jn())throw new eo("No browser detected.");if(a.error||a.error_description||a.error_code)throw new eo(a.error_description||"Error in URL with unspecified error_description",{error:a.error||"unspecified_error",code:a.error_code||"unspecified_code"});switch(r){case"implicit":if(this.flowType==="pkce")throw new Mg("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new eo("Not a valid implicit grant flow url.");break;default:}if(r==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!a.code)throw new Mg("No code detected.");const{data:J,error:q}=yield this._exchangeCodeForSession(a.code);if(q)throw q;const te=new URL(window.location.href);return te.searchParams.delete("code"),window.history.replaceState(window.history.state,"",te.toString()),{data:{session:J.session,redirectType:null},error:null}}const{provider_token:l,provider_refresh_token:o,access_token:u,refresh_token:f,expires_in:m,expires_at:g,token_type:v}=a;if(!u||!m||!f||!v)throw new eo("No session defined in URL");const y=Math.round(Date.now()/1e3),S=parseInt(m);let x=y+S;g&&(x=parseInt(g));const C=x-y;C*1e3<=ss&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${C}s, should have been closer to ${S}s`);const U=x-S;y-U>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",U,x,y):y-U<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",U,x,y);const{data:Y,error:V}=yield this._getUser(u);if(V)throw V;const F={provider_token:l,provider_refresh_token:o,access_token:u,expires_in:S,expires_at:x,refresh_token:f,token_type:v,user:Y.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:F,redirectType:a.type},error:null}}catch(l){if(be(l))return{data:{session:null,redirectType:null},error:l};throw l}})}_isImplicitGrantCallback(a){return!!(a.access_token||a.error_description)}_isPKCECallback(a){return E(this,null,function*(){const r=yield to(this.storage,`${this.storageKey}-code-verifier`);return!!(a.code&&r)})}signOut(){return E(this,arguments,function*(a={scope:"global"}){return yield this.initializePromise,yield this._acquireLock(-1,()=>E(this,null,function*(){return yield this._signOut(a)}))})}_signOut(){return E(this,arguments,function*({scope:a}={scope:"global"}){return yield this._useSession(r=>E(this,null,function*(){var l;const{data:o,error:u}=r;if(u)return{error:u};const f=(l=o.session)===null||l===void 0?void 0:l.access_token;if(f){const{error:m}=yield this.admin.signOut(f,a);if(m&&!(ww(m)&&(m.status===404||m.status===401||m.status===403)))return{error:m}}return a!=="others"&&(yield this._removeSession(),yield no(this.storage,`${this.storageKey}-code-verifier`)),{error:null}}))})}onAuthStateChange(a){const r=Mw(),l={id:r,callback:a,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",r),this.stateChangeEmitters.delete(r)}};return this._debug("#onAuthStateChange()","registered callback with id",r),this.stateChangeEmitters.set(r,l),E(this,null,function*(){yield this.initializePromise,yield this._acquireLock(-1,()=>E(this,null,function*(){this._emitInitialSession(r)}))}),{data:{subscription:l}}}_emitInitialSession(a){return E(this,null,function*(){return yield this._useSession(r=>E(this,null,function*(){var l,o;try{const{data:{session:u},error:f}=r;if(f)throw f;yield(l=this.stateChangeEmitters.get(a))===null||l===void 0?void 0:l.callback("INITIAL_SESSION",u),this._debug("INITIAL_SESSION","callback id",a,"session",u)}catch(u){yield(o=this.stateChangeEmitters.get(a))===null||o===void 0?void 0:o.callback("INITIAL_SESSION",null),this._debug("INITIAL_SESSION","callback id",a,"error",u),console.error(u)}}))})}resetPasswordForEmail(l){return E(this,arguments,function*(a,r={}){let o=null,u=null;this.flowType==="pkce"&&([o,u]=yield es(this.storage,this.storageKey,!0));try{return yield Ce(this.fetch,"POST",`${this.url}/recover`,{body:{email:a,code_challenge:o,code_challenge_method:u,gotrue_meta_security:{captcha_token:r.captchaToken}},headers:this.headers,redirectTo:r.redirectTo})}catch(f){if(be(f))return{data:null,error:f};throw f}})}getUserIdentities(){return E(this,null,function*(){var a;try{const{data:r,error:l}=yield this.getUser();if(l)throw l;return{data:{identities:(a=r.user.identities)!==null&&a!==void 0?a:[]},error:null}}catch(r){if(be(r))return{data:null,error:r};throw r}})}linkIdentity(a){return E(this,null,function*(){var r;try{const{data:l,error:o}=yield this._useSession(u=>E(this,null,function*(){var f,m,g,v,y;const{data:S,error:x}=u;if(x)throw x;const C=yield this._getUrlForProvider(`${this.url}/user/identities/authorize`,a.provider,{redirectTo:(f=a.options)===null||f===void 0?void 0:f.redirectTo,scopes:(m=a.options)===null||m===void 0?void 0:m.scopes,queryParams:(g=a.options)===null||g===void 0?void 0:g.queryParams,skipBrowserRedirect:!0});return yield Ce(this.fetch,"GET",C,{headers:this.headers,jwt:(y=(v=S.session)===null||v===void 0?void 0:v.access_token)!==null&&y!==void 0?y:void 0})}));if(o)throw o;return jn()&&!(!((r=a.options)===null||r===void 0)&&r.skipBrowserRedirect)&&window.location.assign(l==null?void 0:l.url),{data:{provider:a.provider,url:l==null?void 0:l.url},error:null}}catch(l){if(be(l))return{data:{provider:a.provider,url:null},error:l};throw l}})}unlinkIdentity(a){return E(this,null,function*(){try{return yield this._useSession(r=>E(this,null,function*(){var l,o;const{data:u,error:f}=r;if(f)throw f;return yield Ce(this.fetch,"DELETE",`${this.url}/user/identities/${a.identity_id}`,{headers:this.headers,jwt:(o=(l=u.session)===null||l===void 0?void 0:l.access_token)!==null&&o!==void 0?o:void 0})}))}catch(r){if(be(r))return{data:null,error:r};throw r}})}_refreshAccessToken(a){return E(this,null,function*(){const r=`#_refreshAccessToken(${a.substring(0,5)}...)`;this._debug(r,"begin");try{const l=Date.now();return yield Lw(o=>E(this,null,function*(){return o>0&&(yield zw(200*Math.pow(2,o-1))),this._debug(r,"refreshing attempt",o),yield Ce(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:a},headers:this.headers,xform:Zn})}),(o,u)=>{const f=200*Math.pow(2,o);return u&&rd(u)&&Date.now()+f-l<ss})}catch(l){if(this._debug(r,"error",l),be(l))return{data:{session:null,user:null},error:l};throw l}finally{this._debug(r,"end")}})}_isValidSession(a){return typeof a=="object"&&a!==null&&"access_token"in a&&"refresh_token"in a&&"expires_at"in a}_handleProviderSignIn(a,r){return E(this,null,function*(){const l=yield this._getUrlForProvider(`${this.url}/authorize`,a,{redirectTo:r.redirectTo,scopes:r.scopes,queryParams:r.queryParams});return this._debug("#_handleProviderSignIn()","provider",a,"options",r,"url",l),jn()&&!r.skipBrowserRedirect&&window.location.assign(l),{data:{provider:a,url:l},error:null}})}_recoverAndRefresh(){return E(this,null,function*(){var a;const r="#_recoverAndRefresh()";this._debug(r,"begin");try{const l=yield to(this.storage,this.storageKey);if(this._debug(r,"session from storage",l),!this._isValidSession(l)){this._debug(r,"session is not valid"),l!==null&&(yield this._removeSession());return}const o=((a=l.expires_at)!==null&&a!==void 0?a:1/0)*1e3-Date.now()<sd;if(this._debug(r,`session has${o?"":" not"} expired with margin of ${sd}s`),o){if(this.autoRefreshToken&&l.refresh_token){const{error:u}=yield this._callRefreshToken(l.refresh_token);u&&(console.error(u),rd(u)||(this._debug(r,"refresh failed with a non-retryable error, removing the session",u),yield this._removeSession()))}}else yield this._notifyAllSubscribers("SIGNED_IN",l)}catch(l){this._debug(r,"error",l),console.error(l);return}finally{this._debug(r,"end")}})}_callRefreshToken(a){return E(this,null,function*(){var r,l;if(!a)throw new Ca;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const o=`#_callRefreshToken(${a.substring(0,5)}...)`;this._debug(o,"begin");try{this.refreshingDeferred=new _o;const{data:u,error:f}=yield this._refreshAccessToken(a);if(f)throw f;if(!u.session)throw new Ca;yield this._saveSession(u.session),yield this._notifyAllSubscribers("TOKEN_REFRESHED",u.session);const m={session:u.session,error:null};return this.refreshingDeferred.resolve(m),m}catch(u){if(this._debug(o,"error",u),be(u)){const f={session:null,error:u};return rd(u)||(yield this._removeSession()),(r=this.refreshingDeferred)===null||r===void 0||r.resolve(f),f}throw(l=this.refreshingDeferred)===null||l===void 0||l.reject(u),u}finally{this.refreshingDeferred=null,this._debug(o,"end")}})}_notifyAllSubscribers(a,r,l=!0){return E(this,null,function*(){const o=`#_notifyAllSubscribers(${a})`;this._debug(o,"begin",r,`broadcast = ${l}`);try{this.broadcastChannel&&l&&this.broadcastChannel.postMessage({event:a,session:r});const u=[],f=Array.from(this.stateChangeEmitters.values()).map(m=>E(this,null,function*(){try{yield m.callback(a,r)}catch(g){u.push(g)}}));if(yield Promise.all(f),u.length>0){for(let m=0;m<u.length;m+=1)console.error(u[m]);throw u[0]}}finally{this._debug(o,"end")}})}_saveSession(a){return E(this,null,function*(){this._debug("#_saveSession()",a),this.suppressGetSessionWarning=!0,yield Fv(this.storage,this.storageKey,a)})}_removeSession(){return E(this,null,function*(){this._debug("#_removeSession()"),yield no(this.storage,this.storageKey),yield this._notifyAllSubscribers("SIGNED_OUT",null)})}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const a=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{a&&jn()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",a)}catch(r){console.error("removing visibilitychange callback failed",r)}}_startAutoRefresh(){return E(this,null,function*(){yield this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const a=setInterval(()=>this._autoRefreshTokenTick(),ss);this.autoRefreshTicker=a,a&&typeof a=="object"&&typeof a.unref=="function"?a.unref():typeof Deno!="undefined"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(a),setTimeout(()=>E(this,null,function*(){yield this.initializePromise,yield this._autoRefreshTokenTick()}),0)})}_stopAutoRefresh(){return E(this,null,function*(){this._debug("#_stopAutoRefresh()");const a=this.autoRefreshTicker;this.autoRefreshTicker=null,a&&clearInterval(a)})}startAutoRefresh(){return E(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._startAutoRefresh()})}stopAutoRefresh(){return E(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._stopAutoRefresh()})}_autoRefreshTokenTick(){return E(this,null,function*(){this._debug("#_autoRefreshTokenTick()","begin");try{yield this._acquireLock(0,()=>E(this,null,function*(){try{const a=Date.now();try{return yield this._useSession(r=>E(this,null,function*(){const{data:{session:l}}=r;if(!l||!l.refresh_token||!l.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const o=Math.floor((l.expires_at*1e3-a)/ss);this._debug("#_autoRefreshTokenTick()",`access token expires in ${o} ticks, a tick lasts ${ss}ms, refresh threshold is ${Ad} ticks`),o<=Ad&&(yield this._callRefreshToken(l.refresh_token))}))}catch(r){console.error("Auto refresh tick failed with error. This is likely a transient error.",r)}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(a){if(a.isAcquireTimeout||a instanceof Kv)this._debug("auto refresh token tick lock not available");else throw a}})}_handleVisibilityChange(){return E(this,null,function*(){if(this._debug("#_handleVisibilityChange()"),!jn()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=()=>E(this,null,function*(){return yield this._onVisibilityChanged(!1)}),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),yield this._onVisibilityChanged(!0)}catch(a){console.error("_handleVisibilityChange",a)}})}_onVisibilityChanged(a){return E(this,null,function*(){const r=`#_onVisibilityChanged(${a})`;this._debug(r,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),a||(yield this.initializePromise,yield this._acquireLock(-1,()=>E(this,null,function*(){if(document.visibilityState!=="visible"){this._debug(r,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}yield this._recoverAndRefresh()})))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()})}_getUrlForProvider(a,r,l){return E(this,null,function*(){const o=[`provider=${encodeURIComponent(r)}`];if(l!=null&&l.redirectTo&&o.push(`redirect_to=${encodeURIComponent(l.redirectTo)}`),l!=null&&l.scopes&&o.push(`scopes=${encodeURIComponent(l.scopes)}`),this.flowType==="pkce"){const[u,f]=yield es(this.storage,this.storageKey),m=new URLSearchParams({code_challenge:`${encodeURIComponent(u)}`,code_challenge_method:`${encodeURIComponent(f)}`});o.push(m.toString())}if(l!=null&&l.queryParams){const u=new URLSearchParams(l.queryParams);o.push(u.toString())}return l!=null&&l.skipBrowserRedirect&&o.push(`skip_http_redirect=${l.skipBrowserRedirect}`),`${a}?${o.join("&")}`})}_unenroll(a){return E(this,null,function*(){try{return yield this._useSession(r=>E(this,null,function*(){var l;const{data:o,error:u}=r;return u?{data:null,error:u}:yield Ce(this.fetch,"DELETE",`${this.url}/factors/${a.factorId}`,{headers:this.headers,jwt:(l=o==null?void 0:o.session)===null||l===void 0?void 0:l.access_token})}))}catch(r){if(be(r))return{data:null,error:r};throw r}})}_enroll(a){return E(this,null,function*(){try{return yield this._useSession(r=>E(this,null,function*(){var l,o;const{data:u,error:f}=r;if(f)return{data:null,error:f};const m=Object.assign({friendly_name:a.friendlyName,factor_type:a.factorType},a.factorType==="phone"?{phone:a.phone}:{issuer:a.issuer}),{data:g,error:v}=yield Ce(this.fetch,"POST",`${this.url}/factors`,{body:m,headers:this.headers,jwt:(l=u==null?void 0:u.session)===null||l===void 0?void 0:l.access_token});return v?{data:null,error:v}:(a.factorType==="totp"&&(!((o=g==null?void 0:g.totp)===null||o===void 0)&&o.qr_code)&&(g.totp.qr_code=`data:image/svg+xml;utf-8,${g.totp.qr_code}`),{data:g,error:null})}))}catch(r){if(be(r))return{data:null,error:r};throw r}})}_verify(a){return E(this,null,function*(){return this._acquireLock(-1,()=>E(this,null,function*(){try{return yield this._useSession(r=>E(this,null,function*(){var l;const{data:o,error:u}=r;if(u)return{data:null,error:u};const{data:f,error:m}=yield Ce(this.fetch,"POST",`${this.url}/factors/${a.factorId}/verify`,{body:{code:a.code,challenge_id:a.challengeId},headers:this.headers,jwt:(l=o==null?void 0:o.session)===null||l===void 0?void 0:l.access_token});return m?{data:null,error:m}:(yield this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+f.expires_in},f)),yield this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",f),{data:f,error:m})}))}catch(r){if(be(r))return{data:null,error:r};throw r}}))})}_challenge(a){return E(this,null,function*(){return this._acquireLock(-1,()=>E(this,null,function*(){try{return yield this._useSession(r=>E(this,null,function*(){var l;const{data:o,error:u}=r;return u?{data:null,error:u}:yield Ce(this.fetch,"POST",`${this.url}/factors/${a.factorId}/challenge`,{body:{channel:a.channel},headers:this.headers,jwt:(l=o==null?void 0:o.session)===null||l===void 0?void 0:l.access_token})}))}catch(r){if(be(r))return{data:null,error:r};throw r}}))})}_challengeAndVerify(a){return E(this,null,function*(){const{data:r,error:l}=yield this._challenge({factorId:a.factorId});return l?{data:null,error:l}:yield this._verify({factorId:a.factorId,challengeId:r.id,code:a.code})})}_listFactors(){return E(this,null,function*(){const{data:{user:a},error:r}=yield this.getUser();if(r)return{data:null,error:r};const l=(a==null?void 0:a.factors)||[],o=l.filter(f=>f.factor_type==="totp"&&f.status==="verified"),u=l.filter(f=>f.factor_type==="phone"&&f.status==="verified");return{data:{all:l,totp:o,phone:u},error:null}})}_getAuthenticatorAssuranceLevel(){return E(this,null,function*(){return this._acquireLock(-1,()=>E(this,null,function*(){return yield this._useSession(a=>E(this,null,function*(){var r,l;const{data:{session:o},error:u}=a;if(u)return{data:null,error:u};if(!o)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:f}=ld(o.access_token);let m=null;f.aal&&(m=f.aal);let g=m;((l=(r=o.user.factors)===null||r===void 0?void 0:r.filter(S=>S.status==="verified"))!==null&&l!==void 0?l:[]).length>0&&(g="aal2");const y=f.amr||[];return{data:{currentLevel:m,nextLevel:g,currentAuthenticationMethods:y},error:null}}))}))})}fetchJwk(l){return E(this,arguments,function*(a,r={keys:[]}){let o=r.keys.find(m=>m.kid===a);if(o||(o=this.jwks.keys.find(m=>m.kid===a),o&&this.jwks_cached_at+_w>Date.now()))return o;const{data:u,error:f}=yield Ce(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(f)throw f;if(!u.keys||u.keys.length===0)throw new vr("JWKS is empty");if(this.jwks=u,this.jwks_cached_at=Date.now(),o=u.keys.find(m=>m.kid===a),!o)throw new vr("No matching signing key found in JWKS");return o})}getClaims(l){return E(this,arguments,function*(a,r={keys:[]}){try{let o=a;if(!o){const{data:U,error:Y}=yield this.getSession();if(Y||!U.session)return{data:null,error:Y};o=U.session.access_token}const{header:u,payload:f,signature:m,raw:{header:g,payload:v}}=ld(o);if(Gw(f.exp),!u.kid||u.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:U}=yield this.getUser(o);if(U)throw U;return{data:{claims:f,header:u,signature:m},error:null}}const y=Vw(u.alg),S=yield this.fetchJwk(u.kid,r),x=yield crypto.subtle.importKey("jwk",S,y,!0,["verify"]);if(!(yield crypto.subtle.verify(y,x,m,Ow(`${g}.${v}`))))throw new vr("Invalid JWT signature");return{data:{claims:f,header:u,signature:m},error:null}}catch(o){if(be(o))return{data:null,error:o};throw o}})}}_r.nextInstanceID=0;const o2=_r;class c2 extends o2{constructor(a){super(a)}}var u2=function(s,a,r,l){function o(u){return u instanceof r?u:new r(function(f){f(u)})}return new(r||(r=Promise))(function(u,f){function m(y){try{v(l.next(y))}catch(S){f(S)}}function g(y){try{v(l.throw(y))}catch(S){f(S)}}function v(y){y.done?u(y.value):o(y.value).then(m,g)}v((l=l.apply(s,a||[])).next())})};class d2{constructor(a,r,l){var o,u,f;if(this.supabaseUrl=a,this.supabaseKey=r,!a)throw new Error("supabaseUrl is required.");if(!r)throw new Error("supabaseKey is required.");const m=mw(a),g=new URL(m);this.realtimeUrl=new URL("realtime/v1",g),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",g),this.storageUrl=new URL("storage/v1",g),this.functionsUrl=new URL("functions/v1",g);const v=`sb-${g.hostname.split(".")[0]}-auth-token`,y={db:rw,realtime:ow,auth:Object.assign(Object.assign({},lw),{storageKey:v}),global:sw},S=pw(l!=null?l:{},y);this.storageKey=(o=S.auth.storageKey)!==null&&o!==void 0?o:"",this.headers=(u=S.global.headers)!==null&&u!==void 0?u:{},S.accessToken?(this.accessToken=S.accessToken,this.auth=new Proxy({},{get:(x,C)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(C)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((f=S.auth)!==null&&f!==void 0?f:{},this.headers,S.global.fetch),this.fetch=fw(r,this._getAccessToken.bind(this),S.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},S.realtime)),this.rest=new CS(new URL("rest/v1",g).href,{headers:this.headers,schema:S.db.schema,fetch:this.fetch}),S.accessToken||this._listenForAuthEvents()}get functions(){return new gS(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new nw(this.storageUrl.href,this.headers,this.fetch)}from(a){return this.rest.from(a)}schema(a){return this.rest.schema(a)}rpc(a,r={},l={}){return this.rest.rpc(a,r,l)}channel(a,r={config:{}}){return this.realtime.channel(a,r)}getChannels(){return this.realtime.getChannels()}removeChannel(a){return this.realtime.removeChannel(a)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var a,r;return u2(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:l}=yield this.auth.getSession();return(r=(a=l.session)===null||a===void 0?void 0:a.access_token)!==null&&r!==void 0?r:null})}_initSupabaseAuthClient({autoRefreshToken:a,persistSession:r,detectSessionInUrl:l,storage:o,storageKey:u,flowType:f,lock:m,debug:g},v,y){const S={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new c2({url:this.authUrl.href,headers:Object.assign(Object.assign({},S),v),storageKey:u,autoRefreshToken:a,persistSession:r,detectSessionInUrl:l,storage:o,flowType:f,lock:m,debug:g,fetch:y,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(a){return new $S(this.realtimeUrl.href,Object.assign(Object.assign({},a),{params:Object.assign({apikey:this.supabaseKey},a==null?void 0:a.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((r,l)=>{this._handleTokenChanged(r,"CLIENT",l==null?void 0:l.access_token)})}_handleTokenChanged(a,r,l){(a==="TOKEN_REFRESHED"||a==="SIGNED_IN")&&this.changedAccessToken!==l?this.changedAccessToken=l:a==="SIGNED_OUT"&&(this.realtime.setAuth(),r=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const f2=(s,a,r)=>new d2(s,a,r),h2="https://gqdbmvtgychgwztlbaus.supabase.co",m2="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdxZGJtdnRneWNoZ3d6dGxiYXVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NDc2MDAsImV4cCI6MjA2NjUyMzYwMH0.Q889SrVOiIFfKi2S9Ma4xVhjkAE3nKaE_B03G7S6Ibo",li=f2(h2,m2,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}});function p2(){const{getToken:s,userId:a,isLoaded:r,isSignedIn:l}=hv(),[o,u]=ee.useState(!1);return ee.useEffect(()=>{E(null,null,function*(){var m,g,v;if(r)if(l&&a)try{console.log("🔐 Setting up Supabase auth for user:",a);let y=null;try{y=yield s({template:"supabase"})}catch(S){if((m=S.message)!=null&&m.includes("No JWT template exists")){console.warn("⚠️ Supabase JWT template not configured in Clerk. Using default JWT token.");try{y=yield s(),console.log("✅ Using default Clerk JWT token for backend authentication")}catch(x){console.error("❌ Failed to get default Clerk token:",x),u(!1);return}}else throw S}if(y){console.log("✅ Got Clerk token for backend authentication"),u(!0);try{const{data:S,error:x}=yield li.auth.setSession({access_token:y,refresh_token:""});x?console.log("ℹ️ Clerk token not compatible with Supabase session (expected for Clerk-only setup)"):console.log("✅ Supabase session also set successfully:",(v=(g=S.session)==null?void 0:g.user)==null?void 0:v.id)}catch(S){console.log("ℹ️ Using Clerk-only authentication mode")}}else console.warn("⚠️ No token received from Clerk"),u(!1)}catch(y){console.error("💥 Error setting Supabase auth:",y),u(!1)}else try{yield li.auth.signOut(),u(!1)}catch(y){console.error("Error signing out from Supabase:",y)}})},[s,a,r,l]),{supabase:li,userId:a,isSupabaseReady:o,isAuthenticated:l&&!!a}}class Rd{static getAuthHeaders(a){return E(this,null,function*(){const r={"Content-Type":"application/json"};if(a)try{const l=yield a();l&&(r.Authorization=`Bearer ${l}`)}catch(l){console.warn("Failed to get Clerk token:",l)}return r})}static createProject(a,r,l){return E(this,null,function*(){try{const o=a.contract_code||`// ${a.name} - ${a.template} template
// Network: ${a.network}

pragma solidity ^0.8.0;

contract ${a.name.replace(/\s+/g,"")} {
    // TODO: Implement contract logic
    string public name = "${a.name}";

    constructor() {
        // Initialize contract
    }
}`,u=yield this.getAuthHeaders(l);console.log("Creating project with data:",a),console.log("Using API URL:",`${this.API_BASE_URL}/api/audit/contract`);const f=yield fetch(`${this.API_BASE_URL}/api/audit/contract`,{method:"POST",headers:u,body:JSON.stringify({name:a.name,description:a.description,template:a.template,network:a.network,contract_code:o,user_id:r})});if(!f.ok){const g=yield f.text();throw console.error("Failed to create project:",f.statusText,g),new Error(`HTTP ${f.status}: ${f.statusText}`)}const m=yield f.json();if(console.log("Project creation response:",m),m.success&&m.data)return m.data;throw new Error(m.error||"Unknown error occurred")}catch(o){throw console.error("Error creating project:",o),o}})}static getUserProjects(a,r){return E(this,null,function*(){try{const l=yield this.getAuthHeaders(r),o=yield fetch(`${this.API_BASE_URL}/api/audit/history?limit=50`,{method:"GET",headers:l});if(!o.ok)return console.error("Failed to fetch projects:",o.statusText),[];const u=yield o.json();return u.success&&u.data?u.data.map(f=>({id:f.id,name:f.contract_name||`Contract ${f.id.slice(0,8)}`,description:f.description||"Smart contract project",template:"custom",network:f.chain||"ethereum",contract_code:f.contract_code,contract_address:f.contract_address,user_id:a,created_at:f.created_at,updated_at:f.updated_at||f.created_at})):[]}catch(l){return console.error("Error fetching projects:",l),[]}})}static updateProject(a,r){return E(this,null,function*(){try{return console.log("Project update requested:",a,r),null}catch(l){return console.error("Error updating project:",l),null}})}static deleteProject(a){return E(this,null,function*(){try{return console.log("Project delete requested:",a),!1}catch(r){return console.error("Error deleting project:",r),!1}})}static getProject(a,r){return E(this,null,function*(){try{const l=yield this.getAuthHeaders(r),o=yield fetch(`${this.API_BASE_URL}/api/audit/results/${a}`,{method:"GET",headers:l});if(!o.ok)return console.error("Failed to fetch project:",o.statusText),null;const u=yield o.json();return u.success&&u.data?{id:u.data.id,name:u.data.contract_name||`Contract ${u.data.id.slice(0,8)}`,description:u.data.description||"Smart contract project",template:"custom",network:u.data.chain||"ethereum",contract_code:u.data.contract_code,contract_address:u.data.contract_address,user_id:u.data.user_id,created_at:u.data.created_at,updated_at:u.data.updated_at||u.data.created_at}:null}catch(l){return console.error("Error fetching project:",l),null}})}}xp(Rd,"API_BASE_URL","https://flash-audit-app.web.app/api");class cd{static createScan(a,r,l){return E(this,null,function*(){try{const{data:o,error:u}=yield li.from("vulnerability_scans").insert({user_id:l,contract_address:a,network:r,status:"pending"}).select().single();return u?(console.error("Error creating vulnerability scan:",u),null):o}catch(o){return console.error("Error creating vulnerability scan:",o),null}})}static updateScanResults(a,r,l){return E(this,null,function*(){try{const{data:o,error:u}=yield li.from("vulnerability_scans").update({scan_results:r,status:l}).eq("id",a).select().single();return u?(console.error("Error updating scan results:",u),null):o}catch(o){return console.error("Error updating scan results:",o),null}})}static getUserScans(a){return E(this,null,function*(){try{const{data:r,error:l}=yield li.from("vulnerability_scans").select("*").eq("user_id",a).order("created_at",{ascending:!1});return l?(console.error("Error fetching vulnerability scans:",l),[]):r||[]}catch(r){return console.error("Error fetching vulnerability scans:",r),[]}})}static getScan(a){return E(this,null,function*(){try{const{data:r,error:l}=yield li.from("vulnerability_scans").select("*").eq("id",a).single();return l?(console.error("Error fetching vulnerability scan:",l),null):r}catch(r){return console.error("Error fetching vulnerability scan:",r),null}})}}function g2(){console.log("App component rendering...");let s,a=null;try{s=hv().getToken,console.log("Clerk auth loaded successfully")}catch(_){console.error("Error loading Clerk auth:",_)}try{a=p2().userId,console.log("Supabase auth initialized:",{userId:a?"Present":"None"})}catch(_){console.error("Error loading Supabase auth:",_)}const[r,l]=ee.useState("dashboard"),o=ee.useRef(new class{performScan(N,D){return E(this,null,function*(){var L,Q,he;try{if(D==null||D(10,"initializing"),!s)throw new Error("Authentication not available");const we=yield s();if(!we)throw new Error("Authentication required");D==null||D(20,"connecting");const Je={contractAddress:N.contractAddress,chain:N.networkId,agents:["security","gas-optimization","compliance"],analysisMode:"comprehensive",includeCrossChain:!1};D==null||D(30,"starting analysis");let pt;try{const wt=yield fetch("https://flash-audit-app.web.app/api/api/audit/address",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${we}`},body:JSON.stringify(Je)});if(!wt.ok)throw new Error("API request failed");if(pt=yield wt.json(),!pt.success)throw new Error(pt.error||"Audit failed")}catch(wt){return console.warn("Backend API not available, using demo data:",wt),D==null||D(90,"completing analysis"),yield new Promise(Be=>setTimeout(Be,1e3)),D==null||D(100,"complete"),{vulnerabilities:[{name:"Demo Vulnerability",affectedLines:"1-10",description:"This is a demo vulnerability for testing purposes. Backend API is not available.",severity:"medium",fixSuggestion:"This is demo data - deploy backend functions to get real analysis."}],securityScore:75,riskCategory:{label:"medium",justification:"Demo data - backend not deployed"},codeInsights:{gasOptimizationTips:["Demo: Backend API not available - upgrade to Blaze plan to deploy functions"],antiPatternNotices:["Demo: This is test data"],dangerousUsage:["Demo: Backend functions not deployed"]}}}if(D==null||D(50,"analyzing"),pt&&pt.auditId){const wt=pt.auditId;let Be=0;const qt=30;for(;Be<qt;){yield new Promise(Tn=>setTimeout(Tn,1e3)),Be++;const bn=50+Be/qt*40;D==null||D(bn,"analyzing");try{const Tn=yield fetch(`https://flash-audit-app.web.app/api/api/audit/results/${wt}`,{headers:{Authorization:`Bearer ${we}`}});if(Tn.ok){const Ht=yield Tn.json();if(Ht.success&&Ht.data&&Ht.data.status==="completed")return D==null||D(100,"completed"),{success:!0,data:{scanId:wt,contractAddress:N.contractAddress,networkId:N.networkId,vulnerabilities:Ht.data.vulnerabilities||[],gasOptimizations:Ht.data.gasOptimizations||[],securityScore:Ht.data.securityScore||0,riskLevel:((L=Ht.data.riskCategory)==null?void 0:L.label)||"Unknown",complianceChecks:Ht.data.complianceChecks||{},summary:{overallRisk:((Q=Ht.data.riskCategory)==null?void 0:Q.label)||"Unknown",totalVulnerabilities:(Ht.data.vulnerabilities||[]).length,gasOptimizationSavings:((he=Ht.data.gasOptimizations)==null?void 0:he.reduce((ia,ms)=>ia+(ms.savings||0),0))||0}}}}}catch(Tn){console.warn("Status check failed:",Tn)}}throw new Error("Analysis timeout - please try again")}else throw new Error("Invalid audit response")}catch(we){throw console.error("Vulnerability scan error:",we),we}})}}).current,u=ee.useRef(new class{addLog(N,D,L){const Q={timestamp:new Date().toLocaleTimeString(),level:N,message:D,source:L};te(he=>[...he,Q])}startDevelopmentSession(){return E(this,null,function*(){try{this.addLog("info","Starting development session...","realtime");const N=yield s();if(!N){this.addLog("error","Authentication required for development session","realtime");return}const D=yield fetch("https://flash-audit-app.web.app/api/api/v1/realtime/session/start",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${N}`},body:JSON.stringify({enableInstantFeedback:!0,enableLiveVulnerabilityDetection:!0,enableAIDetection:!0,alertLevel:"medium",realTimeAlerts:!0})});if(D.ok){const L=yield D.json();this.addLog("success",L.message||"Development session started","realtime")}else this.addLog("error","Failed to start development session","realtime")}catch(N){this.addLog("error",`Development session error: ${N}`,"realtime")}})}getServiceStatus(){return E(this,null,function*(){try{this.addLog("info","Checking service status...","system");const N=yield s();if(!N){this.addLog("warning","Authentication required for full status check","system");return}const D=[{name:"ChainIDE",endpoint:"/api/v1/chainide/status"},{name:"Real-time Development",endpoint:"/api/v1/realtime/metrics"},{name:"Collaboration Tools",endpoint:"/api/v1/collaboration/status"}];for(const L of D)try{(yield fetch(`https://flash-audit-app.web.app/api${L.endpoint}`,{headers:{Authorization:`Bearer ${N}`}})).ok?this.addLog("success",`${L.name} service: Online`,"system"):this.addLog("warning",`${L.name} service: Degraded`,"system")}catch(Q){this.addLog("error",`${L.name} service: Offline`,"system")}}catch(N){this.addLog("error",`Status check failed: ${N}`,"system")}})}compileContract(N,D){return E(this,null,function*(){var L;try{this.addLog("info",`Compiling contract: ${D}...`,"compiler");const Q=yield s();if(!Q){this.addLog("error","Authentication required for compilation","compiler");return}const he=yield fetch("https://flash-audit-app.web.app/api/api/v1/realtime/validation",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${Q}`},body:JSON.stringify({content:N,filePath:`${D}.sol`})});if(he.ok){const we=yield he.json();we.success?(this.addLog("success","✓ Contract compiled successfully","compiler"),((L=we.data.warnings)==null?void 0:L.length)>0&&we.data.warnings.forEach(Je=>{this.addLog("warning",`⚠ ${Je.message}`,"compiler")})):this.addLog("error",`✗ Compilation failed: ${we.error}`,"compiler")}else this.addLog("error","✗ Compilation service unavailable","compiler")}catch(Q){this.addLog("error",`Compilation error: ${Q}`,"compiler")}})}clearLogs(){te([{timestamp:new Date().toLocaleTimeString(),level:"info",message:"Terminal cleared",source:"system"}])}}).current,[f,m]=ee.useState("explorer"),[g,v]=ee.useState(!1),[y,S]=ee.useState(null),[x]=ee.useState("Ethereum"),[C,U]=ee.useState(""),[Y,V]=ee.useState(""),[F,J]=ee.useState("All"),[q,te]=ee.useState([{timestamp:new Date().toLocaleTimeString(),level:"info",message:"FlashAudit IDE initialized successfully!",source:"system"},{timestamp:new Date().toLocaleTimeString(),level:"info",message:"Backend services connected.",source:"system"}]),[K,W]=ee.useState([]),[le,ne]=ee.useState(null),[ye,ge]=ee.useState({}),[ue,Ae]=ee.useState(null),[Le,Qe]=ee.useState(!1),[Me,z]=ee.useState(new Set(["contracts","test","scripts"])),[G,ae]=ee.useState(null),[xe,j]=ee.useState(""),[I,X]=ee.useState(1),[Z,re]=ee.useState(""),[ve,de]=ee.useState("ethereum"),[oe,Pe]=ee.useState(null),[Bt,an]=ee.useState(!1),[vn,ea]=ee.useState(0),[Ne,Oe]=ee.useState("idle"),[rt,ze]=ee.useState(null),[Ze,Xt]=ee.useState([]);ee.useEffect(()=>{E(null,null,function*(){if(a&&s)try{const D=(yield Rd.getUserProjects(a,s)).map(L=>{var Q,he,we,Je;return{id:L.id,name:L.name,description:(Q=L.description)!=null?Q:null,user_id:L.user_id,created_at:L.created_at,updated_at:L.updated_at,project_data:(he=L.project_data)!=null?he:{},status:(we=L.status)!=null?we:"active",type:(Je=L.type)!=null?Je:"contract"}});Xt(D)}catch(N){console.error("Failed to load projects:",N),Xt([])}else Xt([])})},[a,s]);const nt=[{id:"ethereum",name:"Ethereum",icon:"⟠",color:"#627eea"},{id:"polygon",name:"Polygon",icon:"🔷",color:"#8247e5"},{id:"sui",name:"Sui",icon:"💧",color:"#4da2ff"},{id:"aptos",name:"Aptos",icon:"🅰️",color:"#00d4aa"}],ut=[{id:"blank",name:"Blank Template",version:"v1.0.0",description:"This is a blank template for the Ethereum blockchain.",category:"Basic",network:"Ethereum"},{id:"storage",name:"Storage",version:"v1.0.0",description:"This template constructs a skeleton contract used to persist values on the blockchain.",category:"Basic",network:"Ethereum"},{id:"hello-world",name:"Hello World",version:"v1.0.1",description:'This template constructs a smart contract that returns the "Hello World" message to the contract deployer.',category:"Basic",network:"Ethereum"},{id:"developer-learning",name:"Developer Learning Resource",version:"v1.0.0",description:"This is the developer learning resource helping developers to get started with the EVM blockchain.",category:"Learning",network:"Ethereum"},{id:"ballot",name:"Ballot",version:"v1.0.1",description:'This template constructs a skeleton contract with the "voting with delegation" feature.',category:"Governance",network:"Ethereum"},{id:"erc20-showcase",name:"ERC-20 Showcase",version:"v1.0.1",description:"ERC-20 has emerged as a technical standard for all smart contracts on the Ethereum blockchain for a fungible token implementation.",category:"Token",network:"Ethereum"},{id:"erc721-showcase",name:"ERC-721 Showcase",version:"v1.0.4",description:"ERC-721 is a data standard for creating non-fungible tokens, meaning each token is unique and cannot be divided or directly exchanged for another ERC-721.",category:"NFT",network:"Ethereum"},{id:"erc1155-showcase",name:"ERC-1155 Showcase",version:"v1.0.0",description:"ERC-1155 is a standard interface for contracts that manage multiple token types.",category:"Token",network:"Ethereum"},{id:"erc3525-showcase",name:"ERC-3525 Showcase",version:"v1.0.1",description:"ERC-3525 has emerged as a technical standard for all smart contracts on the Ethereum blockchain for a fungible token implementation.",category:"Token",network:"Ethereum"},{id:"erc4907-showcase",name:"ERC-4907 Showcase",version:"v1.0.0",description:"ERC-4907 is a data standard for creating non-fungible tokens, meaning each token is unique and cannot be divided or directly exchanged for another ERC-4907.",category:"NFT",network:"Ethereum"},{id:"hardhat-dapp-wrap",name:"Hardhat dApp Wrap",version:"v1.0.0",description:"This template constructs a skeleton contract used to persist values on the blockchain.",category:"Framework",network:"Ethereum"},{id:"blank-polygon",name:"Blank Template",version:"v1.0.0",description:"This is a blank template for the Polygon blockchain with low gas fees.",category:"Basic",network:"Polygon"},{id:"polygon-nft",name:"Polygon NFT Collection",version:"v1.2.0",description:"Create NFT collections on Polygon with minimal gas costs and OpenSea compatibility.",category:"NFT",network:"Polygon"},{id:"polygon-defi",name:"DeFi Yield Farm",version:"v1.1.0",description:"Decentralized finance yield farming contract optimized for Polygon network.",category:"DeFi",network:"Polygon"},{id:"polygon-gaming",name:"Gaming Assets",version:"v1.0.0",description:"Gaming asset management contract with fast transactions on Polygon.",category:"Gaming",network:"Polygon"},{id:"polygon-marketplace",name:"NFT Marketplace",version:"v1.3.0",description:"Complete NFT marketplace with low-cost trading on Polygon network.",category:"Marketplace",network:"Polygon"},{id:"blank-sui",name:"Blank Template",version:"v1.0.0",description:"This is a blank template for the Sui blockchain using Move language.",category:"Basic",network:"Sui"},{id:"sui-coin",name:"Sui Coin",version:"v1.0.0",description:"Create custom coins on Sui blockchain with Move programming language.",category:"Token",network:"Sui"},{id:"sui-nft",name:"Sui NFT Collection",version:"v1.1.0",description:"NFT collection template for Sui blockchain with object-centric design.",category:"NFT",network:"Sui"},{id:"sui-defi",name:"Sui DeFi Protocol",version:"v1.0.0",description:"DeFi protocol template leveraging Sui's parallel execution capabilities.",category:"DeFi",network:"Sui"},{id:"sui-gaming",name:"Sui Gaming Objects",version:"v1.0.0",description:"Gaming objects and assets management using Sui's object model.",category:"Gaming",network:"Sui"},{id:"blank-aptos",name:"Blank Template",version:"v1.0.0",description:"This is a blank template for the Aptos blockchain using Move language.",category:"Basic",network:"Aptos"},{id:"aptos-coin",name:"Aptos Coin",version:"v1.0.0",description:"Create custom coins on Aptos blockchain with advanced Move features.",category:"Token",network:"Aptos"},{id:"aptos-nft",name:"Aptos NFT Collection",version:"v1.1.0",description:"NFT collection template for Aptos with token standard compliance.",category:"NFT",network:"Aptos"},{id:"aptos-defi",name:"Aptos DeFi Protocol",version:"v1.0.0",description:"DeFi protocol template with Aptos Move's safety features.",category:"DeFi",network:"Aptos"},{id:"aptos-dao",name:"Aptos DAO",version:"v1.0.0",description:"Decentralized Autonomous Organization template for Aptos governance.",category:"Governance",network:"Aptos"},{id:"flow-nft",name:"Flow NFT Collection",version:"v1.0.0",description:"NFT collection template for Flow blockchain using Cadence language.",category:"NFT",network:"Flow"},{id:"flow-defi",name:"Flow DeFi Protocol",version:"v1.0.0",description:"DeFi protocol template leveraging Flow's resource-oriented programming.",category:"DeFi",network:"Flow"},{id:"conflux-dapp",name:"Conflux dApp",version:"v1.0.0",description:"Decentralized application template for Conflux network with tree-graph consensus.",category:"dApp",network:"Conflux"},{id:"conflux-defi",name:"Conflux DeFi",version:"v1.0.0",description:"DeFi protocol optimized for Conflux's high throughput and low fees.",category:"DeFi",network:"Conflux"},{id:"ic-canister",name:"IC Canister",version:"v1.0.0",description:"Internet Computer canister template using Motoko programming language.",category:"Canister",network:"Internet Computer"},{id:"ic-defi",name:"IC DeFi Protocol",version:"v1.0.0",description:"DeFi protocol for Internet Computer with web-speed transactions.",category:"DeFi",network:"Internet Computer"},{id:"nexus-smart-contract",name:"Nexus Smart Contract",version:"v1.0.0",description:"Smart contract template for Nexus blockchain with quantum resistance.",category:"Contract",network:"Nexus"},{id:"fisco-enterprise",name:"FISCO Enterprise Contract",version:"v1.0.0",description:"Enterprise-grade smart contract for FISCO BCOS consortium blockchain.",category:"Enterprise",network:"FISCO BCOS"},{id:"xdc-trade-finance",name:"XDC Trade Finance",version:"v1.0.0",description:"Trade finance smart contract optimized for XDC Network.",category:"Finance",network:"XDC"},{id:"astar-dapp",name:"Astar dApp",version:"v1.0.0",description:"Multi-chain dApp template for Astar Network supporting EVM and WASM.",category:"dApp",network:"Astar"}],Rt=()=>E(null,null,function*(){window.prompt('Choose wallet: type "metamask" for MetaMask or "phantom" for Phantom',"metamask")==="phantom"?yield ta():yield Da()}),Da=()=>E(null,null,function*(){try{if(typeof window.ethereum!="undefined"){const _=yield window.ethereum.request({method:"eth_requestAccounts"});_.length>0&&(v(!0),S("metamask"),console.log("MetaMask connected:",_[0]))}else alert("MetaMask not detected. Please install MetaMask.")}catch(_){console.error("Failed to connect MetaMask:",_)}}),ta=()=>E(null,null,function*(){try{if(typeof window.solana!="undefined"&&window.solana.isPhantom){const _=yield window.solana.connect();_.publicKey&&(v(!0),S("phantom"),console.log("Phantom connected:",_.publicKey.toString()))}else alert("Phantom wallet not detected. Please install Phantom wallet.")}catch(_){console.error("Failed to connect Phantom:",_)}}),ci=()=>E(null,null,function*(){try{y==="phantom"&&window.solana&&(yield window.solana.disconnect()),v(!1),S(null),console.log("Wallet disconnected")}catch(_){console.error("Failed to disconnect wallet:",_)}}),Ct=_=>{switch(_.id){case"blank":case"blank-polygon":case"blank-sui":case"blank-aptos":return["contracts/BlankContract.sol","README.md","package.json"];case"storage":return["contracts/Storage.sol","contracts/Migrations.sol","README.md","package.json"];case"hello-world":return["contracts/HelloWorld.sol","contracts/Migrations.sol","test/HelloWorld.test.js","README.md","package.json"];case"erc20-showcase":return["contracts/MyToken.sol","contracts/IERC20.sol","test/MyToken.test.js","README.md","package.json"];case"erc721-showcase":return["contracts/MyNFT.sol","contracts/IERC721.sol","test/MyNFT.test.js","README.md","package.json"];case"polygon-nft":return["contracts/PolygonNFT.sol","contracts/interfaces/IERC721.sol","test/PolygonNFT.test.js","scripts/deploy.js","README.md","package.json"];case"polygon-defi":return["contracts/YieldFarm.sol","contracts/RewardToken.sol","test/YieldFarm.test.js","scripts/deploy.js","README.md","package.json"];case"polygon-gaming":return["contracts/GameAssets.sol","contracts/GameItems.sol","test/GameAssets.test.js","scripts/deploy.js","README.md","package.json"];case"polygon-marketplace":return["contracts/NFTMarketplace.sol","contracts/MarketplaceToken.sol","test/NFTMarketplace.test.js","scripts/deploy.js","README.md","package.json"];case"sui-coin":return["sources/coin.move","sources/coin_tests.move","Move.toml","README.md"];case"sui-nft":return["sources/nft.move","sources/nft_tests.move","Move.toml","README.md"];case"sui-defi":return["sources/defi.move","sources/pool.move","sources/defi_tests.move","Move.toml","README.md"];case"sui-gaming":return["sources/game_objects.move","sources/game_logic.move","sources/game_tests.move","Move.toml","README.md"];case"aptos-coin":return["sources/coin.move","sources/coin_tests.move","Move.toml","README.md"];case"aptos-nft":return["sources/nft_collection.move","sources/nft_tests.move","Move.toml","README.md"];case"aptos-defi":return["sources/defi_protocol.move","sources/liquidity_pool.move","sources/defi_tests.move","Move.toml","README.md"];case"aptos-dao":return["sources/dao.move","sources/governance.move","sources/dao_tests.move","Move.toml","README.md"];case"flow-nft":return["contracts/NFTContract.cdc","transactions/mint_nft.cdc","scripts/get_nft.cdc","README.md"];case"flow-defi":return["contracts/DeFiProtocol.cdc","contracts/FlowToken.cdc","transactions/swap.cdc","README.md"];case"conflux-dapp":case"conflux-defi":return["contracts/ConfluxContract.sol","test/ConfluxContract.test.js","README.md","package.json"];case"ic-canister":case"ic-defi":return["src/main.mo","dfx.json","README.md"];case"nexus-smart-contract":return["contracts/NexusContract.cpp","README.md","CMakeLists.txt"];case"fisco-enterprise":return["contracts/EnterpriseContract.sol","test/EnterpriseContract.test.js","README.md","package.json"];case"xdc-trade-finance":return["contracts/TradeFinance.sol","test/TradeFinance.test.js","README.md","package.json"];case"astar-dapp":return["contracts/AstarContract.sol","ink/lib.rs","test/AstarContract.test.js","README.md","package.json"];default:return["contracts/Contract.sol","README.md","package.json"]}},bt=(_,N)=>N.endsWith(".sol")?di(_):N.endsWith(".move")?wo(_,N):N==="Move.toml"?mt(_):N==="README.md"?ht(_):N==="package.json"?sn(_):N.endsWith(".test.js")?Un(_):N.endsWith("deploy.js")?So(_):"// File content will be loaded here",ht=_=>{const D={Polygon:`## Polygon Network

This project is optimized for Polygon with low gas fees and fast transactions.

### Deployment
- Polygon Mainnet: Low cost transactions
- Mumbai Testnet: Free testing environment`,Sui:`## Sui Network

This project uses Move language on Sui blockchain.

### Features
- Object-centric programming model
- Parallel execution
- Low latency transactions`,Aptos:`## Aptos Network

This project uses Move language on Aptos blockchain.

### Features
- Move language safety
- High throughput
- Formal verification support`,Flow:`## Flow Network

This project uses Cadence language on Flow blockchain.

### Features
- Resource-oriented programming
- Built-in security
- Developer-friendly tools`,Conflux:`## Conflux Network

This project leverages Conflux's tree-graph consensus.

### Features
- High throughput
- Low transaction fees
- EVM compatibility`,"Internet Computer":`## Internet Computer

This project runs on the Internet Computer Protocol.

### Features
- Web-speed transactions
- Reverse gas model
- Direct web serving`,Nexus:`## Nexus Network

This project is built for Nexus blockchain.

### Features
- Quantum resistance
- 3D blockchain architecture
- Sustainable consensus`,"FISCO BCOS":`## FISCO BCOS

This project is designed for enterprise consortium blockchain.

### Features
- Enterprise-grade security
- High performance
- Regulatory compliance`,XDC:`## XDC Network

This project is optimized for XDC Network.

### Features
- Enterprise-ready
- Trade finance focus
- Interoperability`,Astar:`## Astar Network

This project supports both EVM and WASM on Astar.

### Features
- Multi-chain compatibility
- dApp staking
- Cross-chain messaging`}[_.network]||"",L=_.network==="Internet Computer"?'1. Install dfx: `sh -ci "$(curl -fsSL https://sdk.dfinity.org/install.sh)"`\n2. Start local replica: `dfx start`\n3. Deploy canister: `dfx deploy`':_.network==="Flow"?'1. Install Flow CLI: `sh -ci "$(curl -fsSL https://storage.googleapis.com/flow-cli/install.sh)"`\n2. Start emulator: `flow emulator start`\n3. Deploy contracts: `flow project deploy`':"1. Install dependencies: `npm install`\n2. Compile contracts: `npm run compile`\n3. Run tests: `npm test`\n4. Deploy: `npm run deploy`";return`# ${_.name}

${_.description}

${D}

## Getting Started

${L}

## Network: ${_.network}
## Category: ${_.category}
## Version: ${_.version}`},sn=_=>{const N={name:_.name.toLowerCase().replace(/\s+/g,"-"),version:"1.0.0",description:_.description,scripts:{compile:"hardhat compile",test:"hardhat test",deploy:"hardhat run scripts/deploy.js"},dependencies:{"@openzeppelin/contracts":"^4.9.0",hardhat:"^2.17.0"}};return _.network==="Polygon"&&(N.dependencies["@polygonlabs/fx-portal"]="^1.0.3",N.dependencies["@maticnetwork/meta-transactions"]="^2.0.0"),JSON.stringify(N,null,2)},mt=_=>`[package]
name = "${_.name.toLowerCase().replace(/\s+/g,"_")}"
version = "1.0.0"
authors = ["Your Name <<EMAIL>>"]

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/testnet" }

[dev-dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/testnet" }

[addresses]
${_.name.toLowerCase().replace(/\s+/g,"_")} = "0x0"
sui = "0x2"`,So=_=>`const hre = require("hardhat");

async function main() {
  console.log("Deploying ${_.name} to ${_.network}...");

  const Contract = await hre.ethers.getContractFactory("${_.name.replace(/\s+/g,"")}");
  const contract = await Contract.deploy();

  await contract.deployed();

  console.log("${_.name} deployed to:", contract.address);
  console.log("Network:", "${_.network}");
  console.log("Transaction hash:", contract.deployTransaction.hash);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });`,wo=(_,N)=>_.network==="Sui"?jo(_,N):_.network==="Aptos"?ui(_,N):"// Move file content",jo=(_,N)=>N.includes("coin")?`module ${_.name.toLowerCase().replace(/\s+/g,"_")}::coin {
    use sui::coin::{Self, Coin, TreasuryCap};
    use sui::url::{Self, Url};

    /// The type identifier of coin. The coin will have a type
    /// tag of kind: \`Coin<package_object::mycoin::MYCOIN>\`
    /// Make sure that the name of the type matches the module's name.
    public struct COIN has drop {}

    /// Module initializer is called once on module publish. A treasury
    /// cap is sent to the publisher, who then controls minting and burning
    fun init(witness: COIN, ctx: &mut TxContext) {
        let (treasury, metadata) = coin::create_currency<COIN>(witness, 9, b"COIN", b"", b"", option::none(), ctx);
        transfer::public_freeze_object(metadata);
        transfer::public_transfer(treasury, tx_context::sender(ctx))
    }

    public entry fun mint(
        treasury_cap: &mut TreasuryCap<COIN>, amount: u64, recipient: address, ctx: &mut TxContext
    ) {
        coin::mint_and_transfer(treasury_cap, amount, recipient, ctx)
    }

    public entry fun burn(treasury_cap: &mut TreasuryCap<COIN>, coin: Coin<COIN>) {
        coin::burn(treasury_cap, coin);
    }
}`:N.includes("nft")?`module ${_.name.toLowerCase().replace(/\s+/g,"_")}::nft {
    use sui::url::{Self, Url};
    use std::string;
    use sui::object::{Self, ID, UID};
    use sui::event;

    /// An example NFT that can be minted by anybody
    public struct DevNetNFT has key, store {
        id: UID,
        /// Name for the token
        name: string::String,
        /// Description of the token
        description: string::String,
        /// URL for the token
        url: Url,
    }

    // ===== Events =====

    public struct NFTMinted has copy, drop {
        // The Object ID of the NFT
        object_id: ID,
        // The creator of the NFT
        creator: address,
        // The name of the NFT
        name: string::String,
    }

    // ===== Public view functions =====

    /// Get the NFT's \`name\`
    public fun name(nft: &DevNetNFT): &string::String {
        &nft.name
    }

    /// Get the NFT's \`description\`
    public fun description(nft: &DevNetNFT): &string::String {
        &nft.description
    }

    /// Get the NFT's \`url\`
    public fun url(nft: &DevNetNFT): &Url {
        &nft.url
    }

    // ===== Entrypoints =====

    /// Create a new devnet_nft
    public entry fun mint_to_sender(
        name: vector<u8>,
        description: vector<u8>,
        url: vector<u8>,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        let nft = DevNetNFT {
            id: object::new(ctx),
            name: string::utf8(name),
            description: string::utf8(description),
            url: url::new_unsafe_from_bytes(url)
        };

        event::emit(NFTMinted {
            object_id: object::id(&nft),
            creator: sender,
            name: nft.name,
        });

        transfer::public_transfer(nft, sender);
    }
}`:"// Move module content",ui=(_,N)=>N.includes("coin")?`module ${_.name.toLowerCase().replace(/\s+/g,"_")}::coin {
    use std::string;
    use aptos_framework::coin;

    struct MyCoin {}

    fun init_module(sender: &signer) {
        let name = string::utf8(b"My Coin");
        let symbol = string::utf8(b"MC");
        let decimals = 8;
        let monitor_supply = true;

        coin::initialize<MyCoin>(
            sender,
            name,
            symbol,
            decimals,
            monitor_supply,
        );
    }

    public entry fun mint(sender: &signer, amount: u64) {
        let coins = coin::mint<MyCoin>(amount, sender);
        coin::deposit(signer::address_of(sender), coins);
    }
}`:N.includes("nft")?`module ${_.name.toLowerCase().replace(/\s+/g,"_")}::nft {
    use std::string::{Self, String};
    use std::vector;
    use aptos_framework::account;
    use aptos_token::token;

    struct NFTCollection has key {
        collection_name: String,
        description: String,
        uri: String,
        maximum: u64,
        mutate_setting: vector<bool>,
    }

    public entry fun create_collection(
        creator: &signer,
        name: String,
        description: String,
        uri: String,
        maximum: u64,
    ) {
        let mutate_setting = vector<bool>[false, false, false];

        token::create_collection(
            creator,
            name,
            description,
            uri,
            maximum,
            mutate_setting,
        );
    }

    public entry fun mint_nft(
        creator: &signer,
        collection_name: String,
        token_name: String,
        description: String,
        uri: String,
        amount: u64,
    ) {
        let mutate_setting = vector<bool>[false, false, false, false, false];

        token::create_token_script(
            creator,
            collection_name,
            token_name,
            description,
            amount,
            uri,
            signer::address_of(creator),
            1,
            0,
            mutate_setting,
            vector<String>[],
            vector<vector<u8>>[],
            vector<String>[],
        );
    }
}`:"// Aptos Move module content",di=(_,N)=>{switch(_.id){case"hello-world":return`// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

contract HelloWorld {
    string public message;

    constructor() {
        message = "Hello, World!";
    }

    function setMessage(string memory _message) public {
        message = _message;
    }

    function getMessage() public view returns (string memory) {
        return message;
    }
}`;case"storage":return`// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

contract Storage {
    uint256 private storedData;

    function set(uint256 x) public {
        storedData = x;
    }

    function get() public view returns (uint256) {
        return storedData;
    }
}`;case"erc20-showcase":return`// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract MyToken is ERC20 {
    constructor() ERC20("MyToken", "MTK") {
        _mint(msg.sender, 1000000 * 10 ** decimals());
    }
}`;case"polygon-nft":return`// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

contract PolygonNFT is ERC721, ERC721URIStorage, Ownable {
    using Counters for Counters.Counter;
    Counters.Counter private _tokenIdCounter;

    uint256 public constant MAX_SUPPLY = 10000;
    uint256 public constant MINT_PRICE = 0.01 ether; // Low cost on Polygon

    constructor() ERC721("PolygonNFT", "PNFT") {}

    function safeMint(address to, string memory uri) public payable {
        require(_tokenIdCounter.current() < MAX_SUPPLY, "Max supply reached");
        require(msg.value >= MINT_PRICE, "Insufficient payment");

        uint256 tokenId = _tokenIdCounter.current();
        _tokenIdCounter.increment();
        _safeMint(to, tokenId);
        _setTokenURI(tokenId, uri);
    }

    function withdraw() public onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }

    // Override required functions
    function _burn(uint256 tokenId) internal override(ERC721, ERC721URIStorage) {
        super._burn(tokenId);
    }

    function tokenURI(uint256 tokenId) public view override(ERC721, ERC721URIStorage) returns (string memory) {
        return super.tokenURI(tokenId);
    }
}`;case"polygon-defi":return`// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

contract YieldFarm is ReentrancyGuard, Ownable {
    IERC20 public stakingToken;
    IERC20 public rewardToken;

    uint256 public rewardRate = 100; // tokens per second
    uint256 public lastUpdateTime;
    uint256 public rewardPerTokenStored;

    mapping(address => uint256) public userRewardPerTokenPaid;
    mapping(address => uint256) public rewards;
    mapping(address => uint256) public balances;

    uint256 private _totalSupply;

    event Staked(address indexed user, uint256 amount);
    event Withdrawn(address indexed user, uint256 amount);
    event RewardPaid(address indexed user, uint256 reward);

    constructor(address _stakingToken, address _rewardToken) {
        stakingToken = IERC20(_stakingToken);
        rewardToken = IERC20(_rewardToken);
    }

    modifier updateReward(address account) {
        rewardPerTokenStored = rewardPerToken();
        lastUpdateTime = block.timestamp;
        if (account != address(0)) {
            rewards[account] = earned(account);
            userRewardPerTokenPaid[account] = rewardPerTokenStored;
        }
        _;
    }

    function rewardPerToken() public view returns (uint256) {
        if (_totalSupply == 0) {
            return rewardPerTokenStored;
        }
        return rewardPerTokenStored + (((block.timestamp - lastUpdateTime) * rewardRate * 1e18) / _totalSupply);
    }

    function earned(address account) public view returns (uint256) {
        return ((balances[account] * (rewardPerToken() - userRewardPerTokenPaid[account])) / 1e18) + rewards[account];
    }

    function stake(uint256 amount) external nonReentrant updateReward(msg.sender) {
        require(amount > 0, "Cannot stake 0");
        _totalSupply += amount;
        balances[msg.sender] += amount;
        stakingToken.transferFrom(msg.sender, address(this), amount);
        emit Staked(msg.sender, amount);
    }

    function withdraw(uint256 amount) public nonReentrant updateReward(msg.sender) {
        require(amount > 0, "Cannot withdraw 0");
        _totalSupply -= amount;
        balances[msg.sender] -= amount;
        stakingToken.transfer(msg.sender, amount);
        emit Withdrawn(msg.sender, amount);
    }

    function getReward() public nonReentrant updateReward(msg.sender) {
        uint256 reward = rewards[msg.sender];
        if (reward > 0) {
            rewards[msg.sender] = 0;
            rewardToken.transfer(msg.sender, reward);
            emit RewardPaid(msg.sender, reward);
        }
    }

    function exit() external {
        withdraw(balances[msg.sender]);
        getReward();
    }
}`;case"polygon-marketplace":return`// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

contract NFTMarketplace is ReentrancyGuard, Ownable {
    struct Listing {
        uint256 price;
        address seller;
        bool active;
    }

    struct Auction {
        uint256 startingBid;
        uint256 highestBid;
        address highestBidder;
        uint256 endTime;
        bool active;
    }

    mapping(address => mapping(uint256 => Listing)) public listings;
    mapping(address => mapping(uint256 => Auction)) public auctions;

    uint256 public marketplaceFee = 250; // 2.5%
    uint256 public constant PERCENTAGE_BASE = 10000;

    event ItemListed(address indexed nftContract, uint256 indexed tokenId, uint256 price, address indexed seller);
    event ItemSold(address indexed nftContract, uint256 indexed tokenId, uint256 price, address seller, address buyer);
    event AuctionCreated(address indexed nftContract, uint256 indexed tokenId, uint256 startingBid, uint256 endTime);
    event BidPlaced(address indexed nftContract, uint256 indexed tokenId, uint256 bid, address bidder);

    function listItem(address nftContract, uint256 tokenId, uint256 price) external {
        require(price > 0, "Price must be greater than 0");
        require(IERC721(nftContract).ownerOf(tokenId) == msg.sender, "Not the owner");
        require(IERC721(nftContract).isApprovedForAll(msg.sender, address(this)), "Contract not approved");

        listings[nftContract][tokenId] = Listing(price, msg.sender, true);
        emit ItemListed(nftContract, tokenId, price, msg.sender);
    }

    function buyItem(address nftContract, uint256 tokenId) external payable nonReentrant {
        Listing memory listing = listings[nftContract][tokenId];
        require(listing.active, "Item not listed");
        require(msg.value >= listing.price, "Insufficient payment");

        listings[nftContract][tokenId].active = false;

        uint256 fee = (listing.price * marketplaceFee) / PERCENTAGE_BASE;
        uint256 sellerAmount = listing.price - fee;

        payable(listing.seller).transfer(sellerAmount);
        payable(owner()).transfer(fee);

        IERC721(nftContract).transferFrom(listing.seller, msg.sender, tokenId);

        emit ItemSold(nftContract, tokenId, listing.price, listing.seller, msg.sender);
    }

    function createAuction(address nftContract, uint256 tokenId, uint256 startingBid, uint256 duration) external {
        require(IERC721(nftContract).ownerOf(tokenId) == msg.sender, "Not the owner");
        require(startingBid > 0, "Starting bid must be greater than 0");

        auctions[nftContract][tokenId] = Auction(
            startingBid,
            0,
            address(0),
            block.timestamp + duration,
            true
        );

        emit AuctionCreated(nftContract, tokenId, startingBid, block.timestamp + duration);
    }

    function placeBid(address nftContract, uint256 tokenId) external payable nonReentrant {
        Auction storage auction = auctions[nftContract][tokenId];
        require(auction.active, "Auction not active");
        require(block.timestamp < auction.endTime, "Auction ended");
        require(msg.value > auction.highestBid, "Bid too low");

        if (auction.highestBidder != address(0)) {
            payable(auction.highestBidder).transfer(auction.highestBid);
        }

        auction.highestBid = msg.value;
        auction.highestBidder = msg.sender;

        emit BidPlaced(nftContract, tokenId, msg.value, msg.sender);
    }

    function endAuction(address nftContract, uint256 tokenId) external nonReentrant {
        Auction storage auction = auctions[nftContract][tokenId];
        require(auction.active, "Auction not active");
        require(block.timestamp >= auction.endTime, "Auction still ongoing");

        auction.active = false;

        if (auction.highestBidder != address(0)) {
            uint256 fee = (auction.highestBid * marketplaceFee) / PERCENTAGE_BASE;
            uint256 sellerAmount = auction.highestBid - fee;

            address seller = IERC721(nftContract).ownerOf(tokenId);
            payable(seller).transfer(sellerAmount);
            payable(owner()).transfer(fee);

            IERC721(nftContract).transferFrom(seller, auction.highestBidder, tokenId);
        }
    }
}`;case"polygon-gaming":return`// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

contract GameAssets is ERC721, Ownable, ReentrancyGuard {
    struct GameItem {
        uint256 itemType; // 1: Weapon, 2: Armor, 3: Consumable
        uint256 rarity; // 1: Common, 2: Rare, 3: Epic, 4: Legendary
        uint256 power;
        uint256 durability;
        bool tradeable;
    }

    mapping(uint256 => GameItem) public gameItems;
    mapping(address => uint256) public playerExperience;
    mapping(address => uint256) public playerLevel;

    IERC20 public gameToken;
    uint256 private _tokenIdCounter;

    event ItemMinted(address indexed player, uint256 indexed tokenId, uint256 itemType, uint256 rarity);
    event ItemUpgraded(uint256 indexed tokenId, uint256 newPower);
    event ExperienceGained(address indexed player, uint256 experience);

    constructor(address _gameToken) ERC721("GameAssets", "GAME") {
        gameToken = IERC20(_gameToken);
    }

    function mintItem(address to, uint256 itemType, uint256 rarity) external onlyOwner {
        uint256 tokenId = _tokenIdCounter++;
        uint256 power = calculatePower(itemType, rarity);

        gameItems[tokenId] = GameItem(
            itemType,
            rarity,
            power,
            100, // Full durability
            true // Tradeable by default
        );

        _mint(to, tokenId);
        emit ItemMinted(to, tokenId, itemType, rarity);
    }

    function upgradeItem(uint256 tokenId, uint256 gameTokenAmount) external nonReentrant {
        require(ownerOf(tokenId) == msg.sender, "Not the owner");
        require(gameTokenAmount > 0, "Invalid token amount");

        gameToken.transferFrom(msg.sender, address(this), gameTokenAmount);

        GameItem storage item = gameItems[tokenId];
        uint256 powerIncrease = gameTokenAmount / 1000; // 1000 tokens = 1 power
        item.power += powerIncrease;

        emit ItemUpgraded(tokenId, item.power);
    }

    function gainExperience(address player, uint256 experience) external onlyOwner {
        playerExperience[player] += experience;

        // Level up logic
        uint256 newLevel = calculateLevel(playerExperience[player]);
        if (newLevel > playerLevel[player]) {
            playerLevel[player] = newLevel;
        }

        emit ExperienceGained(player, experience);
    }

    function calculatePower(uint256 itemType, uint256 rarity) internal pure returns (uint256) {
        uint256 basePower = itemType * 10; // Weapons have higher base power
        uint256 rarityMultiplier = rarity * 25;
        return basePower + rarityMultiplier;
    }

    function calculateLevel(uint256 experience) internal pure returns (uint256) {
        if (experience < 1000) return 1;
        if (experience < 5000) return 2;
        if (experience < 15000) return 3;
        if (experience < 35000) return 4;
        return 5; // Max level
    }

    function repairItem(uint256 tokenId) external {
        require(ownerOf(tokenId) == msg.sender, "Not the owner");
        require(gameToken.transferFrom(msg.sender, address(this), 100), "Insufficient tokens");

        gameItems[tokenId].durability = 100;
    }

    function getItemDetails(uint256 tokenId) external view returns (GameItem memory) {
        return gameItems[tokenId];
    }
}`;default:return`// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

contract ${_.name.replace(/\s+/g,"")} {
    // Your contract code here
}`}},Un=(_,N)=>{switch(_.id){case"polygon-nft":return`const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("PolygonNFT", function () {
  let polygonNFT;
  let owner;
  let addr1;

  beforeEach(async function () {
    [owner, addr1] = await ethers.getSigners();
    const PolygonNFT = await ethers.getContractFactory("PolygonNFT");
    polygonNFT = await PolygonNFT.deploy();
    await polygonNFT.deployed();
  });

  it("Should mint NFT with correct price", async function () {
    const mintPrice = ethers.utils.parseEther("0.01");
    await polygonNFT.connect(addr1).safeMint(addr1.address, "ipfs://test", { value: mintPrice });
    expect(await polygonNFT.ownerOf(0)).to.equal(addr1.address);
  });

  it("Should fail to mint without sufficient payment", async function () {
    const insufficientPrice = ethers.utils.parseEther("0.005");
    await expect(
      polygonNFT.connect(addr1).safeMint(addr1.address, "ipfs://test", { value: insufficientPrice })
    ).to.be.revertedWith("Insufficient payment");
  });
});`;case"polygon-defi":return`const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("YieldFarm", function () {
  let yieldFarm;
  let stakingToken;
  let rewardToken;
  let owner;
  let user1;

  beforeEach(async function () {
    [owner, user1] = await ethers.getSigners();

    const Token = await ethers.getContractFactory("MockERC20");
    stakingToken = await Token.deploy("Staking Token", "STK");
    rewardToken = await Token.deploy("Reward Token", "RWD");

    const YieldFarm = await ethers.getContractFactory("YieldFarm");
    yieldFarm = await YieldFarm.deploy(stakingToken.address, rewardToken.address);

    await stakingToken.mint(user1.address, ethers.utils.parseEther("1000"));
    await rewardToken.mint(yieldFarm.address, ethers.utils.parseEther("10000"));
  });

  it("Should allow staking", async function () {
    const stakeAmount = ethers.utils.parseEther("100");
    await stakingToken.connect(user1).approve(yieldFarm.address, stakeAmount);
    await yieldFarm.connect(user1).stake(stakeAmount);

    expect(await yieldFarm.balances(user1.address)).to.equal(stakeAmount);
  });
};`;default:return`const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("${_.name}", function () {
  it("Should deploy successfully", async function () {
    const Contract = await ethers.getContractFactory("${_.name.replace(/\s+/g,"")}");
    const contract = await Contract.deploy();
    await contract.deployed();
    expect(contract.address).to.not.be.undefined;
  });
});`}},za=()=>{l("templates")},La=()=>E(null,null,function*(){var N;if(!Z.trim()){ze("Please enter a contract address");return}if(!a){ze("Please sign in to perform vulnerability scans");return}an(!0),Pe(null),ze(null),ea(0),Oe("initializing");let _=null;try{if(_=yield cd.createScan(Z.trim(),ve,a),!_)throw new Error("Failed to create scan record");const D={contractAddress:Z.trim(),networkId:ve,scanType:"standard",includeGasOptimization:!0,includeComplianceCheck:!0},L=(he,we)=>{ea(he),Oe(we)},Q=yield o.performScan(D,L);if(Q.success&&Q.data)console.log("Scan results received:",Q.data),Pe(Q.data),Oe("completed"),yield cd.updateScanResults(_.id,Q.data,"completed");else throw new Error(((N=Q.error)==null?void 0:N.message)||"Scan failed")}catch(D){console.error("Vulnerability scan failed:",D),ze(D.message||"Vulnerability scan failed. Please try again."),Oe("failed"),_&&(yield cd.updateScanResults(_.id,{error:D.message},"failed"))}finally{an(!1),ea(100)}}),us=_=>E(null,null,function*(){if(!oe){ze("No scan results to export");return}try{if(_==="json"){const N=JSON.stringify(oe,null,2),D=new Blob([N],{type:"application/json"}),L=URL.createObjectURL(D),Q=document.createElement("a");Q.href=L,Q.download=`vulnerability-report-${oe.scanId||Date.now()}.json`,document.body.appendChild(Q),Q.click(),document.body.removeChild(Q),URL.revokeObjectURL(L)}else if(_==="csv"){const N=Tr(oe),D=new Blob([N],{type:"text/csv"}),L=URL.createObjectURL(D),Q=document.createElement("a");Q.href=L,Q.download=`vulnerability-report-${oe.scanId||Date.now()}.csv`,document.body.appendChild(Q),Q.click(),document.body.removeChild(Q),URL.revokeObjectURL(L)}else if(_==="pdf"){const N=Cr(oe),D=new Blob([N],{type:"text/plain"}),L=URL.createObjectURL(D),Q=document.createElement("a");Q.href=L,Q.download=`vulnerability-report-${oe.scanId||Date.now()}.txt`,document.body.appendChild(Q),Q.click(),document.body.removeChild(Q),URL.revokeObjectURL(L)}}catch(N){ze(`Export failed: ${N.message}`)}}),Tr=_=>{const D=[["Type","Title","Severity","Description","Recommendation"]];return(_.vulnerabilities||[]).forEach(L=>{D.push(["Vulnerability",L.title||"",L.severity||"",L.description||"",L.recommendation||""])}),(_.gasOptimizations||_.gasOptimization||[]).forEach(L=>{D.push(["Gas Optimization",L.title||"","Info",L.description||"",`Save ${L.savings} gas`])}),D.map(L=>L.map(Q=>`"${String(Q).replace(/"/g,'""')}"`).join(",")).join(`
`)},Cr=_=>{var pt,wt;const N=((pt=_.summary)==null?void 0:pt.overallRisk)||_.overallRisk||"Unknown",D=_.contractAddress||"Unknown",L=((wt=_.networkInfo)==null?void 0:wt.name)||_.network||"Unknown",Q=new Date(_.timestamp||_.scanDate||Date.now()).toLocaleString();let he=`VULNERABILITY SCAN REPORT
`;he+=`========================

`,he+=`Contract: ${D}
`,he+=`Network: ${L}
`,he+=`Scan Date: ${Q}
`,he+=`Overall Risk: ${N}

`;const we=_.vulnerabilities||[];he+=`VULNERABILITIES (${we.length})
`,he+=`===============
`,we.length===0?he+=`No vulnerabilities detected.

`:we.forEach((Be,qt)=>{he+=`${qt+1}. ${Be.title} [${Be.severity}]
`,he+=`   Description: ${Be.description}
`,he+=`   Recommendation: ${Be.recommendation}
`,Be.line&&(he+=`   Line: ${Be.line}
`),he+=`
`});const Je=_.gasOptimizations||_.gasOptimization||[];return he+=`GAS OPTIMIZATIONS (${Je.length})
`,he+=`==================
`,Je.length===0?he+=`No gas optimizations found.

`:Je.forEach((Be,qt)=>{he+=`${qt+1}. ${Be.title}
`,he+=`   Description: ${Be.description}
`,he+=`   Savings: ${Be.savings} gas

`}),he},Dn=(_,N)=>{const D={name:_,files:N,timestamp:new Date().toISOString()};localStorage.setItem(`chainide-project-${_}`,JSON.stringify(D))},Pa=_=>{const N=localStorage.getItem(`chainide-project-${_}`);if(N)try{return JSON.parse(N).files}catch(D){console.error("Error loading project from storage:",D)}return null},Eo=_=>E(null,null,function*(){var N;if(console.log("🚀 createProjectFromTemplate called with template:",_.name),console.log("🔑 userId:",a),console.log("🔐 getToken available:",!!s),!a){alert("Please sign in to create a project");return}if(!s){alert("Authentication not ready. Please try again.");return}try{const D=Ct(_),L=D.find(Je=>Je.endsWith(".sol"))||D[0],Q=L?bt(_,L):void 0,he={name:`${_.name} ${Math.random().toString(36).substring(2,8)}`,description:_.description,template:_.name,network:"ethereum",contract_code:Q};console.log("Creating project with data:",he);const we=yield Rd.createProject(he,a,s);if(we){const Je={id:we.id,name:we.name,description:(N=we.description)!=null?N:null,user_id:we.user_id,created_at:we.created_at,updated_at:we.updated_at,project_data:{},status:"active",type:"contract"};Xt(Be=>[Je,...Be]),Ae(Je.name),l("ide");const pt=Ct(_).reduce((Be,qt)=>(Be[qt]=bt(_,qt),Be),{});ge(pt);const wt=Object.keys(pt)[0];wt&&(W([wt]),ne(wt)),console.log("Project created successfully:",we)}else alert("Failed to create project. Please try again.")}catch(D){console.error("Error creating project:",D),alert(`Failed to create project: ${D instanceof Error?D.message:"Unknown error"}`)}}),Ar=()=>{ue&&Object.keys(ye).length>0&&(Dn(ue,ye),console.log(`Project "${ue}" saved successfully!`))},Or=_=>{const N=Ze.find(D=>D.id===_);if(N){Ae(N.name);const D=Pa(N.name);if(D&&Object.keys(D).length>0){ge(D);const L=Object.keys(D)[0];W([L]),ne(L)}else{const L=["contracts/Contract.sol","README.md","package.json"],Q={"contracts/Contract.sol":`// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

contract ${N.name.replace(/\s+/g,"")} {
    // Your contract code here
    string public name = "${N.name}";

    constructor() {
        // Constructor logic
    }
}`,"README.md":`# ${N.name}

Project created on ${new Date(N.created_at).toLocaleDateString()}

## Description
This is a smart contract project.

## Getting Started
1. Install dependencies
2. Compile contracts
3. Deploy to network`,"package.json":JSON.stringify({name:N.name.toLowerCase().replace(/\s+/g,"-"),version:"1.0.0",description:`Smart contract project: ${N.name}`,scripts:{compile:"hardhat compile",test:"hardhat test",deploy:"hardhat run scripts/deploy.js"}},null,2)};ge(Q),W([L[0]]),ne(L[0]),Dn(N.name,Q)}l("ide")}},fi=(_="")=>{if(!ue)return;console.log("createNewFile called with targetPath:",_);const N=prompt("Enter file name (e.g., NewFile.sol):");if(N&&N.trim()){const D=N.trim(),L=_?`${_}/${D}`:D;if(console.log("Creating file at path:",L),ye[L])alert("File already exists!");else{let Q=`// New file
`;D.endsWith(".sol")?Q=`// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

contract ${D.replace(".sol","").replace(/[^a-zA-Z0-9]/g,"")} {
    // Your contract code here
}`:D.endsWith(".js")?Q=`// JavaScript file
console.log("Hello World");`:D.endsWith(".md")&&(Q=`# ${D.replace(".md","")}

Description of this file.`);const he=Ve(se({},ye),{[L]:Q});if(ge(he),ne(L),K.includes(L)||W([...K,L]),ue&&Dn(ue,he),_){console.log("Expanding folder:",_);const we=new Set(Me);we.add(_),z(we)}}}},hi=(_="")=>{if(!ue)return;const N=prompt("Enter folder name:");if(N&&N.trim()){const D=N.trim(),L=_?`${_}/${D}`:D,Q=`${L}/.gitkeep`;if(ye[Q])alert("Folder already exists!");else{const he=Ve(se({},ye),{[Q]:`# This file keeps the folder in version control
`});ge(he);const we=new Set(Me);_&&we.add(_),we.add(L),z(we),ue&&Dn(ue,he)}}},Nr=()=>{const _=document.title;document.title="🔄 Refreshing...",setTimeout(()=>{document.title=_,ge(se({},ye)),console.log("Explorer refreshed successfully")},500)},xo=()=>{if(!ue)return;const _={name:ue,files:ye,timestamp:new Date().toISOString()},N=JSON.stringify(_,null,2),D=new Blob([N],{type:"application/json"}),L=URL.createObjectURL(D),Q=document.createElement("a");Q.href=L,Q.download=`${ue}.json`,document.body.appendChild(Q),Q.click(),document.body.removeChild(Q),URL.revokeObjectURL(L)},kn=()=>{Qe(!Le)},_t=_=>{const N=new Set(Me);N.has(_)?N.delete(_):N.add(_),z(N)},St=(_,N,D)=>{_.preventDefault(),_.stopPropagation(),console.log("Context menu opened for:",{path:N,type:D}),ae({x:_.clientX,y:_.clientY,path:N,type:D}),j(N)},yn=()=>{ae(null),j("")},ds=()=>{console.log("handleNewFileInContext called with contextMenu:",G);let _="";if((G==null?void 0:G.type)==="folder")_=G.path,console.log("Folder context - setting targetPath to:",_);else if((G==null?void 0:G.type)==="file"){const N=G.path;N.includes("/")?_=N.substring(0,N.lastIndexOf("/")):_="",console.log("File context - setting targetPath to:",_)}else _="",console.log("Root context - setting targetPath to:",_);console.log("Final targetPath before calling createNewFile:",_),alert(`Creating file in folder: "${_}" (empty means root)`),fi(_),yn()},ko=()=>{let _="";if((G==null?void 0:G.type)==="folder")_=G.path;else if((G==null?void 0:G.type)==="file"){const N=G.path;N.includes("/")?_=N.substring(0,N.lastIndexOf("/")):_=""}else _="";console.log("Creating folder in path:",_),hi(_),yn()},To=Ze.filter(_=>_.name.toLowerCase().includes(C.toLowerCase())),fs=ut.filter(_=>_.name.toLowerCase().includes(Y.toLowerCase())&&(F==="All"||_.network===F)),Ba=()=>h.jsxs("div",{className:"chain-ide-container",children:[h.jsxs("div",{className:"top-bar",children:[h.jsx("div",{className:"top-bar-left",children:h.jsxs("div",{className:"app-title",children:[h.jsx(Hl,{}),h.jsx("span",{children:"NovaGard"}),h.jsx("button",{className:"back-to-dashboard",onClick:()=>l("dashboard"),children:"← Dashboard"})]})}),h.jsx("div",{className:"top-bar-center",children:h.jsxs("div",{className:"search-container",children:[h.jsx(Bu,{className:"search-icon"}),h.jsx("input",{type:"text",placeholder:"Search Templates",value:Y,onChange:_=>V(_.target.value),className:"search-input"})]})}),h.jsxs("div",{className:"top-bar-right",children:[h.jsxs("div",{className:"wallet-connection",children:[h.jsx($p,{}),g?h.jsxs("div",{className:"wallet-connected-info",children:[h.jsxs("span",{className:"wallet-connected",children:[y==="metamask"?"🦊 MetaMask":"👻 Phantom"," Connected"]}),h.jsx("button",{className:"disconnect-wallet",onClick:ci,children:"Disconnect"})]}):h.jsxs("div",{className:"wallet-options",children:[h.jsx("button",{className:"connect-wallet metamask",onClick:Da,children:"🦊 MetaMask"}),h.jsx("button",{className:"connect-wallet phantom",onClick:ta,children:"👻 Phantom"})]})]}),h.jsx("div",{className:"user-section",children:h.jsx(Ql,{appearance:{elements:{avatarBox:"w-8 h-8",userButtonPopoverCard:"shadow-lg border",userButtonPopoverActionButton:"hover:bg-gray-100"}},showName:!0})})]})]}),h.jsxs("div",{className:"sidebar",children:[h.jsxs("div",{className:"sidebar-icons",children:[h.jsx("button",{className:"sidebar-icon active",title:"Templates",children:h.jsx(Il,{})}),h.jsx("button",{className:"sidebar-icon",title:"Explorer",children:h.jsx(Bu,{})}),h.jsx("button",{className:"sidebar-icon",title:"Extensions",children:h.jsx(Pp,{})})]}),h.jsxs("div",{className:"panel-content",children:[h.jsx("h3",{children:"📁 TEMPLATES"}),h.jsx("div",{className:"templates-sidebar-content",children:h.jsxs("div",{className:"sidebar-section",children:[h.jsx("h4",{children:"Networks"}),h.jsxs("div",{className:"network-list",children:[h.jsxs("button",{className:`network-item ${F==="All"?"active":""}`,onClick:()=>J("All"),children:[h.jsx("span",{className:"network-icon",children:"🌐"}),h.jsx("span",{className:"network-name",children:"All Networks"})]}),nt.map(_=>h.jsxs("button",{className:`network-item ${F===_.name?"active":""}`,onClick:()=>{F===_.name?J("All"):J(_.name)},children:[h.jsx("span",{className:"network-icon",style:{color:_.color},children:_.icon}),h.jsx("span",{className:"network-name",children:_.name})]},_.id))]})]})})]})]}),h.jsx("div",{className:"main-content",children:h.jsx("div",{className:"templates-main-content",children:h.jsxs("div",{className:"templates-section",children:[h.jsxs("div",{className:"section-header",children:[h.jsx("h2",{children:"Public Templates"}),h.jsxs("div",{className:"template-count",children:[fs.length," templates"]})]}),h.jsxs("div",{className:"templates-grid",children:[h.jsx("div",{className:"template-card import-card",children:h.jsxs("div",{className:"template-card-content",children:[h.jsx("div",{className:"import-icon",children:h.jsx("div",{className:"upload-arrow",children:"↑"})}),h.jsxs("div",{className:"template-info",children:[h.jsx("h3",{children:"Import Project"}),h.jsx("p",{children:"Import an existing project"})]})]})}),fs.map(_=>h.jsx("div",{className:"template-card",onClick:N=>{N.preventDefault(),N.stopPropagation(),console.log("Template clicked:",_.name),Eo(_)},style:{cursor:"pointer"},children:h.jsxs("div",{className:"template-card-content",children:[h.jsxs("div",{className:"template-header",children:[h.jsx("div",{className:"template-icon",children:h.jsx(Il,{})}),h.jsx("div",{className:"template-version",children:_.version})]}),h.jsxs("div",{className:"template-info",children:[h.jsx("h3",{children:_.name}),h.jsx("p",{children:_.description})]}),h.jsxs("div",{className:"template-footer",children:[h.jsx("span",{className:"template-category",children:_.category}),h.jsx("span",{className:"template-network",children:_.network})]})]})},_.id))]})]})})})]}),hs=()=>h.jsxs("div",{className:"dashboard-container",children:[h.jsxs("div",{className:"dashboard-header",children:[h.jsx("div",{className:"header-left",children:h.jsxs("div",{className:"logo-section",children:[h.jsx("div",{className:"logo-icon",children:h.jsx(Hl,{})}),h.jsx("span",{className:"logo-text",children:"NovaGard"})]})}),h.jsx("div",{className:"header-center",children:h.jsxs("div",{className:"search-container",children:[h.jsx(Bu,{className:"search-icon"}),h.jsx("input",{type:"text",placeholder:"Search Projects",value:C,onChange:_=>U(_.target.value),className:"search-input"})]})}),h.jsx("div",{className:"header-right",children:h.jsx("div",{className:"user-section",children:h.jsx(Ql,{appearance:{elements:{avatarBox:"w-8 h-8",userButtonPopoverCard:"shadow-lg border",userButtonPopoverActionButton:"hover:bg-gray-100"}},showName:!0})})})]}),h.jsx("div",{className:"dashboard-content",children:h.jsxs("div",{className:"projects-section",children:[h.jsxs("div",{className:"section-header",children:[h.jsx("h2",{children:"All Projects"}),h.jsx("button",{className:"new-project-btn",onClick:za,title:"Create New Project",children:h.jsx(Ip,{})})]}),h.jsxs("div",{className:"projects-grid",children:[h.jsxs("div",{className:"project-card new-project",onClick:za,children:[h.jsx("div",{className:"new-project-icon",children:h.jsx(Ip,{})}),h.jsx("span",{className:"new-project-text",children:"New Project"})]}),h.jsxs("div",{className:"project-card vulnerability-check",onClick:()=>l("vulnerability"),children:[h.jsx("div",{className:"vulnerability-check-icon",children:"🛡️"}),h.jsx("span",{className:"vulnerability-check-text",children:"NovaGard"}),h.jsx("span",{className:"vulnerability-check-subtitle",children:"Check contract vulnerabilities"})]}),To.map(_=>h.jsxs("div",{className:"project-card",onClick:()=>Or(_.id),children:[h.jsx("div",{className:"project-preview",children:h.jsxs("div",{className:"chainide-logo",children:[h.jsx(Hl,{}),h.jsx("span",{children:"NovaGard"})]})}),h.jsxs("div",{className:"project-info",children:[h.jsxs("div",{className:"project-header",children:[h.jsx("span",{className:"project-date",children:new Date(_.created_at).toLocaleDateString()}),h.jsx("button",{className:"project-menu",children:h.jsx(Eb,{})})]}),h.jsxs("div",{className:"project-details",children:[h.jsx(Il,{className:"project-icon"}),h.jsx("span",{className:"project-name",children:_.name})]})]})]},_.id))]})]})})]}),na=()=>{var _,N,D;return h.jsxs("div",{className:"vulnerability-container",children:[h.jsxs("div",{className:"vulnerability-header",children:[h.jsxs("div",{className:"header-left",children:[h.jsxs("div",{className:"logo-section",children:[h.jsx("div",{className:"logo-icon",children:"🛡️"}),h.jsx("span",{className:"logo-text",children:"NovaGard"})]}),h.jsx("button",{className:"back-to-dashboard",onClick:()=>l("dashboard"),children:"← Back to Dashboard"})]}),h.jsx("div",{className:"header-right",children:h.jsx("div",{className:"user-section",children:h.jsx(Ql,{appearance:{elements:{avatarBox:"w-8 h-8",userButtonPopoverCard:"shadow-lg border",userButtonPopoverActionButton:"hover:bg-gray-100"}},showName:!0})})})]}),h.jsx("div",{className:"vulnerability-content",children:h.jsxs("div",{className:"audit-section",children:[h.jsxs("div",{className:"section-header",children:[h.jsx("h2",{children:"Smart Contract Vulnerability Scanner"}),h.jsx("p",{children:"Enter a contract address to perform a comprehensive security audit"})]}),h.jsxs("div",{className:"audit-form",children:[h.jsxs("div",{className:"form-group",children:[h.jsx("label",{htmlFor:"contract-address",children:"Contract Address"}),h.jsx("input",{id:"contract-address",type:"text",placeholder:"0x... (try: 0xA0b86a33E6441E6C7D3E4C5B4B6C7D8E9F0A1B2C3D4E5F6A7B8C9D0E1F2A3B4C5D6E7F8)",value:Z,onChange:L=>re(L.target.value),className:"address-input"}),h.jsx("div",{className:"input-helper",children:h.jsxs("small",{children:["💡 Try this test address:",h.jsx("button",{className:"test-address-btn",onClick:()=>re("0xA0b86a33E6441E6C7D3E4C5B4B6C7D8E9F0A1B2C3D4E5F6A7B8C9D0E1F2A3B4C5D6E7F8"),children:"Use Test Contract"})]})})]}),h.jsxs("div",{className:"form-group",children:[h.jsx("label",{htmlFor:"network-select",children:"Network"}),h.jsxs("select",{id:"network-select",value:ve,onChange:L=>de(L.target.value),className:"network-select",children:[h.jsx("option",{value:"ethereum",children:"Ethereum Mainnet"}),h.jsx("option",{value:"polygon",children:"Polygon"}),h.jsx("option",{value:"aptos",children:"Aptos"}),h.jsx("option",{value:"sui",children:"Sui"}),h.jsx("option",{value:"sol",children:"Solana"})]})]}),h.jsx("button",{className:"scan-button",onClick:La,disabled:Bt||!Z.trim(),children:Bt?h.jsxs(h.Fragment,{children:[h.jsx("div",{className:"spinner"}),Ne==="initializing"&&"Initializing...",Ne==="scanning"&&`Scanning... ${vn}%`,Ne==="completed"&&"Completing..."]}):h.jsx(h.Fragment,{children:"🔍 Start Security Scan"})}),Bt&&h.jsxs("div",{className:"progress-container",children:[h.jsx("div",{className:"progress-bar",children:h.jsx("div",{className:"progress-fill",style:{width:`${vn}%`}})}),h.jsxs("div",{className:"progress-text",children:[Ne," - ",vn,"%"]})]}),rt&&h.jsxs("div",{className:"error-message",children:[h.jsx("span",{className:"error-icon",children:"⚠️"}),rt]})]}),oe&&h.jsxs("div",{className:"results-section",children:[h.jsxs("div",{className:"results-header",children:[h.jsx("h3",{children:"Scan Results"}),h.jsxs("div",{className:"results-header-actions",children:[h.jsxs("div",{className:`risk-badge ${(((_=oe.summary)==null?void 0:_.overallRisk)||oe.overallRisk||"unknown").toLowerCase()}`,children:[((N=oe.summary)==null?void 0:N.overallRisk)||oe.overallRisk||"Unknown"," Risk"]}),h.jsxs("div",{className:"export-buttons",children:[h.jsx("button",{className:"export-btn",onClick:()=>us("json"),title:"Export as JSON",children:"📄 JSON"}),h.jsx("button",{className:"export-btn",onClick:()=>us("pdf"),title:"Export as PDF",children:"📑 PDF"}),h.jsx("button",{className:"export-btn",onClick:()=>us("csv"),title:"Export as CSV",children:"📊 CSV"})]})]})]}),h.jsxs("div",{className:"results-grid",children:[h.jsxs("div",{className:"result-card",children:[h.jsxs("h4",{children:["🚨 Vulnerabilities Found (",(oe.vulnerabilities||[]).length,")"]}),h.jsx("div",{className:"vulnerability-list",children:(oe.vulnerabilities||[]).length>0?oe.vulnerabilities.map((L,Q)=>h.jsxs("div",{className:`vulnerability-item ${L.severity.toLowerCase()}`,children:[h.jsxs("div",{className:"vuln-header",children:[h.jsx("span",{className:"vuln-title",children:L.title}),h.jsx("span",{className:`vuln-severity ${L.severity.toLowerCase()}`,children:L.severity})]}),h.jsx("p",{className:"vuln-description",children:L.description}),h.jsxs("p",{className:"vuln-recommendation",children:[h.jsx("strong",{children:"Recommendation:"})," ",L.recommendation]}),L.line&&h.jsxs("span",{className:"vuln-line",children:["Line: ",L.line]})]},Q)):h.jsxs("div",{className:"no-vulnerabilities",children:[h.jsx("span",{className:"success-icon",children:"✅"}),h.jsx("span",{children:"No vulnerabilities detected!"})]})})]}),h.jsxs("div",{className:"result-card",children:[h.jsxs("h4",{children:["⚡ Gas Optimization (",(oe.gasOptimizations||oe.gasOptimization||[]).length,")"]}),h.jsx("div",{className:"optimization-list",children:(oe.gasOptimizations||oe.gasOptimization||[]).length>0?(oe.gasOptimizations||oe.gasOptimization||[]).map((L,Q)=>h.jsxs("div",{className:"optimization-item",children:[h.jsx("h5",{children:L.title}),h.jsx("p",{children:L.description}),h.jsxs("span",{className:"savings",children:[typeof L.savings=="number"?`${L.savings.toLocaleString()} gas`:L.savings,L.savingsPercentage&&` (${L.savingsPercentage}% savings)`]})]},Q)):h.jsxs("div",{className:"no-optimizations",children:[h.jsx("span",{className:"info-icon",children:"ℹ️"}),h.jsx("span",{children:"No gas optimizations found"})]})})]}),h.jsxs("div",{className:"result-card",children:[h.jsx("h4",{children:"✅ Standard Compliance"}),h.jsx("div",{className:"compliance-list",children:oe.complianceResults&&oe.complianceResults.length>0?oe.complianceResults.map((L,Q)=>h.jsxs("div",{className:"compliance-item",children:[h.jsxs("span",{children:[L.standard,":"]}),h.jsxs("span",{className:L.status==="Compliant"?"compliant":"not-applicable",children:[L.status," (",L.score,"/100)"]})]},Q)):oe.compliance?h.jsxs(h.Fragment,{children:[h.jsxs("div",{className:"compliance-item",children:[h.jsx("span",{children:"ERC-20:"}),h.jsx("span",{className:oe.compliance.erc20==="Compliant"?"compliant":"not-applicable",children:oe.compliance.erc20})]}),h.jsxs("div",{className:"compliance-item",children:[h.jsx("span",{children:"ERC-721:"}),h.jsx("span",{className:oe.compliance.erc721==="Compliant"?"compliant":"not-applicable",children:oe.compliance.erc721})]})]}):h.jsx("div",{className:"compliance-item",children:h.jsx("span",{children:"No compliance data available"})})})]})]}),h.jsxs("div",{className:"scan-info",children:[h.jsxs("p",{children:["Contract: ",oe.contractAddress]}),h.jsxs("p",{children:["Network: ",((D=oe.networkInfo)==null?void 0:D.name)||oe.network||ve]}),h.jsxs("p",{children:["Scan Date: ",new Date(oe.timestamp||oe.scanDate||Date.now()).toLocaleString()]}),oe.scanId&&h.jsxs("p",{children:["Scan ID: ",oe.scanId]}),oe.summary&&h.jsxs(h.Fragment,{children:[h.jsxs("p",{children:["Risk Score: ",oe.summary.riskScore,"/100"]}),h.jsxs("p",{children:["Total Vulnerabilities: ",oe.summary.totalVulnerabilities]}),oe.summary.gasOptimizationSavings>0&&h.jsxs("p",{children:["Potential Gas Savings: ",oe.summary.gasOptimizationSavings.toLocaleString()," gas"]})]})]})]})]})})]})},aa=()=>{switch(f){case"explorer":return h.jsxs("div",{className:"vscode-panel",children:[h.jsx("div",{className:"panel-header",children:h.jsx("span",{className:"panel-title",children:"EXPLORER"})}),h.jsxs("div",{className:"panel-body",children:[ue&&h.jsxs("div",{className:"explorer-section",children:[h.jsxs("div",{className:"section-header-with-toggle",style:{display:"flex",alignItems:"center",gap:"8px"},children:[h.jsx("div",{className:"section-toggle",onClick:kn,title:Le?"Expand Explorer":"Collapse Explorer",style:{cursor:"pointer",fontSize:"18px",color:"#fff",background:"none",border:"none"},children:Le?"▶":"▼"}),h.jsx("span",{className:"section-title",style:{flex:1,color:"#fff",fontWeight:600},children:ue.toUpperCase()}),h.jsxs("div",{className:"section-actions",style:{display:"flex",gap:"4px"},children:[h.jsx("button",{className:"action-btn",title:"New File",onClick:()=>fi(""),style:{background:"none",border:"none",fontSize:"14px",color:"#cccccc",cursor:"pointer",padding:"4px"},children:h.jsx(Bp,{})}),h.jsx("button",{className:"action-btn",title:"New Folder",onClick:()=>hi(""),style:{background:"none",border:"none",fontSize:"14px",color:"#cccccc",cursor:"pointer",padding:"4px"},children:h.jsx(qp,{})}),h.jsx("button",{className:"action-btn",title:"Refresh",onClick:Nr,style:{background:"none",border:"none",fontSize:"14px",color:"#cccccc",cursor:"pointer",padding:"4px"},children:h.jsx(Tb,{})}),h.jsx("button",{className:"action-btn",title:"Download Project",onClick:xo,style:{background:"none",border:"none",fontSize:"14px",color:"#cccccc",cursor:"pointer",padding:"4px"},children:h.jsx(jb,{})})]})]}),!Le&&h.jsx("div",{className:"file-tree",onContextMenu:_=>{_.preventDefault(),_.stopPropagation(),console.log("Root area right-clicked"),St(_,"","root")},onClick:yn,children:(()=>{const _=[];return Object.keys(ye).sort().forEach(D=>{const L=D.split("/");for(let Je=0;Je<L.length-1;Je++){const pt=L.slice(0,Je+1).join("/"),wt=L[Je];_.find(Be=>Be.path===pt&&Be.type==="folder")||_.push({type:"folder",name:wt,path:pt,level:Je,isExpanded:Me.has(pt)})}const Q=L.slice(0,-1).join("/");(L.length===1||Me.has(Q))&&_.push({type:"file",name:L[L.length-1],path:D,level:L.length-1})}),_.map(D=>D.type==="folder"?h.jsxs("div",{className:`folder-header ${xe===D.path?"selected":""}`,style:{paddingLeft:`${20+D.level*16}px`},onClick:L=>{L.stopPropagation(),console.log("Folder clicked:",D.path),_t(D.path)},onContextMenu:L=>{L.preventDefault(),L.stopPropagation(),console.log("Folder right-clicked:",D.path),St(L,D.path,"folder")},children:[h.jsx("span",{className:"folder-toggle",children:D.isExpanded?"▼":"▶"}),h.jsx("span",{className:"folder-icon",children:"📁"}),h.jsx("span",{className:"folder-name",children:D.name})]},`folder-${D.path}`):h.jsxs("div",{className:`file-item ${le===D.path?"active":""} ${xe===D.path?"selected":""}`,style:{paddingLeft:`${32+D.level*16}px`},onClick:()=>{ne(D.path),K.includes(D.path)||W([...K,D.path])},onContextMenu:L=>{L.preventDefault(),L.stopPropagation(),console.log("File right-clicked:",D.path),St(L,D.path,"file")},children:[h.jsx("span",{className:"file-icon",children:"📄"}),h.jsx("span",{className:"file-name",children:D.name})]},`file-${D.path}`))})()})]}),G&&h.jsxs("div",{className:"context-menu",style:{position:"fixed",top:G.y,left:G.x,zIndex:1e3,backgroundColor:"#2d2d30",border:"1px solid #464647",borderRadius:"3px",padding:"4px 0",minWidth:"160px",boxShadow:"0 2px 8px rgba(0,0,0,0.3)"},onClick:_=>_.stopPropagation(),children:[h.jsxs("div",{className:"context-menu-item",onClick:ds,style:{padding:"6px 12px",cursor:"pointer",fontSize:"13px",color:"#cccccc",display:"flex",alignItems:"center",gap:"8px"},onMouseEnter:_=>_.currentTarget.style.backgroundColor="#37373d",onMouseLeave:_=>_.currentTarget.style.backgroundColor="transparent",children:[h.jsx(Bp,{}),h.jsx("span",{children:"New File"})]}),h.jsxs("div",{className:"context-menu-item",onClick:ko,style:{padding:"6px 12px",cursor:"pointer",fontSize:"13px",color:"#cccccc",display:"flex",alignItems:"center",gap:"8px"},onMouseEnter:_=>_.currentTarget.style.backgroundColor="#37373d",onMouseLeave:_=>_.currentTarget.style.backgroundColor="transparent",children:[h.jsx(qp,{}),h.jsx("span",{children:"New Folder"})]})]})]})]});case"plugin":return h.jsxs("div",{className:"vscode-panel",children:[h.jsx("div",{className:"panel-header",children:h.jsx("span",{className:"panel-title",children:"PLUGIN MANAGER"})}),h.jsxs("div",{className:"panel-body",children:[h.jsx("div",{className:"search-box",children:h.jsx("input",{type:"text",placeholder:"Search plugins...",className:"search-input"})}),h.jsxs("div",{className:"plugin-categories",children:[h.jsx("div",{className:"category-item active",children:h.jsx("span",{children:"🔥 Popular"})}),h.jsx("div",{className:"category-item",children:h.jsx("span",{children:"⚡ Installed"})}),h.jsx("div",{className:"category-item",children:h.jsx("span",{children:"🔄 Updates"})})]}),h.jsxs("div",{className:"plugin-list",children:[h.jsxs("div",{className:"plugin-item",children:[h.jsx("div",{className:"plugin-icon",children:"🔧"}),h.jsxs("div",{className:"plugin-details",children:[h.jsx("div",{className:"plugin-name",children:"Solidity Support"}),h.jsx("div",{className:"plugin-description",children:"Syntax highlighting for Solidity"}),h.jsxs("div",{className:"plugin-meta",children:[h.jsx("span",{className:"plugin-author",children:"Microsoft"}),h.jsx("span",{className:"plugin-downloads",children:"2.1M"})]})]}),h.jsx("button",{className:"plugin-action installed",children:"✓"})]}),h.jsxs("div",{className:"plugin-item",children:[h.jsx("div",{className:"plugin-icon",children:"⚡"}),h.jsxs("div",{className:"plugin-details",children:[h.jsx("div",{className:"plugin-name",children:"Hardhat Integration"}),h.jsx("div",{className:"plugin-description",children:"Smart contract development"}),h.jsxs("div",{className:"plugin-meta",children:[h.jsx("span",{className:"plugin-author",children:"Hardhat"}),h.jsx("span",{className:"plugin-downloads",children:"850K"})]})]}),h.jsx("button",{className:"plugin-action installed",children:"✓"})]}),h.jsxs("div",{className:"plugin-item",children:[h.jsx("div",{className:"plugin-icon",children:"🌐"}),h.jsxs("div",{className:"plugin-details",children:[h.jsx("div",{className:"plugin-name",children:"Web3 Provider"}),h.jsx("div",{className:"plugin-description",children:"Blockchain connectivity"}),h.jsxs("div",{className:"plugin-meta",children:[h.jsx("span",{className:"plugin-author",children:"Web3"}),h.jsx("span",{className:"plugin-downloads",children:"1.2M"})]})]}),h.jsx("button",{className:"plugin-action update",children:"Update"})]})]})]})]});case"port":return h.jsxs("div",{className:"vscode-panel",children:[h.jsxs("div",{className:"panel-header",children:[h.jsx("span",{className:"panel-title",children:"PORT MANAGER"}),h.jsx("button",{className:"header-action",title:"Add Port",children:"+"})]}),h.jsx("div",{className:"panel-body",children:h.jsxs("div",{className:"port-list",children:[h.jsxs("div",{className:"port-item",children:[h.jsxs("div",{className:"port-info",children:[h.jsx("div",{className:"port-number",children:"3000"}),h.jsx("div",{className:"port-description",children:"React Development Server"})]}),h.jsxs("div",{className:"port-status running",children:[h.jsx("span",{className:"status-dot"}),h.jsx("span",{children:"Running"})]}),h.jsxs("div",{className:"port-actions",children:[h.jsx("button",{className:"action-btn",title:"Open in Browser",children:"🌐"}),h.jsx("button",{className:"action-btn",title:"Stop",children:"⏹️"})]})]}),h.jsxs("div",{className:"port-item",children:[h.jsxs("div",{className:"port-info",children:[h.jsx("div",{className:"port-number",children:"8545"}),h.jsx("div",{className:"port-description",children:"Ganache Blockchain"})]}),h.jsxs("div",{className:"port-status running",children:[h.jsx("span",{className:"status-dot"}),h.jsx("span",{children:"Running"})]}),h.jsxs("div",{className:"port-actions",children:[h.jsx("button",{className:"action-btn",title:"Open in Browser",children:"🌐"}),h.jsx("button",{className:"action-btn",title:"Stop",children:"⏹️"})]})]}),h.jsxs("div",{className:"port-item",children:[h.jsxs("div",{className:"port-info",children:[h.jsx("div",{className:"port-number",children:"8080"}),h.jsx("div",{className:"port-description",children:"HTTP Server"})]}),h.jsxs("div",{className:"port-status stopped",children:[h.jsx("span",{className:"status-dot"}),h.jsx("span",{children:"Stopped"})]}),h.jsxs("div",{className:"port-actions",children:[h.jsx("button",{className:"action-btn",title:"Start",children:"▶️"}),h.jsx("button",{className:"action-btn",title:"Delete",children:"🗑️"})]})]})]})})]});case"sandbox":return h.jsxs("div",{className:"vscode-panel",children:[h.jsxs("div",{className:"panel-header",children:[h.jsx("span",{className:"panel-title",children:"SANDBOX MANAGEMENT"}),h.jsx("button",{className:"header-action",title:"New Sandbox",children:"+"})]}),h.jsx("div",{className:"panel-body",children:h.jsxs("div",{className:"sandbox-list",children:[h.jsxs("div",{className:"sandbox-item active",children:[h.jsx("div",{className:"sandbox-icon",children:"🟢"}),h.jsxs("div",{className:"sandbox-details",children:[h.jsx("div",{className:"sandbox-name",children:"Development"}),h.jsx("div",{className:"sandbox-description",children:"Local development environment"}),h.jsxs("div",{className:"sandbox-meta",children:[h.jsx("span",{children:"Node.js 18.x"}),h.jsx("span",{children:"•"}),h.jsx("span",{children:"Active"})]})]}),h.jsxs("div",{className:"sandbox-actions",children:[h.jsx("button",{className:"action-btn",title:"Terminal",children:"💻"}),h.jsx("button",{className:"action-btn",title:"Settings",children:"⚙️"})]})]}),h.jsxs("div",{className:"sandbox-item",children:[h.jsx("div",{className:"sandbox-icon",children:"🟡"}),h.jsxs("div",{className:"sandbox-details",children:[h.jsx("div",{className:"sandbox-name",children:"Testing"}),h.jsx("div",{className:"sandbox-description",children:"Testing environment"}),h.jsxs("div",{className:"sandbox-meta",children:[h.jsx("span",{children:"Node.js 16.x"}),h.jsx("span",{children:"•"}),h.jsx("span",{children:"Idle"})]})]}),h.jsxs("div",{className:"sandbox-actions",children:[h.jsx("button",{className:"action-btn",title:"Start",children:"▶️"}),h.jsx("button",{className:"action-btn",title:"Settings",children:"⚙️"})]})]}),h.jsxs("div",{className:"sandbox-item",children:[h.jsx("div",{className:"sandbox-icon",children:"🔴"}),h.jsxs("div",{className:"sandbox-details",children:[h.jsx("div",{className:"sandbox-name",children:"Production"}),h.jsx("div",{className:"sandbox-description",children:"Production environment"}),h.jsxs("div",{className:"sandbox-meta",children:[h.jsx("span",{children:"Node.js 18.x"}),h.jsx("span",{children:"•"}),h.jsx("span",{children:"Stopped"})]})]}),h.jsxs("div",{className:"sandbox-actions",children:[h.jsx("button",{className:"action-btn",title:"Start",children:"▶️"}),h.jsx("button",{className:"action-btn",title:"Settings",children:"⚙️"})]})]})]})})]});case"git":return h.jsxs("div",{className:"vscode-panel",children:[h.jsxs("div",{className:"panel-header",children:[h.jsx("span",{className:"panel-title",children:"GIT MANAGER"}),h.jsx("button",{className:"header-action",title:"Refresh",children:"🔄"})]}),h.jsx("div",{className:"panel-body",children:h.jsxs("div",{className:"git-section",children:[h.jsx("div",{className:"git-branch",children:h.jsxs("div",{className:"branch-info",children:[h.jsx("span",{className:"branch-icon",children:"🌿"}),h.jsx("span",{className:"branch-name",children:"main"}),h.jsx("button",{className:"branch-action",title:"Switch Branch",children:"⇄"})]})}),h.jsxs("div",{className:"git-changes",children:[h.jsxs("div",{className:"changes-header",children:[h.jsx("span",{children:"Changes (2)"}),h.jsxs("div",{className:"changes-actions",children:[h.jsx("button",{className:"action-btn",title:"Stage All",children:"+"}),h.jsx("button",{className:"action-btn",title:"Discard All",children:"↶"})]})]}),h.jsxs("div",{className:"changes-list",children:[h.jsxs("div",{className:"change-item",children:[h.jsx("span",{className:"change-status modified",children:"M"}),h.jsx("span",{className:"change-file",children:"contracts/MyContract.sol"}),h.jsxs("div",{className:"change-actions",children:[h.jsx("button",{className:"action-btn",title:"Stage",children:"+"}),h.jsx("button",{className:"action-btn",title:"Discard",children:"↶"})]})]}),h.jsxs("div",{className:"change-item",children:[h.jsx("span",{className:"change-status added",children:"A"}),h.jsx("span",{className:"change-file",children:"README.md"}),h.jsxs("div",{className:"change-actions",children:[h.jsx("button",{className:"action-btn",title:"Stage",children:"+"}),h.jsx("button",{className:"action-btn",title:"Discard",children:"↶"})]})]})]})]}),h.jsxs("div",{className:"git-commit",children:[h.jsx("textarea",{className:"commit-message",placeholder:"Commit message...",rows:3}),h.jsx("button",{className:"commit-btn",children:"Commit"})]})]})})]});case"compiler":return h.jsxs("div",{className:"vscode-panel",children:[h.jsxs("div",{className:"panel-header",children:[h.jsx("span",{className:"panel-title",children:"COMPILER"}),h.jsx("button",{className:"header-action",title:"Refresh",children:"🔄"})]}),h.jsx("div",{className:"panel-body",children:h.jsxs("div",{className:"compiler-section",children:[h.jsxs("div",{className:"compiler-config",children:[h.jsx("div",{className:"config-header",children:h.jsx("span",{children:"Solidity Compiler"})}),h.jsxs("div",{className:"config-options",children:[h.jsxs("div",{className:"config-item",children:[h.jsx("label",{children:"Version:"}),h.jsxs("select",{className:"config-select",children:[h.jsx("option",{children:"0.8.19"}),h.jsx("option",{children:"0.8.18"}),h.jsx("option",{children:"0.8.17"})]})]}),h.jsxs("div",{className:"config-item",children:[h.jsx("label",{children:"Optimization:"}),h.jsx("input",{type:"checkbox",defaultChecked:!0}),h.jsx("span",{children:"Enabled (200 runs)"})]})]})]}),h.jsxs("div",{className:"compiler-actions",children:[h.jsx("button",{className:"compile-btn primary",onClick:()=>{ue?u.compileContract(`// Sample contract code
pragma solidity ^0.8.0;

contract ${ue} {
    // Contract implementation
}`,ue):u.addLog("warning","No project selected for compilation","compiler")},children:"🔨 Compile All"}),h.jsx("button",{className:"compile-btn secondary",onClick:()=>u.addLog("info","Clean build initiated...","compiler"),children:"🧹 Clean"})]}),h.jsxs("div",{className:"compiler-output",children:[h.jsx("div",{className:"output-header",children:h.jsx("span",{children:"Compilation Results"})}),h.jsxs("div",{className:"output-content",children:[h.jsx("div",{className:"output-line success",children:"✓ Ready to compile"}),h.jsx("div",{className:"output-line info",children:'Select a contract and click "Compile All"'})]})]})]})})]});case"deploy":return h.jsxs("div",{className:"vscode-panel",children:[h.jsxs("div",{className:"panel-header",children:[h.jsx("span",{className:"panel-title",children:"DEPLOY"}),h.jsx("button",{className:"header-action",title:"Refresh",children:"🔄"})]}),h.jsx("div",{className:"panel-body",children:h.jsxs("div",{className:"deploy-section",children:[h.jsxs("div",{className:"deploy-config",children:[h.jsx("div",{className:"config-header",children:h.jsx("span",{children:"Deployment Configuration"})}),h.jsxs("div",{className:"config-options",children:[h.jsxs("div",{className:"config-item",children:[h.jsx("label",{children:"Network:"}),h.jsxs("select",{className:"config-select",children:[h.jsx("option",{children:"Ethereum Mainnet"}),h.jsx("option",{children:"Polygon"}),h.jsx("option",{children:"Arbitrum"}),h.jsx("option",{children:"Local Testnet"})]})]}),h.jsxs("div",{className:"config-item",children:[h.jsx("label",{children:"Gas Limit:"}),h.jsx("input",{type:"number",defaultValue:"3000000",className:"config-input"})]})]})]}),h.jsxs("div",{className:"deploy-actions",children:[h.jsx("button",{className:"deploy-btn primary",onClick:()=>{u.addLog("info","Starting deployment process...","deploy"),u.addLog("info","Estimating gas costs...","deploy"),setTimeout(()=>{u.addLog("success","✓ Contract deployed successfully!","deploy"),u.addLog("info","Contract address: 0x1234...5678","deploy")},2e3)},children:"🚀 Deploy Contract"}),h.jsx("button",{className:"deploy-btn secondary",onClick:()=>u.addLog("info","Estimating deployment costs...","deploy"),children:"💰 Estimate Gas"})]})]})})]});default:return h.jsxs("div",{className:"vscode-panel",children:[h.jsx("div",{className:"panel-header",children:h.jsx("span",{className:"panel-title",children:"EXPLORER"})}),h.jsx("div",{className:"panel-body",children:h.jsx("div",{className:"empty-state",children:h.jsx("p",{children:"Select a panel from the sidebar"})})})]})}},qa=()=>r==="dashboard"?hs():r==="templates"?Ba():r==="vulnerability"?na():h.jsxs("div",{className:"chain-ide-container",onClick:yn,children:[h.jsxs("div",{className:"top-bar",children:[h.jsx("div",{className:"top-bar-left",children:h.jsxs("div",{className:"app-title",children:[h.jsx(Hl,{}),h.jsx("span",{children:"FlashAudit"}),h.jsx("button",{className:"back-to-dashboard",onClick:()=>l("dashboard"),children:"← Dashboard"})]})}),h.jsx("div",{className:"top-bar-center",children:h.jsxs("div",{className:"network-selector",children:[h.jsx(Hp,{}),h.jsx("span",{children:x})]})}),h.jsxs("div",{className:"top-bar-right",children:[h.jsxs("div",{className:"wallet-connection",children:[h.jsx($p,{}),g?h.jsx("span",{className:"wallet-connected",children:"Connected"}):h.jsx("button",{className:"connect-wallet",onClick:Rt,children:"Connect Wallet"})]}),h.jsx("div",{className:"user-section",children:h.jsx(Ql,{appearance:{elements:{avatarBox:"w-8 h-8",userButtonPopoverCard:"shadow-lg border",userButtonPopoverActionButton:"hover:bg-gray-100"}},showName:!0})})]})]}),h.jsxs("div",{className:"sidebar",children:[h.jsxs("div",{className:"sidebar-icons",children:[h.jsx("button",{className:`sidebar-icon ${f==="explorer"?"active":""}`,onClick:()=>m("explorer"),title:"File Explorer",children:h.jsx(Il,{})}),h.jsx("button",{className:`sidebar-icon ${f==="plugin"?"active":""}`,onClick:()=>m("plugin"),title:"Plugin Manager",children:h.jsx(Pp,{})}),h.jsx("button",{className:`sidebar-icon ${f==="port"?"active":""}`,onClick:()=>m("port"),title:"Port Manager",children:h.jsx(Hp,{})}),h.jsx("button",{className:`sidebar-icon ${f==="sandbox"?"active":""}`,onClick:()=>m("sandbox"),title:"Sandbox Management",children:h.jsx(kb,{})}),h.jsx("button",{className:`sidebar-icon ${f==="git"?"active":""}`,onClick:()=>m("git"),title:"Git Manager",children:h.jsx(xb,{})})]}),h.jsx("div",{className:"panel-content",children:aa()})]}),h.jsx("div",{className:"main-content",children:h.jsx("div",{className:"editor-container",children:ue&&le?h.jsxs("div",{className:"file-editor",children:[h.jsx("div",{className:"file-tabs",children:K.map(_=>h.jsxs("div",{className:`file-tab ${le===_?"active":""}`,onClick:()=>ne(_),children:[h.jsx("span",{children:_.split("/").pop()}),h.jsx("button",{className:"close-tab",onClick:N=>{N.stopPropagation();const D=K.filter(L=>L!==_);W(D),le===_&&D.length>0?ne(D[0]):D.length===0&&ne(null)},children:"×"})]},_))}),h.jsxs("div",{className:"file-content",children:[h.jsxs("div",{className:"file-header",children:[h.jsx("span",{className:"file-path",children:le}),h.jsx("div",{className:"file-actions",children:h.jsx("button",{className:"save-btn",title:"Save File",onClick:Ar,children:"💾 Save"})})]}),h.jsxs("div",{className:"code-editor-container",children:[h.jsx("div",{className:"line-numbers",children:(()=>{const N=(ye[le]||"").split(`
`),D=Math.max(N.length,1);return Array.from({length:D},(L,Q)=>h.jsx("div",{className:`line-number ${I===Q+1?"current-line":""}`,children:Q+1},Q))})()}),h.jsx("textarea",{className:"code-editor",value:ye[le]||"",onChange:_=>{const N=Ve(se({},ye),{[le]:_.target.value});ge(N),ue&&(clearTimeout(window.autoSaveTimeout),window.autoSaveTimeout=setTimeout(()=>{Dn(ue,N)},1e3))},onSelect:_=>{const N=_.target,D=N.selectionStart,Q=N.value.substring(0,D).split(`
`).length;X(Q)},onClick:_=>{const N=_.target,D=N.selectionStart,Q=N.value.substring(0,D).split(`
`).length;X(Q)},onKeyUp:_=>{const N=_.target,D=N.selectionStart,Q=N.value.substring(0,D).split(`
`).length;X(Q)},placeholder:"Start coding...",spellCheck:!1,onScroll:_=>{var D;const N=(D=_.currentTarget.parentElement)==null?void 0:D.querySelector(".line-numbers");N&&(N.scrollTop=_.currentTarget.scrollTop)}})]})]})]}):ue?h.jsxs("div",{className:"no-file-selected",children:[h.jsxs("h3",{children:["📁 ",ue]}),h.jsx("p",{children:"Select a file from the explorer to start editing"}),h.jsxs("div",{className:"project-actions",children:[h.jsx("button",{className:"action-btn",onClick:()=>m("compiler"),children:"⚙️ Compile Project"}),h.jsx("button",{className:"action-btn",onClick:()=>m("deploy"),children:"🚀 Deploy Contract"}),h.jsx("button",{className:"action-btn",onClick:()=>m("audit"),children:"🛡️ Run Security Audit"})]})]}):h.jsxs("div",{className:"welcome-message",children:[h.jsx("h2",{children:"🎉 Welcome to NovaGard!"}),h.jsx("p",{children:"Your professional smart contract development environment is ready."}),h.jsxs("div",{className:"features-list",children:[h.jsx("div",{className:"feature-item",children:"✅ Multi-network support"}),h.jsx("div",{className:"feature-item",children:"✅ Advanced security auditing"}),h.jsx("div",{className:"feature-item",children:"✅ Real-time compilation"}),h.jsx("div",{className:"feature-item",children:"✅ One-click deployment"}),h.jsx("div",{className:"feature-item",children:"✅ Audit statistics & analytics"})]}),h.jsx("p",{className:"get-started",children:"👈 Use the sidebar to explore different features!"})]})})}),h.jsx("div",{className:"bottom-panel",children:h.jsxs("div",{className:"terminal-content",children:[h.jsxs("div",{className:"terminal-header",children:[h.jsx("span",{children:"📟 Output"}),h.jsxs("div",{className:"terminal-actions",children:[h.jsx("button",{className:"terminal-action-btn",onClick:()=>u.getServiceStatus(),title:"Check Service Status",children:"🔍 Status"}),h.jsx("button",{className:"terminal-action-btn",onClick:()=>u.startDevelopmentSession(),title:"Start Development Session",children:"🚀 Start Session"}),h.jsx("button",{className:"terminal-action-btn",onClick:()=>u.clearLogs(),title:"Clear Terminal",children:"🗑️ Clear"})]})]}),h.jsx("div",{className:"terminal-output",children:q.map((_,N)=>h.jsxs("div",{className:`terminal-line terminal-${_.level}`,children:[h.jsxs("span",{className:"terminal-timestamp",children:["[",_.timestamp,"]"]}),_.source&&h.jsxs("span",{className:"terminal-source",children:["[",_.source,"]"]}),h.jsx("span",{className:"terminal-message",children:_.message})]},N))})]})})]});return h.jsx(h.Fragment,{children:qa()})}const so="pk_test_ZnVua3ktcGFuZGEtNDYuY2xlcmsuYWNjb3VudHMuZGV2JA";console.log("Environment check:",{NODE_ENV:"production",CLERK_KEY:"Present",CLERK_KEY_LENGTH:(so==null?void 0:so.length)||0});pb.createRoot(document.getElementById("root")).render(h.jsx(P.StrictMode,{children:h.jsx(Av,{publishableKey:so,afterSignOutUrl:"/",appearance:{baseTheme:void 0,variables:{colorPrimary:"#007acc",colorBackground:"#ffffff",colorInputBackground:"#ffffff",colorInputText:"#000000",colorText:"#000000",colorTextSecondary:"#666666",colorShimmer:"#f5f5f5",borderRadius:"8px"},elements:{formButtonPrimary:"bg-blue-600 hover:bg-blue-700 text-white",card:"bg-white border border-gray-300 shadow-lg",headerTitle:"text-black",headerSubtitle:"text-gray-600",socialButtonsBlockButton:"border-gray-300 hover:bg-gray-50 text-black",formFieldInput:"bg-white border-gray-300 text-black",footerActionLink:"text-blue-600 hover:text-blue-700"}},children:h.jsx(g2,{})})}))});export default v2();
